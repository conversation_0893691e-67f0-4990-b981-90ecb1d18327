import { useTranslation } from "react-i18next";
import Footer from "@/components/Footer/Footer";
import PublicHeader from "@/components/PublicHeader";

export default function AboutPage() {
  const { t } = useTranslation();

  const values = t("aboutPage.ourValues.values", { returnObjects: true });
  const teamMembers = t("aboutPage.ourTeam.members", { returnObjects: true });

  return (
    <div className="bg-purple-50 min-h-screen cursor-default">
      <PublicHeader />
      <div className="container mx-auto px-4 py-12 max-w-5xl">
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            {t("aboutPage.title")}
          </h1>
          <p className="text-gray-600">{t("aboutPage.subtitle")}</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-20">
          <div className="bg-purple-100 p-10">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {t("aboutPage.ourMission.title")}
            </h2>
            <p className="text-gray-600 mb-4">{t("aboutPage.ourMission.p1")}</p>
            <p className="text-gray-600">{t("aboutPage.ourMission.p2")}</p>
          </div>

          <div className="bg-purple-100 p-10">
            <h2 className="text-2xl font-bold text-gray-900 pb-10">
              {t("aboutPage.ourValues.title")}
            </h2>
            <div className="space-y-6">
              {(
                values as Array<{
                  number: string;
                  title: string;
                  description: string;
                }>
              ).map((value, index) => (
                <div key={index} className="flex">
                  <div className="mr-4">
                    <div className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold">
                      {value.number}
                    </div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {value.title}
                    </h3>
                    <p className="text-gray-600">{value.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-20">
          <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
            {t("aboutPage.ourTeam.title")}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {(
              teamMembers as Array<{
                name: string;
                role: string;
                description: string;
                initials: string;
              }>
            ).map((member, index) => (
              <div
                key={index}
                className="bg-gray-100 rounded-lg overflow-hidden shadow-sm"
              >
                <div className="p-8 flex justify-center">
                  <div className="bg-gray-300 rounded-full w-20 h-20 flex items-center justify-center">
                    <span className="text-gray-600 text-xl font-medium">
                      {member.initials}
                    </span>
                  </div>
                </div>
                <div className="bg-white p-6">
                  <h3 className="font-bold text-gray-900">{member.name}</h3>
                  <p className="text-purple-500 text-sm mb-2 font-semibold">
                    {member.role}
                  </p>
                  <p className="text-gray-600 text-sm">{member.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
}
