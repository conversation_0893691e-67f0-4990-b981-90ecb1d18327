import { useState, useEffect } from "react";
import StatCard from "../../components/StatCard";
import {
  Users,
  Calendar,
  File,
  Clock,
  BookOpen,
  CheckCircle,
  Shuffle,
} from "lucide-react";
import TopicCard from "../../components/TopicCard";
import ScheduleDashCard from "../../components/ScheduleDashCard/ScheduleDashCard";
import SubjectCard from "../../components/SubjectCard";
import { useTranslation } from "react-i18next";
import FormattedDate from "@/components/FormattedDate";
import RecordAttendanceDialog from "../../components/RecordAttendanceDialog";
import { Button } from "@/components/ui/button";
import LuckyWheelDialog from "../../components/LuckyWheelDialog";
import toast from "react-hot-toast";
import { getAllSubjects } from "../../api/subjects";

// Add this interface to match your subject structure
interface DashboardSubject {
  id: string;
  name: string;
  teacher: string;
  color: string;
  description: string;
}

export default function TeacherDashboard() {
  const { t } = useTranslation();
  const [showAllSubjects, setShowAllSubjects] = useState(false);
  const [showAttendanceDialog, setShowAttendanceDialog] = useState(false);
  const [selectedClass, setSelectedClass] = useState("");
  const [showLuckyWheelDialog, setShowLuckyWheelDialog] = useState(false);
  const [subjects, setSubjects] = useState<DashboardSubject[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSubjects = async () => {
      try {
        const data = await getAllSubjects();
        const formattedSubjects = data.map((subject: any) => ({
          id: subject.id,
          name: subject.name,
          teacher: "You", // Assuming the logged-in teacher
          color: `bg-[${subject.color}]`, // Color already has # in it
          description: subject.description,
        }));
        setSubjects(formattedSubjects);
      } catch (error) {
        console.error("Error fetching subjects:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubjects();
  }, []);

  const visibleSubjects = showAllSubjects ? subjects : subjects.slice(0, 4);

  const currentDay = new Date()
    .toLocaleDateString("en-US", { weekday: "long" })
    .toLowerCase();

  // Format today's date as Apr 10, 2024
  const today = new Date();
  const formattedDate = today.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  // Today's classes with topics
  const todaysClasses = [
    {
      id: "math101",
      name: "Mathematics",
      time: "8:00 - 9:30",
      room: "Room 101",
      topic: "Quadratic Equations",
    },
    {
      id: "phys101",
      name: "Physics",
      time: "9:45 - 11:15",
      room: "Room 205",
      topic: "Newton's Laws of Motion",
    },
    {
      id: "bio101",
      name: "Biology",
      time: "12:00 - 13:30",
      room: "Room 302",
      topic: "Cell Structure and Function",
    },
  ];

  const handleRecordAttendance = (classId: string, className: string) => {
    const selectedClassData = todaysClasses.find((cls) => cls.id === classId);
    setSelectedClass(className);
    setShowAttendanceDialog(true);
    // Pass the topic to the dialog if needed
  };

  const handleAttendanceSubmit = (attendanceData: any) => {
    console.log("Attendance data:", attendanceData);
    setShowAttendanceDialog(false);
    toast.success(t("dashboardMessages.attendanceRecorded"));
    // Here you would typically make an API call to save the attendance
  };

  // Add this mock data for students (you can replace with real data later)
  const classStudents = [
    { id: "1", name: "Emma Johnson" },
    { id: "2", name: "Noah Smith" },
    { id: "3", name: "Olivia Williams" },
    { id: "4", name: "Liam Brown" },
    { id: "5", name: "Ava Jones" },
    { id: "6", name: "William Davis" },
    { id: "7", name: "Sophia Miller" },
    { id: "8", name: "James Wilson" },
  ];

  return (
    <main>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">
          {t("dashboard.title")}
        </h1>
        <p className="text-gray-500">
          <FormattedDate />
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard
          title={t("dashboard.totalStudents")}
          value="127"
          description={t("dashboard.totalStudentsDesc")}
          icon={<Users className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboard.classesToday")}
          value="3"
          description={t(`days.${currentDay}`)}
          icon={<Calendar className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboard.pendingAssignments")}
          value="24"
          description={t("dashboard.pendingAssignmentsDesc")}
          icon={<File className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboard.absencesToday")}
          value="3"
          description={t("dashboard.absencesTodayDesc")}
          icon={<Clock className="h-5 w-5" />}
        />
      </div>
      {/* Quick Attendance Section */}
      <div className="mb-6 p-4 sm:p-6 border border-gray-300 bg-white rounded-md">
        <div className="flex items-center gap-2 mb-3 sm:mb-4">
          <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
          <h2 className="text-lg sm:text-xl font-bold">
            {t("dashboard.quickAttendance")}
          </h2>
        </div>
        <p className="text-sm sm:text-base text-gray-500 mb-3 sm:mb-4">
          {t("dashboard.quickAttendanceDesc")}
        </p>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
          {todaysClasses.map((cls) => (
            <div
              key={cls.id}
              className="border border-gray-200 rounded-md p-3 sm:p-4 hover:border-purple-300 transition-colors"
            >
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2 sm:mb-2">
                <div>
                  <h3 className="font-bold text-sm sm:text-base">{cls.name}</h3>
                  <p className="text-xs sm:text-sm text-gray-500">{cls.time}</p>
                  <p className="text-xs sm:text-sm text-gray-500">{cls.room}</p>
                  {cls.topic && (
                    <p className="text-xs sm:text-sm text-purple-600 mt-1">
                      <span className="font-medium">
                        {t("dashboard.topic")}:
                      </span>{" "}
                      {cls.topic}
                    </p>
                  )}
                </div>
                <Button
                  size="sm"
                  className="bg-purple-600 hover:bg-purple-700 text-white text-xs sm:text-sm w-full sm:w-auto mt-2 sm:mt-0"
                  onClick={() => handleRecordAttendance(cls.id, cls.name)}
                >
                  {t("dashboard.takeAttendance")}
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Attendance Dialog */}
      <RecordAttendanceDialog
        isOpen={showAttendanceDialog}
        onClose={() => setShowAttendanceDialog(false)}
        onSubmit={handleAttendanceSubmit}
        subject={selectedClass}
        date={formattedDate}
      />

      <div className="grid grid-cols-1 lg:grid-cols-1 gap-4 sm:gap-6">
        <div className="mb-6 p-4 sm:p-6 border border-gray-300 bg-white rounded-md">
          <div className="flex items-center gap-2 mb-3 sm:mb-4">
            <Shuffle className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600" />
            <h2 className="text-lg sm:text-xl font-bold">
              {t("luckyWheel.randomStudent")}
            </h2>
          </div>
          <p className="text-sm sm:text-base text-gray-500 mb-3 sm:mb-4">
            {t("luckyWheel.randomStudentDesc")}
          </p>
          <Button
            className="bg-purple-600 hover:bg-purple-700 text-white"
            onClick={() => setShowLuckyWheelDialog(true)}
          >
            {t("luckyWheel.openLuckyWheel")}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 sm:gap-6">
        <div className="col-span-1 lg:col-span-4 p-6 border border-gray-300 bg-white rounded-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <h2 className="text-xl font-bold">
                {t("dashboard.upcomingTopics")}
              </h2>
            </div>
          </div>

          <div className="space-y-4">
            <TopicCard
              title="Newton's Laws of Motion"
              subject="Physics"
              date="4/7/2025"
              description="Understanding the fundamental principles of classical mechanics."
              color="green"
            />
            <TopicCard
              title="Quadratic Equations"
              subject="Mathematics"
              date="4/6/2025"
              description="Solving quadratic equations using various methods."
              color="blue"
            />
            <TopicCard
              title="Naim Frashëri: Life and Works"
              subject="Literature"
              date="4/5/2025"
              description="An overview of the life and literary works of Naim Frashëri, a key figure in Albanian literature."
              color="indigo"
            />
          </div>
        </div>

        {/* Today's Schedule */}
        <div className="col-span-1 lg:col-span-3 p-6 border border-gray-300 bg-white rounded-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <h2 className="text-xl font-bold">
                {t("dashboard.todaysSchedule")}
              </h2>
            </div>
            <span className="text-gray-500">{t(`days.${currentDay}`)}</span>
          </div>

          <div className="space-y-4">
            <ScheduleDashCard
              subject="Mathematics"
              time="8:00 - 9:30"
              room="Room 101"
              color="blue"
            />
            <ScheduleDashCard
              subject="Physics"
              time="9:45 - 11:15"
              room="Room 205"
              color="green"
            />
            <ScheduleDashCard
              subject="Biology"
              time="12:00 - 13:30"
              room="Room 302"
              color="yellow"
            />
          </div>
        </div>
      </div>

      <div className="py-10">
        <p className="pb-5 font-semibold text-lg">
          {t("dashboard.yourSubjects")}
        </p>
        {loading ? (
          <div className="text-center py-4">Loading subjects...</div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 ">
              {visibleSubjects.map((subject) => (
                <SubjectCard
                  key={subject.id as string}
                  subject={{
                    ...subject,
                    id: parseInt(subject.id) || 0,
                  }}
                  isTeacher
                />
              ))}
            </div>
            {subjects.length > 4 && (
              <div className="flex justify-center mt-4">
                <button
                  onClick={() => setShowAllSubjects(!showAllSubjects)}
                  className="text-sm font-medium px-4 py-2 rounded-md border border-gray-300 hover:bg-slate-100 bg-white"
                >
                  {showAllSubjects
                    ? t("dashboard.showLess")
                    : t("dashboard.viewAllSubjects")}
                </button>
              </div>
            )}
          </>
        )}
      </div>
      <LuckyWheelDialog
        isOpen={showLuckyWheelDialog}
        onClose={() => setShowLuckyWheelDialog(false)}
        students={classStudents}
      />
    </main>
  );
}
