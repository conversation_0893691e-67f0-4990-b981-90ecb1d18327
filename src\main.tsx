import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import AuthProvider from "react-auth-kit";
import createStore from "react-auth-kit/createStore";
import { User } from "./types";
import "./index.css";
import App from "./App.tsx";
import "./i18n";

const store = createStore<User>({
  authName: "_auth",
  authType: "cookie",
  cookieDomain: window.location.hostname,
  cookieSecure: window.location.protocol === "https:",
  refresh: {
    interval: 10, // Refresh token 10 minutes before expiry
    refreshApiCallback: async (param: {
      authToken?: string;
      refreshToken?: string;
      authUserState: User | null;
    }) => {
      try {
        if (!param.refreshToken) {
          throw new Error("No refresh token available");
        }

        const response = await fetch(
          `${import.meta.env.VITE_EDITAR_BASE_API_URL}/auth/refresh`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              refresh_token: param.refreshToken,
            }),
          }
        );

        if (!response.ok) {
          throw new Error("Token refresh failed");
        }

        const data = await response.json();
        return {
          isSuccess: true,
          newAuthToken: data.access_token,
          newAuthTokenExpireIn: data.expires_in,
          newRefreshTokenExpiresIn: data.expires_in,
          newAuthUserState: param.authUserState,
        };
      } catch (error) {
        console.error("Token refresh error:", error);
        return {
          isSuccess: false,
          newAuthToken: "",
          newAuthTokenExpireIn: 0,
          newRefreshTokenExpiresIn: 0,
          newAuthUserState: param.authUserState,
        };
      }
    },
  },
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AuthProvider store={store}>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </AuthProvider>
  </StrictMode>
);
