import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Users, Plus, Trash2, Save, Edit, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  validateEmail,
  validateRequired,
  isValidPhoneCharacter,
  validatePhoneFormat,
  validateUniqueStaffId,
} from "../../../lib/validation";
import toast from "react-hot-toast";
import { useSetupWizard } from "../SetupWizardContext";

interface StaffMember {
  id: string;
  firstName: string;
  lastName: string;
  role: string;
  email: string;
  phone: string;
  gender: string;
  idNumber: string;
}

export default function StaffManagementStep() {
  const { t } = useTranslation();
  const {
    setupData,
    updateStaff,
    validationErrors,
    setValidationError,
    clearValidationErrors,
  } = useSetupWizard();
  const [isAddingStaff, setIsAddingStaff] = useState(false);
  const [editingStaffId, setEditingStaffId] = useState<string | null>(null);
  const [newStaffMember, setNewStaffMember] = useState({
    firstName: "",
    lastName: "",
    role: "Teacher",
    email: "",
    phone: "",
    gender: "",
    idNumber: "",
  });
  const [editingStaffMember, setEditingStaffMember] =
    useState<StaffMember | null>(null);
  const [phoneValidationError, setPhoneValidationError] = useState<string>("");
  const [editPhoneValidationError, setEditPhoneValidationError] =
    useState<string>("");
  const [idNumberValidationError, setIdNumberValidationError] =
    useState<string>("");
  const [editIdNumberValidationError, setEditIdNumberValidationError] =
    useState<string>("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<string | null>(null);

  const handleAddStaff = () => {
    setIsAddingStaff(true);
    setNewStaffMember({
      firstName: "",
      lastName: "",
      role: "",
      email: "",
      phone: "",
      gender: "",
      idNumber: "",
    });
    setPhoneValidationError("");
    setIdNumberValidationError("");
  };

  const handleCancelAdd = () => {
    setIsAddingStaff(false);
    setPhoneValidationError("");
    setIdNumberValidationError("");
  };

  const handlePhoneInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    const filteredValue = value
      .split("")
      .filter(char => isValidPhoneCharacter(char))
      .join("");

    // Update the phone value
    setNewStaffMember({
      ...newStaffMember,
      phone: filteredValue,
    });

    // Real-time validation
    if (filteredValue.trim() === "") {
      setPhoneValidationError("");
      setValidationError("staffPhone", false);
    } else {
      const validation = validatePhoneFormat(filteredValue);
      if (!validation.isValid) {
        setPhoneValidationError(validation.error || "invalid");
        setValidationError("staffPhone", true);
      } else {
        setPhoneValidationError("");
        setValidationError("staffPhone", false);
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewStaffMember({
      ...newStaffMember,
      [name]: value,
    });

    // Clear validation errors when user types
    if (name === "firstName" && value.trim() !== "") {
      setValidationError("staffFirstName", false);
    } else if (name === "lastName" && value.trim() !== "") {
      setValidationError("staffLastName", false);
    } else if (name === "email" && value.trim() !== "") {
      setValidationError("staffEmail", false);
    } else if (name === "phone" && value.trim() !== "") {
      setValidationError("staffPhone", false);
    }
  };

  // Handle keydown events to prevent invalid characters
  const handlePhoneKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const allowedKeys = [
      "Backspace",
      "Delete",
      "Tab",
      "Escape",
      "Enter",
      "Home",
      "End",
      "ArrowLeft",
      "ArrowRight",
    ];

    if (
      allowedKeys.includes(e.key) ||
      (e.ctrlKey && ["a", "c", "v", "x", "z"].includes(e.key.toLowerCase()))
    ) {
      return;
    }

    // Ensure that it is a number, +, space, -, (, ), or .
    if (!isValidPhoneCharacter(e.key)) {
      e.preventDefault();
    }
  };

  // Function to handle ID number input changes
  const handleIdNumberInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = e.target;

    setNewStaffMember({
      ...newStaffMember,
      idNumber: value,
    });

    // Real-time validation for ID number
    if (value.trim() === "") {
      setIdNumberValidationError("");
      setValidationError("staffIdNumber", false);
    } else {
      const validation = validateUniqueStaffId(value, setupData.staff);
      if (!validation.isValid) {
        setIdNumberValidationError(validation.error || "invalid");
        setValidationError("staffIdNumber", true);
      } else {
        setIdNumberValidationError("");
        setValidationError("staffIdNumber", false);
      }
    }
  };

  // Function to get appropriate error message for phone validation
  const getPhoneErrorMessage = (errorType: string): string => {
    switch (errorType) {
      case "required":
        return t("validationSetup.staffPhoneRequired");
      case "tooShort":
        return t("validationSetup.staffPhoneTooShort");
      case "tooLong":
        return t("validationSetup.staffPhoneTooLong");
      case "invalidFormat":
        return t("validationSetup.staffPhoneInvalidFormat");
      default:
        return t("validationSetup.staffPhoneInvalid");
    }
  };

  // Function to get appropriate error message for ID number validation
  const getIdNumberErrorMessage = (errorType: string): string => {
    switch (errorType) {
      case "required":
        return t("validationSetup.staffIdNumberRequired");
      case "duplicateStaffId":
        return t("validationSetup.staffIdNumberDuplicate");
      default:
        return t("validationSetup.staffIdNumberRequired");
    }
  };

  const handleRoleChange = (value: string) => {
    setNewStaffMember({
      ...newStaffMember,
      role: value,
    });

    if (value) {
      setValidationError("staffRole", false);
    }
  };

  const handleGenderChange = (value: string) => {
    setNewStaffMember({
      ...newStaffMember,
      gender: value,
    });

    // Clear validation error when gender is selected
    if (value) {
      setValidationError("staffGender", false);
    }
  };

  const handleSubmit = () => {
    clearValidationErrors();
    let isValid = true;

    // Validate form data
    if (!validateRequired(newStaffMember.firstName)) {
      setValidationError("staffFirstName", true);
      isValid = false;
    }

    if (!validateRequired(newStaffMember.lastName)) {
      setValidationError("staffLastName", true);
      isValid = false;
    }

    if (!validateEmail(newStaffMember.email)) {
      setValidationError("staffEmail", true);
      isValid = false;
    }

    if (!newStaffMember.gender) {
      setValidationError("staffGender", true);
      isValid = false;
    }
    if (!newStaffMember.role) {
      setValidationError("staffRole", true);
      isValid = false;
    }

    // Enhanced phone validation
    const phoneValidation = validatePhoneFormat(newStaffMember.phone);
    if (!phoneValidation.isValid) {
      setValidationError("staffPhone", true);
      setPhoneValidationError(phoneValidation.error || "invalid");
      isValid = false;
    }

    // ID Number validation
    const idNumberValidation = validateUniqueStaffId(
      newStaffMember.idNumber,
      setupData.staff
    );
    if (!idNumberValidation.isValid) {
      setValidationError("staffIdNumber", true);
      setIdNumberValidationError(idNumberValidation.error || "required");
      isValid = false;
    }

    if (!isValid) return;

    // Add new staff member
    const newMember: StaffMember = {
      id: Date.now().toString(),
      ...newStaffMember,
    };

    updateStaff([...setupData.staff, newMember]);
    setIsAddingStaff(false);
    setPhoneValidationError("");
    setIdNumberValidationError("");
    toast.success(t("staffMessages.staffAdded"));
  };

  const handleDeleteStaff = (id: string) => {
    setStaffToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteStaff = () => {
    if (staffToDelete) {
      updateStaff(
        setupData.staff.filter(member => member.id !== staffToDelete)
      );
      toast.success(t("staffMessages.staffRemoved"));
      setDeleteDialogOpen(false);
      setStaffToDelete(null);
    }
  };

  const cancelDeleteStaff = () => {
    setDeleteDialogOpen(false);
    setStaffToDelete(null);
  };

  const handleEditStaff = (member: StaffMember) => {
    setEditingStaffId(member.id);
    setEditingStaffMember({ ...member });
    setEditPhoneValidationError("");
    setEditIdNumberValidationError("");
    clearValidationErrors();
  };

  const handleCancelEdit = () => {
    setEditingStaffId(null);
    setEditingStaffMember(null);
    setEditPhoneValidationError("");
    setEditIdNumberValidationError("");
    clearValidationErrors();
  };

  const handleSaveEdit = () => {
    if (!editingStaffMember) return;

    clearValidationErrors();
    let isValid = true;

    // Validate required fields
    if (!validateRequired(editingStaffMember.firstName)) {
      setValidationError("editFirstName", true);
      isValid = false;
    }

    if (!validateRequired(editingStaffMember.lastName)) {
      setValidationError("editLastName", true);
      isValid = false;
    }

    if (!validateRequired(editingStaffMember.role)) {
      setValidationError("editRole", true);
      isValid = false;
    }

    if (!validateRequired(editingStaffMember.email)) {
      setValidationError("editEmail", true);
      isValid = false;
    } else if (!validateEmail(editingStaffMember.email)) {
      setValidationError("editEmail", true);
      isValid = false;
    }

    if (!validateRequired(editingStaffMember.phone)) {
      setValidationError("editPhone", true);
      isValid = false;
    }

    if (!validateRequired(editingStaffMember.gender)) {
      setValidationError("editGender", true);
      isValid = false;
    }

    // Enhanced phone validation
    const phoneValidation = validatePhoneFormat(editingStaffMember.phone);
    if (!phoneValidation.isValid) {
      setValidationError("editPhone", true);
      setEditPhoneValidationError(phoneValidation.error || "invalid");
      isValid = false;
    }

    // ID Number validation for editing
    const idNumberValidation = validateUniqueStaffId(
      editingStaffMember.idNumber,
      setupData.staff,
      editingStaffMember.id
    );
    if (!idNumberValidation.isValid) {
      setValidationError("editIdNumber", true);
      setEditIdNumberValidationError(idNumberValidation.error || "required");
      isValid = false;
    }

    if (!isValid) return;

    // Update staff member
    const updatedStaff = setupData.staff.map(member =>
      member.id === editingStaffMember.id ? editingStaffMember : member
    );

    updateStaff(updatedStaff);
    setEditingStaffId(null);
    setEditingStaffMember(null);
    setEditPhoneValidationError("");
    setEditIdNumberValidationError("");
    toast.success(t("staffMessages.staffUpdated"));
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingStaffMember) return;

    const { name, value } = e.target;

    // Handle phone input with character restriction
    if (name === "phone") {
      const filteredValue = value
        .split("")
        .filter(isValidPhoneCharacter)
        .join("");
      setEditingStaffMember({
        ...editingStaffMember,
        [name]: filteredValue,
      });
    } else {
      setEditingStaffMember({
        ...editingStaffMember,
        [name]: value,
      });
    }

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(
        `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
        false
      );
      if (name === "phone") {
        setEditPhoneValidationError("");
      }
      if (name === "idNumber") {
        setEditIdNumberValidationError("");
      }
    }
  };

  const handleEditSelectChange = (name: string, value: string) => {
    if (!editingStaffMember) return;

    setEditingStaffMember({
      ...editingStaffMember,
      [name]: value,
    });

    // Clear validation error when user selects
    setValidationError(
      `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
      false
    );
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center text-gray-800">
          <Users className="h-5 w-5 text-purple-600 mr-2" />
          {t("setup.staffManagement")}
        </h2>
        {!isAddingStaff && (
          <Button
            onClick={handleAddStaff}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="h-4 w-4 mr-2" /> {t("setup.addStaff")}
          </Button>
        )}
      </div>

      <p className="text-gray-600 mb-2">
        {t("setup.addStaffMembers")} {t("setup.schoolCategory")}: Primary
      </p>
      <p className="text-gray-500 text-sm mb-4">
        {t("setup.availableRoles")}: Teacher, Subject Teacher, Professor,
        Inspector
      </p>

      {isAddingStaff && (
        <Card className="mb-6 border-purple-200 shadow-sm">
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium mb-4">
              {t("setup.addNewStaffMember")}
            </h3>
            <div className="grid gap-4">
              <div className="pb-4 mb-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="firstName"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.firstName")}*
                    </label>
                    <Input
                      id="firstName"
                      name="firstName"
                      placeholder={t("setup.enterFirstName")}
                      value={newStaffMember.firstName}
                      onChange={handleInputChange}
                      className={
                        validationErrors.staffFirstName
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }
                    />
                    {validationErrors.staffFirstName && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.staffFirstNameRequired")}
                      </p>
                    )}
                  </div>
                  <div>
                    <label
                      htmlFor="lastName"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.lastName")}*
                    </label>
                    <Input
                      id="lastName"
                      name="lastName"
                      placeholder={t("setup.enterLastName")}
                      value={newStaffMember.lastName}
                      onChange={handleInputChange}
                      className={
                        validationErrors.staffLastName
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }
                    />
                    {validationErrors.staffLastName && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.staffLastNameRequired")}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="pb-4 mb-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="gender"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.gender")}*
                    </label>
                    <Select
                      value={newStaffMember.gender}
                      onValueChange={handleGenderChange}
                    >
                      <SelectTrigger
                        className={`w-full ${
                          validationErrors.staffGender
                            ? "border-red-500 focus-visible:ring-red-500"
                            : ""
                        }`}
                      >
                        <SelectValue placeholder={t("setup.selectGender")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">{t("setup.male")}</SelectItem>
                        <SelectItem value="female">
                          {t("setup.female")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {validationErrors.staffGender && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.genderRequired")}
                      </p>
                    )}
                  </div>
                  <div>
                    <label
                      htmlFor="role"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.role")}*
                    </label>
                    <Select
                      value={newStaffMember.role}
                      onValueChange={handleRoleChange}
                    >
                      <SelectTrigger
                        className={`w-full ${
                          validationErrors.staffGender
                            ? "border-red-500 focus-visible:ring-red-500"
                            : ""
                        }`}
                      >
                        <SelectValue placeholder={t("setup.selectRole")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Teacher">
                          {t("setup.teacher")}
                        </SelectItem>
                        <SelectItem value="Subject Teacher">
                          {t("setup.subjectTeacher")}
                        </SelectItem>
                        <SelectItem value="Professor">
                          {t("setup.professor")}
                        </SelectItem>
                        <SelectItem value="Inspector">
                          {t("setup.inspector")}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {validationErrors.staffRole && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.staffRoleRequired")}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className=" pb-4 mb-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <label
                      htmlFor="email"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.email")}*
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={newStaffMember.email}
                      onChange={handleInputChange}
                      className={
                        validationErrors.staffEmail
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }
                    />
                    {validationErrors.staffEmail && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.staffEmailRequired")}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="phone"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.phone")}*
                    </label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      placeholder="+383 49123123"
                      value={newStaffMember.phone}
                      onChange={handlePhoneInputChange}
                      onKeyDown={handlePhoneKeyDown}
                      className={
                        validationErrors.staffPhone
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }
                    />
                    {validationErrors.staffPhone && phoneValidationError && (
                      <p className="text-red-500 text-xs mt-1">
                        {getPhoneErrorMessage(phoneValidationError)}
                      </p>
                    )}
                    {!newStaffMember.phone && (
                      <p className="text-gray-500 text-xs mt-1">
                        {t("validationSetup.staffPhoneFormats")}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="idNumber"
                      className="text-sm font-medium mb-1 block"
                    >
                      {t("setup.idNumber")}*
                    </label>
                    <Input
                      id="idNumber"
                      name="idNumber"
                      type="text"
                      placeholder={t("setup.enterIdNumber")}
                      value={newStaffMember.idNumber}
                      onChange={handleIdNumberInputChange}
                      className={
                        validationErrors.staffIdNumber
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }
                    />
                    {validationErrors.staffIdNumber &&
                      idNumberValidationError && (
                        <p className="text-red-500 text-xs mt-1">
                          {getIdNumberErrorMessage(idNumberValidationError)}
                        </p>
                      )}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={handleCancelAdd}>
                  {t("setup.cancel")}
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t("setup.saveStaffMember")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {setupData.staff.length > 0 ? (
        <div className="border rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.name")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.role")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.email")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.phone")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.idNumber")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.actions")}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {setupData.staff.map(member => (
                <tr key={member.id}>
                  {editingStaffId === member.id ? (
                    // Edit mode
                    <>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex flex-col gap-2">
                          <div className="flex gap-2">
                            <Input
                              name="firstName"
                              value={editingStaffMember?.firstName || ""}
                              onChange={handleEditInputChange}
                              placeholder={t("setup.firstName")}
                              className={`text-sm h-9 w-24 ${
                                validationErrors.editFirstName
                                  ? "border-red-500 focus-visible:ring-red-500"
                                  : ""
                              }`}
                            />
                            <Input
                              name="lastName"
                              value={editingStaffMember?.lastName || ""}
                              onChange={handleEditInputChange}
                              placeholder={t("setup.lastName")}
                              className={`text-sm h-9 w-24 ${
                                validationErrors.editLastName
                                  ? "border-red-500 focus-visible:ring-red-500"
                                  : ""
                              }`}
                            />
                          </div>
                          <Select
                            value={editingStaffMember?.gender || ""}
                            onValueChange={value =>
                              handleEditSelectChange("gender", value)
                            }
                          >
                            <SelectTrigger
                              className={`w-full text-sm h-9  ${
                                validationErrors.editGender
                                  ? "border-red-500 focus-visible:ring-red-500"
                                  : ""
                              }`}
                            >
                              <SelectValue
                                placeholder={t("setup.selectGender")}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="male">
                                {t("setup.male")}
                              </SelectItem>
                              <SelectItem value="female">
                                {t("setup.female")}
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <Select
                          value={editingStaffMember?.role || ""}
                          onValueChange={value =>
                            handleEditSelectChange("role", value)
                          }
                        >
                          <SelectTrigger
                            className={`w-full text-sm h-9 ${
                              validationErrors.editRole
                                ? "border-red-500 focus-visible:ring-red-500"
                                : ""
                            }`}
                          >
                            <SelectValue placeholder={t("setup.selectRole")} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Teacher">
                              {t("setup.teacher")}
                            </SelectItem>
                            <SelectItem value="Subject Teacher">
                              {t("setup.subjectTeacher")}
                            </SelectItem>
                            <SelectItem value="Professor">
                              {t("setup.professor")}
                            </SelectItem>
                            <SelectItem value="Inspector">
                              {t("setup.inspector")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </td>
                      <td className="px-2 py-4 whitespace-nowrap">
                        <Input
                          name="email"
                          type="email"
                          value={editingStaffMember?.email || ""}
                          onChange={handleEditInputChange}
                          placeholder="<EMAIL>"
                          className={`text-sm h-9 w-32 ${
                            validationErrors.editEmail
                              ? "border-red-500 focus-visible:ring-red-500"
                              : ""
                          }`}
                        />
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <Input
                          name="phone"
                          type="tel"
                          value={editingStaffMember?.phone || ""}
                          onChange={handleEditInputChange}
                          placeholder="+383 49123123"
                          className={`text-sm h-9 w-32 ${
                            validationErrors.editPhone
                              ? "border-red-500 focus-visible:ring-red-500"
                              : ""
                          }`}
                        />
                        {validationErrors.editPhone &&
                          editPhoneValidationError && (
                            <p className="text-red-500 text-xs mt-1">
                              {getPhoneErrorMessage(editPhoneValidationError)}
                            </p>
                          )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <Input
                          name="idNumber"
                          type="text"
                          value={editingStaffMember?.idNumber || ""}
                          onChange={handleEditInputChange}
                          placeholder={t("setup.enterIdNumber")}
                          className={`text-sm h-9 w-32 ${
                            validationErrors.editIdNumber
                              ? "border-red-500 focus-visible:ring-red-500"
                              : ""
                          }`}
                        />
                        {validationErrors.editIdNumber &&
                          editIdNumberValidationError && (
                            <p className="text-red-500 text-xs mt-1">
                              {getIdNumberErrorMessage(
                                editIdNumberValidationError
                              )}
                            </p>
                          )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="flex gap-2 justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleSaveEdit}
                            className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleCancelEdit}
                            className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </>
                  ) : (
                    // Display mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {member.firstName} {member.lastName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {member.role}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {member.email}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {member.phone}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {member.idNumber || "-"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex gap-2 justify-center">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditStaff(member)}
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteStaff(member.id)}
                            className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="border rounded-md p-8 text-center text-gray-500">
          {t("setup.noStaffMembersAdded")}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteConfirmation.confirmDeleteStaff")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteConfirmation.confirmDeleteStaffMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteStaff}>
              {t("deleteConfirmation.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteStaff}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("deleteConfirmation.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
