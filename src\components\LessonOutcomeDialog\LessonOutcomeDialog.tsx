import React from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import LessonOutcomeForm, { LessonOutcomes } from "../LessonOutcomeForm/LessonOutcomeForm";
import { useTranslation } from "react-i18next";

interface LessonOutcomeDialogProps {
  trigger: React.ReactNode;
  lessonId: string;
  lessonTitle: string;
  onSubmit: (outcomes: LessonOutcomes) => void;
  initialData?: LessonOutcomes;
}

export default function LessonOutcomeDialog({
  trigger,
  lessonId,
  lessonTitle,
  onSubmit,
  initialData,
}: LessonOutcomeDialogProps) {
  const { t } = useTranslation();
  const [open, setOpen] = React.useState(false);

  const handleSubmit = (outcomes: LessonOutcomes) => {
    onSubmit(outcomes);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("lessonOutcomes.dialogTitle")}</DialogTitle>
        </DialogHeader>
        <LessonOutcomeForm
          lessonId={lessonId}
          lessonTitle={lessonTitle}
          onSubmit={handleSubmit}
          onCancel={() => setOpen(false)}
          initialData={initialData}
        />
      </DialogContent>
    </Dialog>
  );
}