import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { CheckCircle } from "lucide-react";
import Footer from "@/components/Footer";
import AccordionCard from "@/components/AccordionCard";
import PublicHeader from "@/components/PublicHeader";

export default function PricingPage() {
  const { t } = useTranslation();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [isTablet, setIsTablet] = useState(
    window.innerWidth >= 640 && window.innerWidth < 1024
  );

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 1024);
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial sizing
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const pricingPlans = [
    {
      name: t("pricingPage.plans.basic.name"),
      price: t("pricingPage.plans.basic.price"),
      description: t("pricingPage.plans.basic.description"),
      features: t("pricingPage.plans.basic.features", { returnObjects: true }),
      popular: false,
    },
    {
      name: t("pricingPage.plans.professional.name"),
      price: t("pricingPage.plans.professional.price"),
      description: t("pricingPage.plans.professional.description"),
      features: t("pricingPage.plans.professional.features", {
        returnObjects: true,
      }),
      popular: true,
    },
    {
      name: t("pricingPage.plans.enterprise.name"),
      price: t("pricingPage.plans.enterprise.price"),
      description: t("pricingPage.plans.enterprise.description"),
      features: t("pricingPage.plans.enterprise.features", {
        returnObjects: true,
      }),
      popular: false,
    },
  ];

  return (
    <div className="cursor-default">
      <PublicHeader />

      <div className="bg-purple-50 py-16 md:py-24">
        <div className="container mx-auto px-4 md:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1
              className={`${
                isMobile ? "text-3xl" : isTablet ? "text-4xl" : "text-5xl"
              } font-bold mb-4`}
            >
              {t("pricingPage.title")}
            </h1>
            <p
              className={`text-gray-600 ${isMobile ? "text-base" : "text-lg"}`}
            >
              {t("pricingPage.subtitle")}
            </p>
          </div>

          {/* Pricing Cards */}
          <div
            className={`grid ${
              isMobile
                ? "grid-cols-1 gap-6"
                : isTablet
                ? "grid-cols-2 gap-6"
                : "grid-cols-3 gap-8"
            } max-w-6xl mx-auto`}
          >
            {pricingPlans.map((plan, index) => (
              <div
                key={index}
                className={`bg-white rounded-lg shadow-md overflow-hidden relative ${
                  plan.popular ? "border-2 border-purple-500" : ""
                }`}
              >
                {plan.popular && (
                  <div className="bg-purple-500 text-white text-xs font-bold px-3 py-1 absolute right-0 top-0 rounded-bl-lg">
                    {t("pricingPage.mostPopular")}
                  </div>
                )}
                <div className={`p-${isMobile ? "4" : "6"}`}>
                  <h2 className="text-2xl font-bold mb-2">{plan.name}</h2>
                  <p className="text-gray-600 text-sm mb-4 h-12">
                    {plan.description}
                  </p>
                  <div className="mb-6">
                    <span
                      className={`${
                        isMobile ? "text-3xl" : "text-4xl"
                      } font-bold`}
                    >
                      {plan.price}
                    </span>
                    <span className="text-gray-500">
                      {t("pricingPage.perMonth")}
                    </span>
                  </div>
                  <button
                    className={`w-full py-2 rounded-md font-medium transition cursor-pointer ${
                      plan.popular
                        ? "bg-purple-500 text-white hover:bg-purple-600"
                        : "bg-gray-100 text-gray-800 hover:bg-gray-200"
                    }`}
                  >
                    {t("pricingPage.getStarted")}
                  </button>
                </div>
                <div
                  className={`p-${
                    isMobile ? "4" : "6"
                  } bg-gray-50 h-full border-t`}
                >
                  <h3 className="font-medium mb-4">
                    {t("pricingPage.whatsIncluded")}
                  </h3>
                  <ul className="space-y-3">
                    {(plan.features as string[]).map(
                      (feature: string, i: number) => (
                        <li key={i} className="flex items-start">
                          <CheckCircle className="text-green-500 mr-2 h-5 w-5 flex-shrink-0" />
                          <span
                            className={`${isMobile ? "text-xs" : "text-sm"}`}
                          >
                            {feature}
                          </span>
                        </li>
                      )
                    )}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <AccordionCard />

      {/* Help Section */}
      <div className="py-16 bg-purple-50">
        <div className="container mx-auto px-4 md:px-8 text-center">
          <h2
            className={`${
              isMobile ? "text-2xl" : isTablet ? "text-2xl" : "text-3xl"
            } font-bold mb-4`}
          >
            {t("pricingPage.needHelp")}
          </h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            {t("pricingPage.helpDescription")}
          </p>
          <div
            className={`flex ${
              isMobile ? "flex-col" : "flex-row"
            } gap-4 justify-center`}
          >
            <button className="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-md transition">
              {t("pricingPage.contactSales")}
            </button>
            <button className="bg-white border border-gray-300 text-gray-700 px-6 py-3 rounded-md hover:bg-gray-50 transition flex items-center justify-center gap-2">
              {t("pricingPage.scheduleDemo")}
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 12H19M19 12L12 5M19 12L12 19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
