export interface Student {
  id: string;
  name: string;
  dob: string;
  parent: string;
  phone: string;
  email: string;
  status: "active" | "inactive";
  grade: string;
  section: string;
}

export interface Section {
  [sectionName: string]: Student[];
}

export interface Grade {
  [sectionName: string]: Student[];
}

export interface StudentsData {
  [grade: string]: Section;
}

export interface TeacherAssignment {
  teacherId: string;
  teacherName: string;
  assignedGrades: string[];
  assignedSections: { [grade: string]: string[] };
}

export const teacherAssignments: TeacherAssignment[] = [
  {
    teacherId: "TEACH001",
    teacherName: "<PERSON>",
    assignedGrades: ["grade9"],
    assignedSections: {
      grade9: ["sectionA", "sectionB"],
    },
  },
  {
    teacherId: "TEACH002",
    teacherName: "<PERSON>",
    assignedGrades: ["grade1", "grade2"],
    assignedSections: {
      grade1: ["sectionA"],
      grade2: ["sectionA", "sectionB"],
    },
  },
  {
    teacherId: "TEACH003",
    teacherName: "<PERSON>",
    assignedGrades: ["grade3", "grade4"],
    assignedSections: {
      grade3: ["sectionA", "sectionB"],
      grade4: ["sectionA", "sectionC"],
    },
  },
  {
    teacherId: "TEACH004",
    teacherName: "Emily Davis",
    assignedGrades: ["grade5", "grade6", "grade7"],
    assignedSections: {
      grade5: ["sectionA", "sectionB"],
      grade6: ["sectionA"],
      grade7: ["sectionA", "sectionC"],
    },
  },
];

// Updated function (no need to add grade/section manually anymore)
export const getStudentsForTeacher = (teacherId: string): Student[] => {
  const assignment = teacherAssignments.find((t) => t.teacherId === teacherId);
  if (!assignment) return [];

  const teacherStudents: Student[] = [];

  assignment.assignedGrades.forEach((grade) => {
    const sections = assignment.assignedSections[grade] || [];
    sections.forEach((section) => {
      const studentsInSection = studentsData[grade]?.[section] || [];
      teacherStudents.push(...studentsInSection);
    });
  });

  return teacherStudents;
};

export const getAllStudents = (): Student[] => {
  const allStudents: Student[] = [];

  Object.entries(studentsData).forEach(([_, sections]) => {
    Object.entries(sections).forEach(([_, students]) => {
      allStudents.push(...students);
    });
  });

  return allStudents;
};

export const studentsData: StudentsData = {
  grade1: {
    sectionA: [
      {
        id: "STU101",
        name: "Arda Berisha",
        dob: "2/3/2018",
        parent: "Luan Berisha",
        phone: "+355 69 000 0001",
        email: "<EMAIL>",
        status: "active",
        grade: "grade1",
        section: "sectionA",
      },
      {
        id: "STU102",
        name: "Elira Gashi",
        dob: "6/11/2018",
        parent: "Nora Gashi",
        phone: "+355 69 000 0002",
        email: "<EMAIL>",
        status: "active",
        grade: "grade1",
        section: "sectionA",
      },
    ],
    sectionB: [
      {
        id: "STU103",
        name: "Driton Selimi",
        dob: "5/15/2018",
        parent: "Bujar Selimi",
        phone: "+355 69 000 0003",
        email: "<EMAIL>",
        status: "active",
        grade: "grade1",
        section: "sectionB",
      },
      {
        id: "STU104",
        name: "Adea Morina",
        dob: "3/8/2018",
        parent: "Teuta Morina",
        phone: "+355 69 000 0004",
        email: "<EMAIL>",
        status: "inactive",
        grade: "grade1",
        section: "sectionB",
      },
    ],
  },
  grade2: {
    sectionA: [
      {
        id: "STU201",
        name: "Luan Krasniqi",
        dob: "9/10/2017",
        parent: "Besnik Krasniqi",
        phone: "+355 69 000 0010",
        email: "<EMAIL>",
        status: "active",
        grade: "grade2",
        section: "sectionA",
      },
      {
        id: "STU202",
        name: "Mira Dervishi",
        dob: "11/22/2017",
        parent: "Arta Dervishi",
        phone: "+355 69 000 0011",
        email: "<EMAIL>",
        status: "active",
        grade: "grade2",
        section: "sectionA",
      },
    ],
    sectionB: [
      {
        id: "STU203",
        name: "Eron Shala",
        dob: "7/5/2017",
        parent: "Rinor Shala",
        phone: "+355 69 000 0012",
        email: "<EMAIL>",
        status: "inactive",
        grade: "grade2",
        section: "sectionB",
      },
    ],
  },
  grade3: {
    sectionA: [
      {
        id: "STU301",
        name: "Arta Hoxha",
        dob: "4/2/2016",
        parent: "Ilir Hoxha",
        phone: "+355 69 000 0020",
        email: "<EMAIL>",
        status: "active",
        grade: "grade3",
        section: "sectionA",
      },
    ],
    sectionB: [
      {
        id: "STU302",
        name: "Dion Kelmendi",
        dob: "5/13/2016",
        parent: "Jeton Kelmendi",
        phone: "+355 69 000 0021",
        email: "<EMAIL>",
        status: "active",
        grade: "grade3",
        section: "sectionB",
      },
    ],
  },
  grade4: {
    sectionA: [
      {
        id: "STU401",
        name: "Lira Mehmeti",
        dob: "1/9/2015",
        parent: "Valbona Mehmeti",
        phone: "+355 69 000 0030",
        email: "<EMAIL>",
        status: "active",
        grade: "grade4",
        section: "sectionA",
      },
    ],
    sectionC: [
      {
        id: "STU402",
        name: "Bleron Shabani",
        dob: "2/28/2015",
        parent: "Arben Shabani",
        phone: "+355 69 000 0031",
        email: "<EMAIL>",
        status: "inactive",
        grade: "grade4",
        section: "sectionC",
      },
    ],
  },
  grade5: {
    sectionA: [
      {
        id: "STU501",
        name: "Elior Berbatovci",
        dob: "3/12/2014",
        parent: "Enver Berbatovci",
        phone: "+355 69 000 0040",
        email: "<EMAIL>",
        status: "active",
        grade: "grade5",
        section: "sectionA",
      },
    ],
    sectionB: [
      {
        id: "STU502",
        name: "Megi Rexha",
        dob: "6/18/2014",
        parent: "Rina Rexha",
        phone: "+355 69 000 0041",
        email: "<EMAIL>",
        status: "active",
        grade: "grade5",
        section: "sectionB",
      },
    ],
  },
  grade6: {
    sectionA: [
      {
        id: "STU601",
        name: "Rron Istrefi",
        dob: "11/5/2013",
        parent: "Mentor Istrefi",
        phone: "+355 69 000 0050",
        email: "<EMAIL>",
        status: "active",
        grade: "grade6",
        section: "sectionA",
      },
    ],
  },
  grade7: {
    sectionA: [
      {
        id: "STU701",
        name: "Vesa Daka",
        dob: "7/21/2012",
        parent: "Besa Daka",
        phone: "+355 69 000 0060",
        email: "<EMAIL>",
        status: "active",
        grade: "grade7",
        section: "sectionA",
      },
    ],
    sectionC: [
      {
        id: "STU702",
        name: "Leon Hajdari",
        dob: "9/29/2012",
        parent: "Shkëlzen Hajdari",
        phone: "+355 69 000 0061",
        email: "<EMAIL>",
        status: "inactive",
        grade: "grade7",
        section: "sectionC",
      },
    ],
  },
  grade8: {
    sectionA: [
      {
        id: "STU801",
        name: "Rita Osmani",
        dob: "2/17/2011",
        parent: "Luljeta Osmani",
        phone: "+355 69 000 0070",
        email: "<EMAIL>",
        status: "active",
        grade: "grade8",
        section: "sectionA",
      },
    ],
  },
  grade9: {
    sectionA: [
      {
        id: "STU901",
        name: "Drin Gërvalla",
        dob: "12/3/2010",
        parent: "Ardian Gërvalla",
        phone: "+355 69 000 0080",
        email: "<EMAIL>",
        status: "active",
        grade: "grade9",
        section: "sectionA",
      },
    ],
    sectionB: [
      {
        id: "STU902",
        name: "Jeta Ukshini",
        dob: "8/25/2010",
        parent: "Naim Ukshini",
        phone: "+355 69 000 0081",
        email: "<EMAIL>",
        status: "active",
        grade: "grade9",
        section: "sectionB",
      },
    ],
  },
};
