// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
// import { Badge } from "@/components/ui/badge";
// import { Button } from "@/components/ui/button";
// import {
//   Search,
//   Filter,
//   Plus,
//   EyeIcon,
//   Pencil,
//   Phone,
//   Mail,
//   User,
//   Calendar,
// } from "lucide-react";
// import { useTranslation } from "react-i18next";
// import { useMemo, useState, useEffect } from "react";

// // Teacher-specific mock data - represents students in teacher's assigned classes
// const teacherStudentsData = {
//   grade9: {
//     sectionA: [
//       {
//         id: "STU901",
//         name: "Endrit Peci",
//         dob: "3/2/2010",
//         parent: "<PERSON><PERSON> Peci",
//         phone: "+355 69 000 0080",
//         email: "<EMAIL>",
//         status: "active" as const,
//       },
//       {
//         id: "STU904",
//         name: "<PERSON><PERSON>",
//         dob: "8/15/2010",
//         parent: "<PERSON><PERSON><PERSON>",
//         phone: "+355 69 000 0083",
//         email: "<EMAIL>",
//         status: "active" as const,
//       },
//       {
//         id: "STU905",
//         name: "Drin Kastrati",
//         dob: "1/20/2010",
//         parent: "Fatmire Kastrati",
//         phone: "+355 69 000 0084",
//         email: "<EMAIL>",
//         status: "active" as const,
//       },
//     ],
//     sectionB: [
//       {
//         id: "STU902",
//         name: "Sara Daka",
//         dob: "12/29/2010",
//         parent: "Mentor Daka",
//         phone: "+355 69 000 0081",
//         email: "<EMAIL>",
//         status: "inactive" as const,
//       },
//       {
//         id: "STU903",
//         name: "Ilir Luma",
//         dob: "6/17/2010",
//         parent: "Fatmir Luma",
//         phone: "+355 69 000 0082",
//         email: "<EMAIL>",
//         status: "active" as const,
//       },
//     ],
//   },
// };

// export default function TeacherStudentsPage() {
//   const { t } = useTranslation();
//   const [query, setQuery] = useState("");
//   const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
//   const [isTablet, setIsTablet] = useState(
//     window.innerWidth >= 768 && window.innerWidth < 1024
//   );

//   // Handle responsive breakpoints
//   useEffect(() => {
//     const handleResize = () => {
//       setIsMobile(window.innerWidth < 768);
//       setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
//     };

//     window.addEventListener("resize", handleResize);
//     handleResize();
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   // Flatten teacher's students data, adding grade and section info to each student
//   const allStudents = useMemo(() => {
//     const students = [];
//     for (const grade in teacherStudentsData) {
//       const sections = teacherStudentsData[grade];
//       for (const section in sections) {
//         const studentsInSection = sections[section].map((student) => ({
//           ...student,
//           grade,
//           section,
//         }));
//         students.push(...studentsInSection);
//       }
//     }
//     return students;
//   }, []);

//   // Filter students by query
//   const filteredStudents = useMemo(() => {
//     if (!query) return allStudents;
//     const lowerQuery = query.toLowerCase();
//     return allStudents.filter(
//       ({ name, id, parent }) =>
//         name.toLowerCase().includes(lowerQuery) ||
//         id.toLowerCase().includes(lowerQuery) ||
//         parent.toLowerCase().includes(lowerQuery)
//     );
//   }, [query, allStudents]);

//   // Calculate teacher-specific statistics
//   const teacherStats = useMemo(() => {
//     const totalStudents = allStudents.length;
//     const activeStudents = allStudents.filter(
//       (s) => s.status === "active"
//     ).length;
//     const totalSections = Object.keys(teacherStudentsData.grade9).length;

//     return {
//       totalStudents,
//       activeStudents,
//       totalSections,
//       totalClasses: 1, // Teacher typically teaches one grade
//     };
//   }, [allStudents]);

//   // StudentCard component for mobile/tablet view
//   const StudentCard = ({
//     student,
//     showGradeSection = false,
//   }: {
//     student: any;
//     showGradeSection?: boolean;
//   }) => (
//     <div className="bg-white border border-gray-300 rounded-lg p-4 mb-4 shadow-sm">
//       <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
//         <div className="flex-1">
//           <div className="flex items-center gap-2 mb-2">
//             <User className="w-4 h-4 text-gray-500" />
//             <h3 className="font-semibold text-lg">{student.name}</h3>
//             <Badge
//               variant={student.status === "active" ? "default" : "secondary"}
//               className={
//                 student.status === "active"
//                   ? "bg-green-100 text-green-600 hover:bg-green-100 ml-auto sm:ml-0"
//                   : "bg-red-100 text-red-600 hover:bg-red-100 ml-auto sm:ml-0"
//               }
//             >
//               {student.status}
//             </Badge>
//           </div>

//           <div className="space-y-2 text-sm">
//             <div className="flex items-center gap-2">
//               <span className="font-medium text-gray-600">ID:</span>
//               <span>{student.id}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <Calendar className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.dateOfBirth")}:
//               </span>
//               <span>{student.dob}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <User className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.parent/guardian")}:
//               </span>
//               <span>{student.parent}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <Phone className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.phone")}:
//               </span>
//               <span>{student.phone}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <Mail className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.email")}:
//               </span>
//               <span className="truncate">{student.email}</span>
//             </div>

//             {showGradeSection && (
//               <>
//                 <div className="flex items-center gap-2">
//                   <span className="font-medium text-gray-600">
//                     {t("manageStudents.grade")}:
//                   </span>
//                   <span>{student.grade.replace("grade", "")}</span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <span className="font-medium text-gray-600">
//                     {t("manageStudents.section")}:
//                   </span>
//                   <span>{student.section}</span>
//                 </div>
//               </>
//             )}
//           </div>
//         </div>

//         <div className="flex gap-2 sm:flex-col sm:gap-1">
//           <Button variant="ghost" size="sm" className="flex-1 sm:flex-none">
//             <EyeIcon className="w-4 h-4 mr-2" />
//             <span className="sm:hidden">View</span>
//           </Button>
//           <Button variant="ghost" size="sm" className="flex-1 sm:flex-none">
//             <Pencil className="w-4 h-4 mr-2" />
//             <span className="sm:hidden">Edit</span>
//           </Button>
//         </div>
//       </div>
//     </div>
//   );

//   // Render students function - responsive table/cards
//   const renderStudents = (
//     students: any[],
//     sectionName: string,
//     showGradeSection = false
//   ) => (
//     <div className="border border-gray-300 rounded-md p-4 mb-6">
//       <div className="text-lg font-semibold mb-4">{sectionName}</div>

//       {/* Mobile/Tablet Card View */}
//       {isMobile || isTablet ? (
//         <div className="space-y-4">
//           {students.map((student, idx) => (
//             <StudentCard
//               key={idx}
//               student={student}
//               showGradeSection={showGradeSection}
//             />
//           ))}
//         </div>
//       ) : (
//         /* Desktop Table View */
//         <div className="overflow-x-auto">
//           <table className="min-w-full text-sm text-left">
//             <thead className="bg-gray-100">
//               <tr className="text-gray-400 ">
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.studentId")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {`${t("manageStudents.firstName")} ${t(
//                     "manageStudents.lastName"
//                   )}`}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.dateOfBirth")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.parent/guardian")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.phone")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.email")}
//                 </th>
//                 {showGradeSection && (
//                   <>
//                     <th className="px-4 py-2 font-normal">
//                       {t("manageStudents.grade")}
//                     </th>
//                     <th className="px-4 py-2 font-normal">
//                       {t("manageStudents.section")}
//                     </th>
//                   </>
//                 )}
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.status")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.actions")}
//                 </th>
//               </tr>
//             </thead>
//             <tbody>
//               {students.map((student, idx) => (
//                 <tr key={idx} className="border-t">
//                   <td className="px-4 py-2 ">{student.id}</td>
//                   <td className="px-4 py-2 ">{student.name}</td>
//                   <td className="px-4 py-2 ">{student.dob}</td>
//                   <td className="px-4 py-2 ">{student.parent}</td>
//                   <td className="px-4 py-2 ">{student.phone}</td>
//                   <td className="px-4 py-2 ">{student.email}</td>
//                   {showGradeSection && (
//                     <>
//                       <td className="px-4 py-2 ">
//                         {student.grade.replace("grade", "")}
//                       </td>
//                       <td className="px-4 py-2 ">{student.section}</td>
//                     </>
//                   )}
//                   <td className="px-4 py-2 ">
//                     <Badge
//                       variant={
//                         student.status === "active" ? "default" : "secondary"
//                       }
//                       className={
//                         student.status === "active"
//                           ? "bg-green-100 text-green-600 hover:bg-green-100"
//                           : "bg-red-100 text-red-600 hover:bg-red-100"
//                       }
//                     >
//                       {student.status}
//                     </Badge>
//                   </td>
//                   <td className="px-4 py-2 ">
//                     <div className="flex items-center gap-2">
//                       <Button variant="ghost" size="sm">
//                         <EyeIcon className="w-4 h-4" />
//                       </Button>
//                       <Button variant="ghost" size="sm">
//                         <Pencil className="w-4 h-4" />
//                       </Button>
//                     </div>
//                   </td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>
//       )}
//     </div>
//   );

//   return (
//     <div className="w-full">
//       <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
//         <div>
//           <h2 className="text-2xl sm:text-3xl font-bold">
//             {t("manageStudents.title")}
//           </h2>
//           <p className="text-gray-500">{t("manageStudents.description")}</p>
//         </div>
//         <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
//           <Button variant="outline" size="sm" className="cursor-pointer">
//             <Filter className="w-4 h-4 mr-2" />
//             {t("manageStudents.filter")}
//           </Button>

//           <Button
//             size="sm"
//             className="bg-purple-600 hover:bg-purple-700 cursor-pointer"
//             disabled={true}
//           >
//             <Plus className="w-4 h-4 mr-2" />
//             {t("manageStudents.addStudent")}
//           </Button>
//         </div>
//       </div>

//       {/* Search bar */}
//       <div className="mb-4 p-2 rounded-md bg-white border border-gray-300">
//         <div className="w-full bg-white border rounded-md flex items-center gap-2 px-4 py-2">
//           <Search className="w-5 h-5 text-gray-400" />
//           <input
//             type="text"
//             placeholder={t("manageStudents.searchPlaceholder")}
//             className="w-full bg-transparent outline-none"
//             value={query}
//             onChange={(e) => setQuery(e.target.value)}
//           />
//         </div>
//       </div>

//       {/* Statistics cards */}
//       <div className="mb-6 grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4">
//         <div className="p-3 sm:p-4 rounded-md bg-white border border-gray-300 flex flex-col justify-between">
//           <div className="text-xl sm:text-2xl font-bold text-blue-500">
//             {teacherStats.totalStudents}
//           </div>
//           <div className="text-xs sm:text-sm text-muted-foreground">
//             {t("manageStudents.totalStudents")}
//           </div>
//         </div>
//         <div className="p-3 sm:p-4 rounded-md bg-white border border-gray-300 flex flex-col justify-between">
//           <div className="text-xl sm:text-2xl font-bold text-green-500">
//             {teacherStats.activeStudents}
//           </div>
//           <div className="text-xs sm:text-sm text-muted-foreground">
//             {t("manageStudents.activeStudents")}
//           </div>
//         </div>
//         <div className="p-3 sm:p-4 rounded-md bg-white border border-gray-300 flex flex-col justify-between">
//           <div className="text-xl sm:text-2xl font-bold text-purple-500">
//             {teacherStats.totalClasses}
//           </div>
//           <div className="text-xs sm:text-sm text-muted-foreground">
//             {t("manageStudents.totalClasses")}
//           </div>
//         </div>
//         <div className="p-3 sm:p-4 rounded-md bg-white border border-gray-300 flex flex-col justify-between">
//           <div className="text-xl sm:text-2xl font-bold text-red-500">
//             {teacherStats.totalSections}
//           </div>
//           <div className="text-xs sm:text-sm text-muted-foreground">
//             {t("manageStudents.totalSections")}
//           </div>
//         </div>
//       </div>

//       {/* Students display */}
//       {query ? (
//         filteredStudents.length > 0 ? (
//           renderStudents(
//             filteredStudents,
//             t("manageStudents.searchResults"),
//             true
//           )
//         ) : (
//           <div className="flex items-center justify-center h-64">
//             <p className="text-center text-muted-foreground">
//               {t("manageStudents.noStudentsFound")}
//             </p>
//           </div>
//         )
//       ) : (
//         <Tabs defaultValue="sectionA" className="w-full">
//           <TabsList className="bg-muted w-full justify-center flex-wrap gap-1">
//             {Object.keys(teacherStudentsData.grade9).map((sectionKey) => (
//               <TabsTrigger
//                 key={sectionKey}
//                 value={sectionKey}
//                 className="min-w-[100px] sm:w-[120px] text-xs sm:text-sm capitalize"
//               >
//                 {`${t("manageStudents.section")} ${sectionKey.replace(
//                   "section",
//                   ""
//                 )}`}
//               </TabsTrigger>
//             ))}
//           </TabsList>

//           {Object.entries(teacherStudentsData.grade9).map(
//             ([sectionKey, students]) => (
//               <TabsContent key={sectionKey} value={sectionKey}>
//                 {students.length > 0
//                   ? renderStudents(
//                       students,
//                       `${t("setup.grade")} 9 - ${t(
//                         "manageStudents.section"
//                       )} ${sectionKey.replace("section", "")}`
//                     )
//                   : null}

//                 {students.length === 0 && (
//                   <p className="text-center mt-6 text-muted-foreground">
//                     {t("manageStudents.noStudentsFound")}
//                   </p>
//                 )}
//               </TabsContent>
//             )
//           )}
//         </Tabs>
//       )}
//     </div>
//   );
// }
