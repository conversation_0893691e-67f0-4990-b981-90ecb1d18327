import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CalendarIcon, Printer, ChevronUp } from "lucide-react";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

// Add validation interface
interface ValidationErrors {
  teacherName?: boolean;
  subject?: boolean;
  class?: boolean;
  period?: boolean;
  hour?: boolean;
  schoolYear?: boolean;
  lessonTopic?: boolean;
  lessonType?: boolean;
  directorName?: boolean;
  // Add the new textarea fields
  teacherThoughts?: boolean;
  methodsUsed?: boolean;
  changesApplied?: boolean;
  objectivesAchieved?: boolean;
  directorThoughts?: boolean;
  adviceForTeacher?: boolean;
}

export interface LessonObservation {
  schoolName: string;
  teacherName: string;
  subject: string;
  date: Date;
  class: string;
  period: string;
  hour: string;
  schoolYear: string;
  observationType: "regular" | "special";
  observationPurpose: string;
  lessonTopic: string;
  lessonType: string;
  evaluationItems: Array<{
    id: number;
    description: string;
    rating: "good" | "average" | "poor" | null;
    notes: string;
  }>;
  teacherThoughts: string;
  methodsUsed: string;
  changesApplied: string;
  objectivesAchieved: string;
  directorThoughts: string;
  adviceForTeacher: string;
  directorName: string;
  directorSignature: string;
  teacherSignature: string;
}

export interface LessonObservationFormProps {
  initialData?: LessonObservation | null;
  onSubmit: (data: LessonObservation) => void;
  onSaveDraft?: (data: LessonObservation) => void;
  selectedTeacher?: any;
  hideActions?: boolean;
  formId?: string;
  onCancel?: () => void;
}

export default function LessonObservationForm({
  initialData,
  onSubmit,
  onSaveDraft,
  selectedTeacher,
  hideActions = false, // Default to showing actions
  formId = "lesson-observation-form",
  onCancel,
}: LessonObservationFormProps) {
  const { t } = useTranslation();
  const [date, setDate] = useState<Date>(new Date());
  const formRef = useRef<HTMLDivElement>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  // Add validation state
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {}
  );

  // Add validation helper functions
  const setValidationError = (
    field: keyof ValidationErrors,
    hasError: boolean
  ) => {
    setValidationErrors((prev) => ({
      ...prev,
      [field]: hasError,
    }));
  };

  const clearValidationErrors = () => {
    setValidationErrors({});
  };

  const [observation, setObservation] = useState<LessonObservation>(
    initialData || {
      schoolName: "SHFMU ELENA GJIKA  - PRISHTINE",
      teacherName: selectedTeacher ? selectedTeacher.name : "",
      subject: selectedTeacher ? selectedTeacher.subject : "",
      date: new Date(),
      class: "",
      period: "",
      hour: "",
      schoolYear: "",
      observationType: "regular",
      observationPurpose: "",
      lessonTopic: "",
      lessonType: "",
      evaluationItems: [
        {
          id: 1,
          description: "Mësimi mbahet në kabinet - klasë",
          rating: null,
          notes: "",
        },
        {
          id: 2,
          description: "Fillimi i orës mësimore",
          rating: null,
          notes: "",
        },
        {
          id: 3,
          description: "Qasja dhe komunikimi i mësimdhënësit me nxënës",
          rating: null,
          notes: "",
        },
        {
          id: 4,
          description: "Përdorimi i reformave, metodologjisë e mësimdhënies",
          rating: null,
          notes: "",
        },
        {
          id: 5,
          description: "I përfshijnë të gjithë nxënësit gjatë procesit mësimor",
          rating: null,
          notes: "",
        },
        {
          id: 6,
          description: "Përdorë mjetet e konkretizimit që ka shkolla",
          rating: null,
          notes: "",
        },
        {
          id: 7,
          description: "Përgatit vet mjetet e konkretizimit",
          rating: null,
          notes: "",
        },
        { id: 8, description: "Punon në grupe", rating: null, notes: "" },
        {
          id: 9,
          description:
            "Drejtori dhe organizon klasën si duhet - disiplina dhe qetësia",
          rating: null,
          notes: "",
        },
        {
          id: 10,
          description: "Shfrytëzon tabelën - përdorë shkumësa me ngjyrë",
          rating: null,
          notes: "",
        },
        {
          id: 11,
          description: "Dhënja e njohurive të reja",
          rating: null,
          notes: "",
        },
        {
          id: 12,
          description: "Funksionimi i punës së pavarur të nxënësve",
          rating: null,
          notes: "",
        },
        {
          id: 13,
          description:
            "Përdorë pyetje të nivelit të lartë, të të menduarit kritik",
          rating: null,
          notes: "",
        },
        {
          id: 14,
          description: "Kontrollimi i përvetësimit të njohurive të nxënësve",
          rating: null,
          notes: "",
        },
        {
          id: 15,
          description: "Ka përgatitjen me shkrim për orë mësimore",
          rating: null,
          notes: "",
        },
        {
          id: 16,
          description: "I përmbahet përfundimeve të orës mësimore",
          rating: null,
          notes: "",
        },
        {
          id: 17,
          description: "Ka të vendosura punime e nxënësve në klasë",
          rating: null,
          notes: "",
        },
        {
          id: 18,
          description: "Jep detyrat e shtëpisë me kohë",
          rating: null,
          notes: "",
        },
        {
          id: 19,
          description: "Notis nxënësit për mësim, përdorë lavdatat",
          rating: null,
          notes: "",
        },
      ],
      teacherThoughts: "",
      methodsUsed: "",
      changesApplied: "",
      objectivesAchieved: "",
      directorThoughts: "",
      adviceForTeacher: "",
      directorName: "",
      directorSignature: "",
      teacherSignature: "",
    }
  );

  useEffect(() => {
    if (selectedTeacher) {
      setObservation((prev) => ({
        ...prev,
        teacherName: selectedTeacher.name,
        subject: selectedTeacher.subject,
      }));
    }
  }, [selectedTeacher]);

  const handleRatingChange = (
    id: number,
    value: "good" | "average" | "poor"
  ) => {
    setObservation((prev) => ({
      ...prev,
      evaluationItems: prev.evaluationItems.map((item) =>
        item.id === id ? { ...item, rating: value } : item
      ),
    }));
  };

  const handleNotesChange = (id: number, notes: string) => {
    setObservation((prev) => ({
      ...prev,
      evaluationItems: prev.evaluationItems.map((item) =>
        item.id === id ? { ...item, notes } : item
      ),
    }));
  };

  const handleInputChange = (
    field: keyof LessonObservation,
    value: string | Date
  ) => {
    setObservation((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Track scroll position to show/hide scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      if (formRef.current) {
        setShowScrollTop(formRef.current.scrollTop > 300);
      }
    };

    const formElement = formRef.current;
    if (formElement) {
      formElement.addEventListener("scroll", handleScroll);
      return () => formElement.removeEventListener("scroll", handleScroll);
    }
  }, []);

  const scrollToTop = () => {
    formRef.current?.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous errors
    clearValidationErrors();

    // Validate form
    let isValid = true;

    // Required fields validation
    if (!observation.teacherName.trim()) {
      setValidationError("teacherName", true);
      isValid = false;
    }

    if (!observation.subject.trim()) {
      setValidationError("subject", true);
      isValid = false;
    }

    if (!observation.class.trim()) {
      setValidationError("class", true);
      isValid = false;
    }

    if (!observation.period.trim()) {
      setValidationError("period", true);
      isValid = false;
    }

    if (!observation.hour.trim()) {
      setValidationError("hour", true);
      isValid = false;
    }

    if (!observation.schoolYear.trim()) {
      setValidationError("schoolYear", true);
      isValid = false;
    }

    if (!observation.lessonTopic.trim()) {
      setValidationError("lessonTopic", true);
      isValid = false;
    }

    if (!observation.lessonType.trim()) {
      setValidationError("lessonType", true);
      isValid = false;
    }

    if (!observation.directorName.trim()) {
      setValidationError("directorName", true);
      isValid = false;
    }

    if (!observation.teacherThoughts.trim()) {
      setValidationError("teacherThoughts", true);
      isValid = false;
    }

    if (!observation.methodsUsed.trim()) {
      setValidationError("methodsUsed", true);
      isValid = false;
    }

    if (!observation.changesApplied.trim()) {
      setValidationError("changesApplied", true);
      isValid = false;
    }

    if (!observation.objectivesAchieved.trim()) {
      setValidationError("objectivesAchieved", true);
      isValid = false;
    }

    if (!observation.directorThoughts.trim()) {
      setValidationError("directorThoughts", true);
      isValid = false;
    }

    if (!observation.adviceForTeacher.trim()) {
      setValidationError("adviceForTeacher", true);
      isValid = false;
    }

    // Only submit if valid
    if (isValid) {
      onSubmit(observation);
    } else {
      // Scroll to the top to show errors
      formRef.current?.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    }
  };

  const handleSaveDraft = () => {
    if (onSaveDraft) {
      // For drafts, we save without validation
      onSaveDraft(observation);
    }
  };

  // Add a print-specific version of the form
  const handlePrint = () => {
    // Get the current form data
    const formData = { ...observation };

    // Store it in localStorage for the print page
    localStorage.setItem("printFormData", JSON.stringify(formData));

    // Open a dedicated print page
    window.open("/print-observation-form", "_blank");
  };

  return (
    <div className="relative w-full h-full">
      <div
        ref={formRef}
        className="bg-white rounded-md shadow-sm p-6 h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide print-container"
      >
        <form id={formId} onSubmit={handleSubmit} className="pb-20 print-form">
          {!hideActions && (
            <div className="flex justify-between items-center mb-6 border-b pb-4 sticky top-0 bg-white z-10 no-print">
              <h1 className="text-2xl font-bold">
                {t("lessonObservation.formTitle")}
              </h1>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrint}
                  className="flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  {t("lessonObservation.printForm")}
                </Button>
                {onSaveDraft && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSaveDraft}
                    className="border-purple-300 text-purple-700 hover:bg-purple-50"
                  >
                    {t("lessonObservation.saveDraft")}
                  </Button>
                )}
                <Button
                  type="submit"
                  className="bg-purple-600 hover:bg-purple-700"
                  onClick={(e) => {
                    e.preventDefault();
                    handleSubmit(e);
                  }}
                >
                  {t("lessonObservation.saveForm")}
                </Button>
              </div>
            </div>
          )}

          {!hideActions && (
            <div className="mb-6 text-center">
              <h2 className="text-lg font-semibold">
                {observation.schoolName}
              </h2>
            </div>
          )}

          {/* Section 1: Basic Information */}
          <div className="mb-8">
            <div className="text-center mb-4">
              <h2 className="text-lg font-semibold">
                {observation.schoolName}
              </h2>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-medium mb-10 text-center">
                {t("lessonObservation.formTitle")}
              </h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <Label htmlFor="teacherName" className="pb-2">
                  {t("lessonObservation.teacherName")}*
                </Label>
                <Input
                  id="teacherName"
                  placeholder={t("lessonObservation.teacherNamePlaceholder")}
                  value={observation.teacherName}
                  onChange={(e) =>
                    handleInputChange("teacherName", e.target.value)
                  }
                  className={
                    validationErrors.teacherName
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.teacherName && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.teacherNameRequired")}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="subject" className="pb-2">
                  {t("lessonObservation.subject")}*
                </Label>
                <Input
                  id="subject"
                  placeholder={t("lessonObservation.subjectPlaceholder")}
                  value={observation.subject}
                  onChange={(e) => handleInputChange("subject", e.target.value)}
                  className={
                    validationErrors.subject
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.subject && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.subjectRequired")}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div>
                <Label htmlFor="date" className="pb-2">
                  {t("lessonObservation.date")}:
                </Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? (
                        format(date, "PPP")
                      ) : (
                        <span> {t("lessonObservation.pickDate")}</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={(date) => {
                        if (date) {
                          setDate(date);
                          handleInputChange("date", date);
                        }
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div>
                <Label htmlFor="class" className="pb-2">
                  {t("lessonObservation.class")}*
                </Label>
                <Input
                  id="class"
                  placeholder={t("lessonObservation.classPlaceholder")}
                  value={observation.class}
                  onChange={(e) => handleInputChange("class", e.target.value)}
                  className={
                    validationErrors.class
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.class && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.classRequired")}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="hour" className="pb-2">
                  {t("lessonObservation.hour")}*
                </Label>
                <Input
                  id="hour"
                  type="time"
                  value={observation.hour}
                  onChange={(e) => handleInputChange("hour", e.target.value)}
                  className={
                    validationErrors.hour
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.hour && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.hourRequired")}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="period" className="pb-2">
                  {t("lessonObservation.period")}*
                </Label>
                <Input
                  id="period"
                  placeholder={t("lessonObservation.periodPlaceholder")}
                  value={observation.period}
                  onChange={(e) => handleInputChange("period", e.target.value)}
                  className={
                    validationErrors.period
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.period && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.periodRequired")}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <Label htmlFor="schoolYear" className="pb-2">
                  {t("lessonObservation.schoolYear")}*
                </Label>
                <Input
                  id="schoolYear"
                  placeholder={t("lessonObservation.schoolYearPlaceholder")}
                  value={observation.schoolYear}
                  onChange={(e) =>
                    handleInputChange("schoolYear", e.target.value)
                  }
                  className={
                    validationErrors.schoolYear
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.schoolYear && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.schoolYearRequired")}
                  </p>
                )}
              </div>
              <div>
                <Label className="block pb-5">
                  {t("lessonObservation.observationType")}:
                </Label>
                <RadioGroup
                  value={observation.observationType}
                  onValueChange={(value: "regular" | "special") =>
                    handleInputChange("observationType", value)
                  }
                  className="flex space-x-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="regular" id="regular" />
                    <Label htmlFor="regular">
                      {t("lessonObservation.regular")}
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="special" id="special" />
                    <Label htmlFor="special">
                      {t("lessonObservation.special")}
                    </Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            <div className="mb-6">
              <Label htmlFor="observationPurpose" className="block mb-2">
                {t("lessonObservation.observationPurpose")}:
              </Label>
              <Textarea
                id="observationPurpose"
                placeholder={t(
                  "lessonObservation.observationPurposePlaceholder"
                )}
                value={observation.observationPurpose}
                onChange={(e) =>
                  handleInputChange("observationPurpose", e.target.value)
                }
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <Label htmlFor="lessonTopic" className="block mb-2">
                  {t("lessonObservation.lessonTopic")}*
                </Label>
                <Input
                  id="lessonTopic"
                  placeholder={t("lessonObservation.lessonTopicPlaceholder")}
                  value={observation.lessonTopic}
                  onChange={(e) =>
                    handleInputChange("lessonTopic", e.target.value)
                  }
                  className={
                    validationErrors.lessonTopic
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.lessonTopic && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.lessonTopicRequired")}
                  </p>
                )}
              </div>
              <div>
                <Label htmlFor="lessonType" className="block mb-2">
                  {t("lessonObservation.lessonType")}*
                </Label>
                <Input
                  id="lessonType"
                  placeholder={t("lessonObservation.lessonTypePlaceholder")}
                  value={observation.lessonType}
                  onChange={(e) =>
                    handleInputChange("lessonType", e.target.value)
                  }
                  className={
                    validationErrors.lessonType
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.lessonType && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationObservation.lessonTypeRequired")}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Section 2: Evaluation Items */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">
              {t("lessonObservation.evaluationItems")}
            </h3>
            <div className="border rounded-md overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                      Nr.
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ECURIA E ORËS MËSIMORE
                    </th>
                    <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      {t("lessonObservation.rating.good")}
                    </th>
                    <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      {t("lessonObservation.rating.average")}
                    </th>
                    <th className="px-4 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
                      {t("lessonObservation.rating.poor")}
                    </th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t("lessonObservation.notes")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {observation.evaluationItems.map((item) => (
                    <tr key={`evaluation-item-${item.id}`}>
                      <td className="px-4 py-2">{item.id}</td>
                      <td className="px-4 py-2">{item.description}</td>
                      <td className="px-4 py-2 text-center">
                        <button
                          type="button"
                          onClick={() => handleRatingChange(item.id, "good")}
                          className={`size-5 rounded-full border ${
                            item.rating === "good"
                              ? "bg-green-500 border-green-500"
                              : "border-gray-300 hover:border-green-300"
                          }`}
                          aria-label="Good"
                        />
                      </td>
                      <td className="px-4 py-2 text-center">
                        <button
                          type="button"
                          onClick={() => handleRatingChange(item.id, "average")}
                          className={`size-5 rounded-full border ${
                            item.rating === "average"
                              ? "bg-yellow-500 border-yellow-500"
                              : "border-gray-300 hover:border-yellow-300"
                          }`}
                          aria-label="Average"
                        />
                      </td>
                      <td className="px-4 py-2 text-center">
                        <button
                          type="button"
                          onClick={() => handleRatingChange(item.id, "poor")}
                          className={`size-5 rounded-full border ${
                            item.rating === "poor"
                              ? "bg-red-500 border-red-500"
                              : "border-gray-300 hover:border-red-300"
                          }`}
                          aria-label="Poor"
                        />
                      </td>
                      <td className="px-4 py-2">
                        <Input
                          value={item.notes || ""}
                          onChange={(e) =>
                            handleNotesChange(item.id, e.target.value)
                          }
                          placeholder={t("lessonObservation.notesPlaceholder")}
                          className="w-full"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Section 3: Teacher Feedback */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">
              {t("lessonObservation.teacherFeedback")}
            </h3>

            <div className="mb-6">
              <Label htmlFor="teacherThoughts" className="block mb-2">
                {t("lessonObservation.teacherThoughts")}*
              </Label>
              <Textarea
                id="teacherThoughts"
                placeholder={t("lessonObservation.teacherThoughtsPlaceholder")}
                value={observation.teacherThoughts}
                onChange={(e) =>
                  handleInputChange("teacherThoughts", e.target.value)
                }
                className={
                  validationErrors.teacherThoughts
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
                rows={4}
              />
              {validationErrors.teacherThoughts && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationObservation.teacherThoughtsRequired")}
                </p>
              )}
            </div>

            <div className="mb-6">
              <Label htmlFor="methodsUsed" className="block mb-2">
                {t("lessonObservation.methodsUsed")}*
              </Label>
              <Textarea
                id="methodsUsed"
                placeholder={t("lessonObservation.methodsUsedPlaceholder")}
                value={observation.methodsUsed}
                onChange={(e) =>
                  handleInputChange("methodsUsed", e.target.value)
                }
                className={
                  validationErrors.methodsUsed
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
                rows={4}
              />
              {validationErrors.methodsUsed && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationObservation.methodsUsedRequired")}
                </p>
              )}
            </div>

            <div className="mb-6">
              <Label htmlFor="changesApplied" className="block mb-2">
                {t("lessonObservation.changesApplied")}*
              </Label>
              <Textarea
                id="changesApplied"
                placeholder={t("lessonObservation.changesAppliedPlaceholder")}
                value={observation.changesApplied}
                onChange={(e) =>
                  handleInputChange("changesApplied", e.target.value)
                }
                className={
                  validationErrors.changesApplied
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
                rows={4}
              />
              {validationErrors.changesApplied && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationObservation.changesAppliedRequired")}
                </p>
              )}
            </div>

            <div className="mb-6">
              <Label htmlFor="objectivesAchieved" className="block mb-2">
                {t("lessonObservation.objectivesAchieved")}*
              </Label>
              <Textarea
                id="objectivesAchieved"
                placeholder={t(
                  "lessonObservation.objectivesAchievedPlaceholder"
                )}
                value={observation.objectivesAchieved}
                onChange={(e) =>
                  handleInputChange("objectivesAchieved", e.target.value)
                }
                className={
                  validationErrors.objectivesAchieved
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
                rows={4}
              />
              {validationErrors.objectivesAchieved && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationObservation.objectivesAchievedRequired")}
                </p>
              )}
            </div>
          </div>

          {/* Section 4: Director's Feedback */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">
              {t("lessonObservation.directorFeedbackTitle")}
            </h3>

            <div className="mb-6">
              <Label htmlFor="directorThoughts" className="block mb-2">
                l{t("lessonObservation.directorThoughts")}*
              </Label>
              <Textarea
                id="directorThoughts"
                placeholder={t("lessonObservation.directorThoughtsPlaceholder")}
                value={observation.directorThoughts}
                onChange={(e) =>
                  handleInputChange("directorThoughts", e.target.value)
                }
                className={
                  validationErrors.directorThoughts
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
                rows={4}
              />
              {validationErrors.directorThoughts && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationObservation.directorThoughtsRequired")}
                </p>
              )}
            </div>

            <div className="mb-6">
              <Label htmlFor="adviceForTeacher" className="block mb-2">
                {t("lessonObservation.adviceForTeacher")}*
              </Label>
              <Textarea
                id="adviceForTeacher"
                placeholder={t("lessonObservation.adviceForTeacherPlaceholder")}
                value={observation.adviceForTeacher}
                onChange={(e) =>
                  handleInputChange("adviceForTeacher", e.target.value)
                }
                className={
                  validationErrors.adviceForTeacher
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
                rows={4}
              />
              {validationErrors.adviceForTeacher && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationObservation.adviceForTeacherRequired")}
                </p>
              )}
            </div>
          </div>

          {/* Section 5: Signatures */}
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">
              {t("lessonObservation.signaturesTitle")}
            </h3>

            <div className="grid grid-cols-2 gap-6">
              <div>
                <p className="font-medium mb-2">
                  {t("lessonObservation.directorLabel")}:
                </p>
                <Input
                  id="directorName"
                  placeholder={t("lessonObservation.directorNamePlaceholder")}
                  value={observation.directorName}
                  onChange={(e) =>
                    handleInputChange("directorName", e.target.value)
                  }
                  className="mb-2"
                />
                <p className="text-sm mb-1">
                  {t("lessonObservation.directorSignatureLabel")}:
                </p>
                <Input
                  id="directorSignature"
                  placeholder={t("lessonObservation.signaturePlaceholder")}
                  value={observation.directorSignature}
                  onChange={(e) =>
                    handleInputChange("directorSignature", e.target.value)
                  }
                />
              </div>

              <div>
                <p className="font-medium mb-2">
                  {t("lessonObservation.teacherLabel")}:
                </p>
                <Input
                  id="teacherName"
                  placeholder={t("lessonObservation.teacherNamePlaceholder")}
                  value={observation.teacherName}
                  onChange={(e) =>
                    handleInputChange("teacherName", e.target.value)
                  }
                  className="mb-2"
                  readOnly
                />
                <p className="text-sm mb-1">
                  {t("lessonObservation.teacherSignatureLabel")}:
                </p>
                <Input
                  id="teacherSignature"
                  placeholder={t("lessonObservation.signaturePlaceholder")}
                  value={observation.teacherSignature}
                  onChange={(e) =>
                    handleInputChange("teacherSignature", e.target.value)
                  }
                />
              </div>
            </div>

            <div className="mt-4 text-center">
              <p className="font-medium mb-2">
                {t("lessonObservation.observedOnLabel")}:
              </p>
              <p className="text-lg">
                {format(observation.date, "dd/MM/yyyy")}
              </p>
            </div>
          </div>
        </form>
      </div>

      {!hideActions && (
        <div className="mt-6 flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel || (() => {})}
          >
            {t("lessonObservation.cancel")}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleSaveDraft}
            className="border-purple-300 text-purple-700 hover:bg-purple-50"
          >
            {t("lessonObservation.saveDraft")}
          </Button>
          <Button
            type="button"
            onClick={(e) => handleSubmit(e)}
            className="bg-purple-600 hover:bg-purple-700"
          >
            {t("lessonObservation.saveForm")}
          </Button>
        </div>
      )}
      {showScrollTop && (
        <Button
          type="button"
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-2 rounded-full bg-purple-600 hover:bg-purple-700 text-white shadow-md"
          size="icon"
        >
          <ChevronUp className="h-5 w-5" />
        </Button>
      )}
    </div>
  );
}
