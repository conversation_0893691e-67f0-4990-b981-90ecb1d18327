import { useTranslation } from "react-i18next";
import { useTypedAuthUser } from "../../hooks/useAuth";
import { SlGraduation } from "react-icons/sl";
import { FaCheckCircle, FaCalendarAlt, FaMedal } from "react-icons/fa";
import AccordionCard from "@/components/AccordionCard";
import KeyFeatures from "@/components/KeyFeaturesCard";
import Footer from "@/components/Footer";
import PublicHeader from "@/components/PublicHeader";

export default function WelcomePage() {
  const { t } = useTranslation();
  const user = useTypedAuthUser();

  const handleGetStarted = () => {
    if (user) {
      window.open("/dashboard", "_blank");
    } else {
      window.open("/login", "_blank");
    }
  };

  return (
    <main className="overflow-x-hidden cursor-default">
      <PublicHeader />
      {/* Hero Section */}
      <section className="w-full bg-gradient-to-r from-purple-600 to-purple-800 text-white">
        <div className="container mx-auto px-4 md:px-8 lg:px-20 py-12 md:py-16 lg:py-20 flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-12">
          <div className="w-full lg:max-w-xl">
            <div className="bg-white/20 text-sm inline-block px-4 py-1 rounded-full mb-4">
              {t("welcomePage.tagline")}
            </div>

            <p className="text-base md:text-lg mb-6 text-white/90">
              {t("welcomePage.description")}
            </p>
            <div className="flex flex-wrap gap-4">
              <button
                onClick={handleGetStarted}
                className="bg-white text-purple-700 px-6 py-2 rounded font-semibold shadow cursor-pointer"
              >
                {t("welcomePage.getStarted")}
              </button>
              <button className="border border-white px-6 py-2 rounded font-semibold">
                {t("welcomePage.learnMore")}
              </button>
            </div>
          </div>

          <div className="relative w-full max-w-[250px] md:max-w-[350px] lg:max-w-[400px] h-[250px] md:h-[350px] lg:h-[400px] mt-8 lg:mt-0 mx-auto lg:mx-0">
            <div className="absolute inset-0 bg-white/10 rounded-full flex items-center justify-center">
              <SlGraduation className="text-white text-6xl md:text-7xl lg:text-9xl" />
            </div>
            <div className="absolute top-0 right-0 bg-orange-500 p-3 rounded-xl animate-float">
              <FaMedal className="text-white w-6 h-6 md:w-8 md:h-8" />
            </div>
            <div className="absolute top-1/4 left-0 bg-purple-500 p-3 rounded-xl animate-float">
              <FaCheckCircle className="text-white w-6 h-6 md:w-8 md:h-8" />
            </div>
            <div className="absolute bottom-0 left-1/3 bg-green-500 p-3 rounded-xl animate-float">
              <FaCalendarAlt className="text-white w-6 h-6 md:w-8 md:h-8" />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <KeyFeatures
        title={t("welcomePage.keyFeatures")}
        description={t("welcomePage.featuresDescription")}
      />

      {/* CTA Section */}
      <section className="w-full bg-gradient-to-r from-purple-600 to-purple-800 text-white py-12 md:py-16 lg:py-20">
        <div className="container mx-auto px-4 md:px-8 text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-4">
            {t("welcomePage.ctaTitle")}
          </h2>
          <p className="max-w-2xl mx-auto mb-6 md:mb-8 text-sm md:text-base">
            {t("welcomePage.ctaDescription")}
          </p>
          <button
            onClick={handleGetStarted}
            className="bg-white text-blue-900 px-5 py-2 md:px-6 md:py-3 rounded font-semibold hover:bg-gray-100 transition cursor-pointer"
          >
            {t("welcomePage.getStartedNow")}
          </button>
        </div>
      </section>

      <AccordionCard />

      <Footer />
    </main>
  );
}
