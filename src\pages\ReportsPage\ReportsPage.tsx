import { useState } from "react";
import {
  Calendar as CalendarIcon,
  Download,
  Printer,
  FileText,
} from "lucide-react";
import { format } from "date-fns";
import { useTranslation } from "react-i18next";

import { Calendar } from "@/components/ui/calendar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";

const sampleReports = [
  {
    title: "End of Year Academic Performance Report",
    category: "Performance",
    date: "12/15/2023",
    status: "Final",
    size: "2.4 MB",
  },
  {
    title: "Quarterly Attendance Summary Q3",
    category: "Attendance",
    date: "9/30/2023",
    status: "Final",
    size: "1.8 MB",
  },
  {
    title: "Mid-year Financial Statement",
    category: "Financial",
    date: "7/15/2023",
    status: "Draft",
    size: "3.2 MB",
  },
  {
    title: "Teacher Evaluation Report",
    category: "Personnel",
    date: "6/10/2023",
    status: "Final",
    size: "1.5 MB",
  },
  {
    title: "Student Progress Summary - Grade 10",
    category: "Performance",
    date: "5/20/2023",
    status: "Final",
    size: "4.1 MB",
  },
];

export default function ReportsPage() {
  const { t } = useTranslation();
  const [selectedType, setSelectedType] = useState(t("reports.types.academic"));
  const [selectedPeriod, setSelectedPeriod] = useState(
    t("reports.periods.currentSemester")
  );
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState(t("reports.available.allTypes"));

  const reportTypes = [
    t("reports.types.academic"),
    t("reports.types.attendance"),
    t("reports.types.financial"),
    t("reports.types.personnel"),
  ];

  const periods = [
    t("reports.periods.currentSemester"),
    t("reports.periods.lastSemester"),
    t("reports.periods.currentYear"),
    t("reports.periods.custom"),
  ];

  const filteredReports = sampleReports.filter((report) => {
    const matchesSearch = report.title
      .toLowerCase()
      .includes(searchQuery.toLowerCase());
    const matchesType =
      filterType === t("reports.available.allTypes") ||
      report.category === filterType;
    return matchesSearch && matchesType;
  });

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold">{t("reports.title")}</h1>
          <p className="text-gray-500 pt-1">{t("reports.description")}</p>
        </div>
        <Button className="bg-purple-600 hover:bg-purple-700">
          {t("reports.newReport")}
        </Button>
      </div>

      {/* Report Generator */}
      <div className="bg-white border border-gray-300 p-6 mb-10 rounded-md">
        <h2 className="text-xl font-semibold mb-4">
          {t("reports.generator.title")}
        </h2>
        <p className="text-gray-500 mb-6">
          {t("reports.generator.description")}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("reports.generator.reportType")} />
              </SelectTrigger>
              <SelectContent>
                {reportTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={t("reports.generator.period")} />
              </SelectTrigger>
              <SelectContent>
                {periods.map((period) => (
                  <SelectItem key={period} value={period}>
                    {period}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={`w-full justify-start text-left font-normal ${
                    !selectedDate && "text-muted-foreground"
                  }`}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? (
                    format(selectedDate, "PPP")
                  ) : (
                    <span>{t("reports.generator.customDate")}</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={setSelectedDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          <Button className="bg-purple-600 hover:bg-purple-700">
            {t("reports.generator.generate")}
          </Button>
        </div>
      </div>

      {/* Available Reports */}
      <div className="bg-white border border-gray-300 rounded p-6">
        <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-6">
          <h2 className="text-xl font-semibold mb-4 md:mb-0">
            {t("reports.available.title")}
          </h2>
          <div className="flex gap-4">
            <input
              type="text"
              placeholder={t("reports.available.search")}
              className="border rounded-md px-4 py-2 w-full md:w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-40 border-gray-300">
                <SelectValue placeholder={t("reports.available.filterType")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={t("reports.available.allTypes")}>
                  {t("reports.available.allTypes")}
                </SelectItem>
                {Array.from(new Set(sampleReports.map((r) => r.category))).map(
                  (cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Report List */}
        <div className="divide-y">
          {filteredReports.map((report, idx) => (
            <div key={idx} className="flex justify-between items-center py-4">
              <div className="flex items-center gap-3">
                <FileText className="w-8 h-8 text-gray-500 mr-2" />
                <div>
                  <p className="font-semibold">{report.title}</p>
                  <p className="text-gray-500 text-sm">
                    {report.category} • {report.date} •{" "}
                    {t(
                      `reports.available.status.${report.status.toLowerCase()}`
                    )}{" "}
                    • {report.size}
                  </p>
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  title={t("reports.actions.download")}
                >
                  <Download className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  title={t("reports.actions.print")}
                >
                  <Printer className="w-5 h-5" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
