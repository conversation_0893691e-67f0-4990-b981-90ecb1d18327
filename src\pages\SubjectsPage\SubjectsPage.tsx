import { useTypedAuthUser } from "../../hooks/useAuth";
import StudentSubjects from "./StudentSubjects ";
import TeacherSubjects from "./TeacherSubjects ";
import ParentSubjects from "./ParentSubjects ";
import DirectorSubjects from "./DirectorSubjects ";

export default function SubjectsPage() {
  const authUser = useTypedAuthUser();

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return <StudentSubjects />;
    case "teacher":
      return <TeacherSubjects />;
    case "parent":
      return <ParentSubjects />;
    case "admin":
      return <DirectorSubjects />;
    default:
      console.warn("SubjectsPage: Invalid or unknown role", {
        originalRole: authUser?.role,
        normalizedRole,
      });
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Invalid Role
          </h2>
          <p className="text-gray-600">
            Your account role "{authUser?.role}" is not recognized. Please
            contact support.
          </p>
        </div>
      );
  }
}
