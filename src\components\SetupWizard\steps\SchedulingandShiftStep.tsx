import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Clock,
  Plus,
  X,
  CheckCircle,
  AlertCircle,
  Trash2,
  Edit,
  Save,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import toast from "react-hot-toast";
import { useSetupWizard } from "../SetupWizardContext";

interface Shift {
  id: string;
  number: number;
  name: string;
  startTime: string;
  maxClasses: number;
}

interface GeneralShiftInfo {
  shortBreakLength: number;
  longBreakLength: number;
  longBreakLocation: number;
}

export default function SchedulingandShiftStep() {
  const { t } = useTranslation();
  const {
    setupData,
    updateShifts,
    validationErrors,
    setValidationError,
    clearValidationErrors,
  } = useSetupWizard();

  const [generalInfo, setGeneralInfo] = useState<GeneralShiftInfo>(
    setupData.shifts.generalInfo || {
      shortBreakLength: 5,
      longBreakLength: 15,
      longBreakLocation: 3,
    }
  );
  const [shifts, setShifts] = useState<Shift[]>(
    setupData.shifts.shiftList || []
  );
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingShiftId, setEditingShiftId] = useState<string | null>(null);
  const [newShift, setNewShift] = useState<Shift>({
    id: Date.now().toString(),
    number: shifts.length + 1,
    name: `Shift ${shifts.length + 1}`,
    startTime: "",
    maxClasses: 1,
  });
  const [editingShift, setEditingShift] = useState<Shift | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [shiftToDelete, setShiftToDelete] = useState<string | null>(null);

  useEffect(() => {
    if (shifts.length > 0) {
      setValidationError("shiftList", false);
    }
  }, [shifts]);

  useEffect(() => {
    setNewShift((prev) => ({
      ...prev,
      number: shifts.length + 1,
      name: `Shift ${shifts.length + 1}`,
    }));
  }, [shifts.length]);

  const handleGeneralInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numericValue = parseInt(value) || 0;
    setGeneralInfo((prev) => ({ ...prev, [name]: numericValue }));

    if (value.trim() !== "") {
      setValidationError(name, false);
    }
  };

  const handleShiftInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name === "maxClasses") {
      const numericValue = parseInt(value) || 1;
      setNewShift((prev) => ({ ...prev, [name]: numericValue }));
    } else {
      setNewShift((prev) => ({ ...prev, [name]: value }));
    }

    if (value.trim() !== "") {
      setValidationError(name, false);
      // Also clear duplicate time error when user changes start time
      if (name === "startTime") {
        setValidationError("duplicateShiftTime", false);
      }
    }
  };

  const handleAddShift = () => {
    clearValidationErrors();
    let isValid = true;

    if (!newShift.name.trim()) {
      setValidationError("name", true);
      isValid = false;
    }

    if (!newShift.startTime) {
      setValidationError("startTime", true);
      isValid = false;
    }

    // Check for duplicate shift times
    const isDuplicateTime = shifts.some(
      (existingShift) => existingShift.startTime === newShift.startTime
    );

    if (isDuplicateTime) {
      setValidationError("duplicateShiftTime", true);
      isValid = false;
    }

    if (newShift.maxClasses < 1) {
      setValidationError("maxClasses", true);
      isValid = false;
    }

    if (!isValid) return;

    const newShiftWithId = { ...newShift, id: Date.now().toString() };
    const updatedShifts = [...shifts, newShiftWithId];

    setShifts(updatedShifts);

    updateShifts({
      generalInfo,
      shiftList: updatedShifts,
    });

    setValidationError("shiftList", false);

    setNewShift({
      id: Date.now().toString(),
      number: updatedShifts.length + 1,
      name: `Shift ${updatedShifts.length + 1}`,
      startTime: "",
      maxClasses: 1,
    });
    setShowAddForm(false);
    toast.success(t("shiftMessages.shiftAdded"));
  };

  const handleCancel = () => {
    setShowAddForm(false);
    clearValidationErrors();
    setNewShift({
      id: Date.now().toString(),
      number: shifts.length + 1,
      name: `Shift ${shifts.length + 1}`,
      startTime: "",
      maxClasses: 1,
    });
  };

  const handleDeleteShift = (id: string) => {
    setShiftToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteShift = () => {
    if (shiftToDelete) {
      const updatedShifts = shifts.filter(
        (shift) => shift.id !== shiftToDelete
      );
      // Renumber the remaining shifts
      const renumberedShifts = updatedShifts.map((shift, index) => ({
        ...shift,
        number: index + 1,
        name: shift.name.startsWith("Shift ")
          ? `Shift ${index + 1}`
          : shift.name,
      }));

      setShifts(renumberedShifts);
      updateShifts({
        generalInfo,
        shiftList: renumberedShifts,
      });
      toast.success(t("shiftMessages.shiftRemoved"));
      setDeleteDialogOpen(false);
      setShiftToDelete(null);
    }
  };

  const cancelDeleteShift = () => {
    setDeleteDialogOpen(false);
    setShiftToDelete(null);
  };

  const handleEditShift = (shift: Shift) => {
    setEditingShiftId(shift.id);
    setEditingShift({ ...shift });
    clearValidationErrors();
  };

  const handleCancelEdit = () => {
    setEditingShiftId(null);
    setEditingShift(null);
    clearValidationErrors();
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingShift) return;

    const { name, value } = e.target;
    if (name === "maxClasses") {
      const numericValue = parseInt(value) || 1;
      setEditingShift((prev) =>
        prev ? { ...prev, [name]: numericValue } : null
      );
    } else {
      setEditingShift((prev) => (prev ? { ...prev, [name]: value } : null));
    }

    if (value.trim() !== "") {
      setValidationError(
        `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
        false
      );
      // Also clear duplicate time error when user changes start time
      if (name === "startTime") {
        setValidationError("editDuplicateShiftTime", false);
      }
    }
  };

  const handleSaveEdit = () => {
    if (!editingShift) return;

    clearValidationErrors();
    let isValid = true;

    if (!editingShift.name.trim()) {
      setValidationError("editName", true);
      isValid = false;
    }

    if (!editingShift.startTime) {
      setValidationError("editStartTime", true);
      isValid = false;
    }

    // Check for duplicate shift times (excluding current shift)
    const isDuplicateTime = shifts.some(
      (existingShift) =>
        existingShift.id !== editingShift.id &&
        existingShift.startTime === editingShift.startTime
    );

    if (isDuplicateTime) {
      setValidationError("editDuplicateShiftTime", true);
      isValid = false;
    }

    if (editingShift.maxClasses < 1) {
      setValidationError("editMaxClasses", true);
      isValid = false;
    }

    if (!isValid) return;

    // Update shift
    const updatedShifts = shifts.map((shift) =>
      shift.id === editingShift.id ? editingShift : shift
    );

    setShifts(updatedShifts);
    updateShifts({
      generalInfo,
      shiftList: updatedShifts,
    });

    setEditingShiftId(null);
    setEditingShift(null);
    toast.success(t("shiftMessages.shiftUpdated"));
  };

  const handleGeneralInfoSave = () => {
    updateShifts({
      generalInfo,
      shiftList: shifts,
    });
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Clock className="h-5 w-5 text-purple-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-800">
            {t("setup.shiftsAndScheduling")}
          </h2>
        </div>
      </div>

      {/* General Shift Info Section */}
      <div className="bg-purple-50 rounded-lg border p-5 mb-6">
        <h3 className="text-lg font-medium mb-4">
          {t("setup.generalShiftInfo")}
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div>
            <label
              htmlFor="shortBreakLength"
              className="text-sm font-medium mb-1 block"
            >
              {t("setup.shortBreakLength")}*
            </label>
            <Input
              id="shortBreakLength"
              name="shortBreakLength"
              type="number"
              min="1"
              value={generalInfo.shortBreakLength}
              onChange={handleGeneralInfoChange}
              onBlur={handleGeneralInfoSave}
              placeholder={t("setup.shortBreakLengthPlaceholder")}
              className={
                validationErrors.shortBreakLength
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
            />
            {validationErrors.shortBreakLength && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.shortBreakLengthRequired")}
              </p>
            )}
          </div>
          <div>
            <label
              htmlFor="longBreakLength"
              className="text-sm font-medium mb-1 block"
            >
              {t("setup.longBreakLength")}*
            </label>
            <Input
              id="longBreakLength"
              name="longBreakLength"
              type="number"
              min="1"
              value={generalInfo.longBreakLength}
              onChange={handleGeneralInfoChange}
              onBlur={handleGeneralInfoSave}
              placeholder={t("setup.longBreakLengthPlaceholder")}
              className={
                validationErrors.longBreakLength
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
            />
            {validationErrors.longBreakLength && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.longBreakLengthRequired")}
              </p>
            )}
          </div>
          <div>
            <label
              htmlFor="longBreakLocation"
              className="text-sm font-medium mb-1 block"
            >
              {t("setup.longBreakLocation")}*
            </label>
            <Input
              id="longBreakLocation"
              name="longBreakLocation"
              type="number"
              min="1"
              value={generalInfo.longBreakLocation}
              onChange={handleGeneralInfoChange}
              onBlur={handleGeneralInfoSave}
              className={
                validationErrors.longBreakLocation
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
            />
            <p className="text-xs text-gray-500 mt-1">
              {t("setup.longBreakLocationDesc")}
            </p>
            {validationErrors.longBreakLocation && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.longBreakLocationRequired")}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Shifts Section */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium">{t("setup.shifts")}</h3>
        {!showAddForm && (
          <Button
            onClick={() => setShowAddForm(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white hover:text-white"
            variant="outline"
            size="sm"
          >
            <Plus className="h-4 w-4 mr-1" />
            {t("setup.addNewShift")}
          </Button>
        )}
      </div>

      {showAddForm && (
        <div className="bg-white rounded-lg border p-5 mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">{t("setup.addNewShift")}</h3>
            <button
              onClick={handleCancel}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label
                  htmlFor="number"
                  className="text-sm font-medium mb-1 block"
                >
                  {t("setup.shiftNumber")}
                </label>
                <Input
                  id="number"
                  name="number"
                  value={newShift.number}
                  disabled
                  className="bg-gray-100"
                />
              </div>
              <div>
                <label
                  htmlFor="name"
                  className="text-sm font-medium mb-1 block"
                >
                  {t("setup.shiftName")}*
                </label>
                <Input
                  id="name"
                  name="name"
                  value={newShift.name}
                  onChange={handleShiftInputChange}
                  placeholder="e.g., Morning Shift"
                  className={
                    validationErrors.name
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.name && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationSetup.shiftNameRequired")}
                  </p>
                )}
              </div>
              <div>
                <label
                  htmlFor="startTime"
                  className="text-sm font-medium mb-1 block"
                >
                  {t("setup.startTime")}*
                </label>
                <Input
                  id="startTime"
                  name="startTime"
                  type="time"
                  value={newShift.startTime}
                  onChange={handleShiftInputChange}
                  className={
                    validationErrors.startTime ||
                    validationErrors.duplicateShiftTime
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.startTime && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationSetup.startTimeRequired")}
                  </p>
                )}
                {validationErrors.duplicateShiftTime && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationSetup.duplicateShiftTime")}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="maxClasses"
                className="text-sm font-medium mb-1 block"
              >
                {t("setup.maxClassesInShift")}*
              </label>
              <Input
                id="maxClasses"
                name="maxClasses"
                type="number"
                min="1"
                value={newShift.maxClasses}
                onChange={handleShiftInputChange}
                placeholder={t("setup.maxClassesPlaceholder")}
                className={
                  validationErrors.maxClasses
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
              />
              {validationErrors.maxClasses && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationSetup.maxClassesRequired")}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="text-gray-600"
              >
                {t("setup.cancel")}
              </Button>
              <Button
                onClick={handleAddShift}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {t("setup.addShift")}
              </Button>
            </div>
          </div>
        </div>
      )}

      {shifts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-8">
          {shifts.map((shift) => (
            <div key={shift.id} className="border rounded-md p-4 relative">
              {editingShiftId === shift.id ? (
                // Edit mode
                <div className="space-y-3">
                  <div className="flex justify-end gap-1 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSaveEdit}
                      className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                    >
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCancelEdit}
                      className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div>
                    <Input
                      name="name"
                      value={editingShift?.name || ""}
                      onChange={handleEditInputChange}
                      placeholder={t("setup.shiftName")}
                      className={`text-sm ${
                        validationErrors.editName
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    />
                    {validationErrors.editName && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.shiftNameRequired")}
                      </p>
                    )}
                  </div>

                  <div>
                    <Input
                      name="startTime"
                      type="time"
                      value={editingShift?.startTime || ""}
                      onChange={handleEditInputChange}
                      className={`text-sm ${
                        validationErrors.editStartTime ||
                        validationErrors.editDuplicateShiftTime
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    />
                    {validationErrors.editStartTime && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.startTimeRequired")}
                      </p>
                    )}
                    {validationErrors.editDuplicateShiftTime && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.duplicateShiftTime")}
                      </p>
                    )}
                  </div>

                  <div>
                    <Input
                      name="maxClasses"
                      type="number"
                      min="1"
                      value={editingShift?.maxClasses || 1}
                      onChange={handleEditInputChange}
                      placeholder={t("setup.maxClasses")}
                      className={`text-sm ${
                        validationErrors.editMaxClasses
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    />
                    {validationErrors.editMaxClasses && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.maxClassesRequired")}
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                // Display mode
                <>
                  <div className="flex justify-end gap-1 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditShift(shift)}
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteShift(shift.id)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <h3 className="font-medium text-gray-900">{shift.name}</h3>
                  <p className="text-sm text-gray-500">
                    {t("setup.shiftNumber")}: {shift.number}
                  </p>
                  <p className="text-sm text-gray-500">
                    {t("setup.startTime")}: {shift.startTime}
                  </p>
                  <p className="text-sm text-gray-500">
                    {t("setup.maxClassesInShift")}: {shift.maxClasses}
                  </p>
                </>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div
          className={`border rounded-md p-8 text-center mb-8 ${
            validationErrors.shiftList
              ? "border-red-500 bg-red-50 text-red-600"
              : "text-gray-500"
          }`}
        >
          {validationErrors.shiftList ? (
            <div className="flex flex-col items-center">
              <AlertCircle className="h-8 w-8 text-red-500 mb-2" />
              <p>{t("validationSetup.addAtLeastOneShift")}</p>
            </div>
          ) : (
            t("setup.noShiftsAddedYet")
          )}
        </div>
      )}

      {/* Schedule Planning Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="border rounded-lg p-4 bg-gray-50">
          <h3 className="font-semibold text-lg mb-2">
            {t("setup.studentScheduling")}
          </h3>
          <p className="text-sm text-gray-600 mb-2">
            {t(
              "setup.studentSchedulingDesc",
              "After setup, you can create detailed class schedules for students, including:"
            )}
          </p>
          <ul className="space-y-1 text-sm">
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.classPeriods")}</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.teacherAssignments")}</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.roomLocations")}</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.weeklySchedules")}</span>
            </li>
          </ul>
        </div>

        <div className="border rounded-lg p-4 bg-gray-50">
          <h3 className="font-semibold text-lg mb-2">
            {t("setup.staffScheduling")}
          </h3>
          <p className="text-sm text-gray-600 mb-2">
            {t(
              "setup.staffSchedulingDesc",
              "Teacher and staff schedules can be managed, including:"
            )}
          </p>
          <ul className="space-y-1 text-sm">
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.teachingHours")}</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.classAssignments")}</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.nonTeachingDuties")}</span>
            </li>
            <li className="flex items-start">
              <span className="text-purple-600 mr-2">•</span>
              <span>{t("setup.substitutionManagement")}</span>
            </li>
          </ul>
        </div>
      </div>

      {/* Add a reminder at the bottom of the component */}
      <div className="border rounded-lg p-4 bg-blue-50">
        <div className="flex items-start">
          <CheckCircle className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
          <div>
            <h3 className="font-semibold text-lg text-blue-700">
              {t("setup.setupComplete")}
            </h3>
            <p className="text-sm text-blue-600 mb-2">
              {t(
                "setup.setupCompleteDesc",
                "You've completed all the necessary setup steps for your school. After finishing, you'll be able to:"
              )}
            </p>
            <ul className="space-y-1 text-sm text-blue-600">
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                <span>{t("setup.createSchedules")}</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                <span>{t("setup.manageAttendance")}</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                <span>{t("setup.trackPerformance")}</span>
              </li>
              <li className="flex items-start">
                <span className="text-blue-600 mr-2">•</span>
                <span>{t("setup.generateReports")}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <p className="text-sm text-gray-500 mt-6">
        * {t("setup.requiredFields")}
      </p>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteConfirmation.confirmDeleteShift")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteConfirmation.confirmDeleteShiftMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteShift}>
              {t("deleteConfirmation.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteShift}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("deleteConfirmation.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
