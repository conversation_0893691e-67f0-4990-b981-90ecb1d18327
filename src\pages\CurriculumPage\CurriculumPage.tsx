import { useTypedAuthUser } from "../../hooks/useAuth";
import StudentCurriculumPage from "./StudentCurriculumPage";
import TeacherCurriculumPage from "./TeacherCurriculumPage";
import ParentCurriculumPage from "./ParentCurriculumPage";
import DirectorCurriculumPage from "./DirectorCurriculumPage";

export default function CurriculumPage() {
  const authUser = useTypedAuthUser();

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return <StudentCurriculumPage />;
    case "teacher":
      return <TeacherCurriculumPage />;
    case "parent":
      return <ParentCurriculumPage />;
    case "admin":
      return <DirectorCurriculumPage />;
    default:
      console.warn("CurriculumPage: Invalid or unknown role", {
        originalRole: authUser?.role,
        normalizedRole,
      });
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Invalid Role
          </h2>
          <p className="text-gray-600">
            Your account role "{authUser?.role}" is not recognized. Please
            contact support.
          </p>
        </div>
      );
  }
}
