{"name": "e-ditar", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test:refresh": "node test-refresh-token.js"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.11", "@tailwindcss/vite": "^4.1.4", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.9.4", "i18next": "^25.0.1", "i18next-browser-languagedetector": "^8.0.5", "lucide-react": "^0.503.0", "react": "^19.1.0", "react-auth-kit": "^3.1.3", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.2", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tailwind-scrollbar-hide": "^2.0.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/canvas-confetti": "^1.9.0", "@types/node": "^22.15.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/styled-components": "^5.1.34", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8", "typescript": "~5.8.3", "typescript-eslint": "^8.31.0", "vite": "^6.3.3"}}