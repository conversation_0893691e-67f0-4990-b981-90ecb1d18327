import { useState } from "react";
import { BookOpen, Users } from "lucide-react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import ManageClassDialog from "../ManageClassDialog";

export default function SubjectCard({
  subject,
  isTeacher = false,
}: {
  subject: {
    id: number;
    name: string;
    teacher: string;
    color: string;
    score?: number;
  };
  isTeacher?: boolean;
}) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isManageDialogOpen, setIsManageDialogOpen] = useState(false);

  const handleButtonClick = () => {
    if (isTeacher) {
      // Open dialog for teachers
      setIsManageDialogOpen(true);
    } else {
      // Navigate to subject details page for students
      navigate(`/subjects/${subject.id}`);
    }
  };

  return (
    <>
      <div className="border border-gray-300 rounded-lg shadow-sm overflow-hidden transition hover:shadow-md bg-white ">
        <div
          className={`py-3 px-4 flex flex-row items-center justify-between bg-purple-100 text-purple-700`}
        >
          <h3 className="font-bold">{subject.name}</h3>
          <BookOpen className="h-4 w-4 text-purple-700" />
        </div>
        <div className="pt-4 px-4 pb-2">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-slate-500" />
              <span className="text-sm text-slate-600">
                {isTeacher ? "You are teaching" : "Teacher: " + subject.teacher}
              </span>
            </div>
            <div className="text-sm text-slate-600">
              {isTeacher ? "Class size: 25 students" : "3 upcoming assignments"}
            </div>
          </div>
        </div>
        <div className="px-4 pb-4 pt-2">
          <button
            onClick={handleButtonClick}
            className="w-full border border-gray-300 text-sm py-1.5 rounded-md hover:bg-gray-100 transition cursor-pointer"
          >
            {isTeacher ? t("manageClass") : t("moreDetails")}
          </button>
        </div>
      </div>

      {isTeacher && (
        <ManageClassDialog
          isOpen={isManageDialogOpen}
          onClose={() => setIsManageDialogOpen(false)}
          subject={subject}
        />
      )}
    </>
  );
}
