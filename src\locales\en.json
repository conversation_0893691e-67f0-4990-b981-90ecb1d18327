{"welcome": "Welcome", "welcomeTo": "Welcome to E-ditar", "welcomeDesc": "Sign in to continue to your educational dashboard", "profile": "Profile", "logout": "Log out", "signIn": "Sign In", "signingIn": "Signing in...", "forgotPassword": "Forgot password?", "resetPassword": " Reset Password", "resetDesc": " Enter your email to receive reset instructions", "rememberpassword": "Remember your password?", "backToLogin": " Back to login", "sendResetLink": " Send Reset Link", "continueWith": "  or continue with", "edit": "Edit", "view": "view", "reason": "Reason", "viewSubmissions": "View Submissions", "submitAssignment": "Submit Assignment", "moreDetails": "More Details", "takeQuiz": "Take Quiz", "manageClass": "Manage Class", "allStudents": "All Students", "searchStudents": " Search students...", "fullName": "Full Name", "Role": "Role", "name": "Name", "contact": "Contact", "email": "Email", "password": "Password", "avgGrade": "Avg. Grade", "actions": "Actions", "saveChanges": "Save Changes", "achievements": "Achievements", "learningResources": "learning resources", "viewLess": "View Less", "viewAllgrades": "View All Grades", "viewAllabsences": "View All Absences", "academicPerformance": " Academic Performance", "yourSubjectPerformance": "Your Subject Performance", "yourMonthlyPerformance": "Your Monthly Performance", "yourYearlyPerformance": "Your Yearly Performance", "monthlyPerformance": "Monthly Performance", "subjectPerformance": " Subject Performance", "yearlyComparison": "Yearly Comparison", "upcomingEvents": "Upcoming Events", "signinWithGoogle": "Sign in with Google", "schoolPerformance": "School Performance", "gradePerformance": "Grade Performance", "notFound": "Oops!", "notFoundMessage": "The page you are looking for does not exist.", "goBack": "Go Back", "warnings": {"title": "Student Warnings", "childsWarnings": "Child's Warnings", "myWarnings": "My Warnings", "schoolWarnings": "School Warnings", "classWarnings": "Class Warnings", "addNew": "Add New Warning", "type": "Warning Type", "selectType": "Select type", "subject": "Subject", "subjectPlaceholder": "Subject (optional)", "text": "Warning Text", "date": "Warning Date", "action": "Action", "textPlaceholder": "Describe the warning...", "submit": "Submit Warning", "list": "Warning List", "filter": "Filter", "all": "All Types", "noWarnings": "No warnings found", "textRequired": "Warning text is required", "typeRequired": "Warning type is required", "addSuccess": "Warning added successfully", "addError": "Failed to add warning", "deleteSuccess": "Warning removed successfully", "teacher": "Teacher", "description": "Add and manage student warnings for behavior, academic, and attendance issues.", "parentDescription": "View warnings issued to your child.", "schoolDescription": "View warnings issued to students in your school.", "studentDescription": "View warnings issued to you.", "teacherDescription": "View warnings issued to students in your classes."}, "sidebar": {"dashboard": "Dashboard", "schedule": "Schedule", "analytics": "Analytics", "staffMembers": "Staff Members", "reports": "Reports", "subjects": "Subjects & Topics", "assignments": "Assignments", "grades": "Grades", "attendance": "Attendance", "studentmanagement": "Student Management", "messages": "Messages", "settings": "Settings", "manageTopics": "Manage Topics", "childSchedule": "Child's Schedule", "gradeStudents": "Grade Students", "classSchedule": "Class Schedule", "curriculum": "Curriculum", "lessonObservation": "Lesson Observation", "assistant": "AI Assistant"}, "welcomePage": {"home": "Home", "login": "Log In", "tagline": "Modern School Management System", "description": "A comprehensive platform for students, teachers, parents, and school directors to manage education efficiently.", "getStarted": "Get Started", "learnMore": "Learn More", "keyFeatures": "Key Features", "featuresDescription": "E-ditar offers a complete solution for educational management with role-based access and powerful features.", "learnMoreLink": "Learn more", "ctaTitle": "Ready to transform your educational experience?", "ctaDescription": "Join thousands of schools already using E-ditar to improve learning outcomes and efficiency.", "getStartedNow": "Get Started Now", "copyright": "E-ditar Learning Hub. All rights reserved.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "aboutUs": "About", "contactUs": "Contact", "featuresTitle": "Features", "pricing": "Pricing", "features": {"dashboards": {"title": "Role-Based Dashboards", "description": "Customized interfaces for students, teachers, parents, and directors with relevant information."}, "timetable": {"title": "Smart Timetable System", "description": "Interactive weekly schedule with class details, assignments, and test reminders."}, "grades": {"title": "Grade Management", "description": "Comprehensive grading system with progress tracking, reports, and analytics."}, "attendance": {"title": "Attendance Tracking", "description": "Real-time attendance management with notifications and historical reports."}, "communication": {"title": "Communication Hub", "description": "Seamless interaction between students, teachers, and parents through messaging."}, "ai": {"title": "AI Learning Assistant", "description": "Smart recommendations for study materials and personalized learning paths."}}, "faqTitle": "Frequently Asked Questions", "faqDescription": "Find answers to common questions about E-ditar and how it can help your school.", "faq": {"question1": "What is <PERSON><PERSON><PERSON><PERSON>?", "answer1": "E-ditar is a comprehensive school management system designed to streamline educational processes for students, teachers, parents, and school directors.", "question2": "How does E-ditar benefit students?", "answer2": "Students can access their timetables, grades, assignments, and attendance records in one place. They can also track their progress and achievements through the platform.", "question3": "How does E-di<PERSON> help teachers?", "answer3": "Teachers can manage classes, record grades, track attendance, communicate with students and parents, and create/distribute learning materials efficiently.", "question4": "Is E-ditar suitable for all school sizes?", "answer4": "Yes, E-ditar is designed to be scalable and can accommodate schools of all sizes, from small private institutions to large public school systems.", "question5": "How secure is the data in E-ditar?", "answer5": "E-ditar employs industry-standard security measures to protect all user data. Access is role-based, ensuring that users only see information relevant to their role."}}, "privacy": {"section1Title": "1. Information We Collect", "section1Desc": "We collect information you provide directly to us, such as when you create an account, update your profile, use interactive features, or contact us. Types of data include:", "section1List": ["Personal information (name, email, phone number)", "Educational data (grades, attendance, assignments)", "Usage information and device data"], "section2Title": "2. How We Use Your Information", "section2Desc": "We use the information we collect to:", "section2List": ["Provide, maintain, and improve our services", "Send notifications, updates, and support messages", "Analyze usage patterns and optimize user experience", "Protect against unauthorized access and fraud"], "section3Title": "3. Data Security", "section3Desc": "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. All data is encrypted during transmission and at rest.", "section4Title": "4. Data Retention", "section4Desc": "We retain your information for as long as your account is active or as needed to provide services. We may retain certain information as required by law or for legitimate business purposes.", "section5Title": "5. Your Rights", "section5Desc": "Depending on your location, you may have rights to:", "section5List": ["Access and receive a copy of your data", "Rectify inaccurate personal information", "Request deletion of your personal data", "Object to processing of your information"], "understand": "I Understand"}, "terms": {"section1Title": "1. Acceptance of Terms", "section1Desc": "By accessing or using E-ditar, you agree to be bound by these Terms of Service. If you do not agree to these terms, please do not use our services.", "section2Title": "2. User Accounts", "section2Desc": "You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. You must immediately notify us of any unauthorized use of your account.", "section3Title": "3. Acceptable Use", "section3Desc": "You agree not to:", "section3List": ["Use the service for any illegal purpose", "Share your account credentials with others", "Attempt to gain unauthorized access to any part of the service", "Upload or transmit viruses or malicious code", "Harass, bully, or intimidate other users"], "section4Title": "4. Intellectual Property", "section4Desc": "All content, features, and functionality of E-ditar, including but not limited to text, graphics, logos, and software, are owned by E-ditar and are protected by copyright, trademark, and other intellectual property laws.", "section5Title": "5. Termination", "section5Desc": "We may terminate or suspend your account and access to the service immediately, without prior notice, for conduct that we believe violates these Terms or is harmful to other users, us, or third parties, or for any other reason.", "section6Title": "6. <PERSON><PERSON><PERSON> of Warranties", "section6Desc": "The service is provided \"as is\" without warranties of any kind, either express or implied. We do not warrant that the service will be uninterrupted or error-free.", "section7Title": "7. Limitation of Liability", "section7Desc": "In no event shall <PERSON><PERSON><PERSON><PERSON> be liable for any indirect, incidental, special, consequential, or punitive damages resulting from your use or inability to use the service.", "acceptButton": "I Accept"}, "luckyWheel": {"title": "Lucky Wheel", "dialogTitle": "Random Student Selector", "spin": "Spin the Wheel", "spinning": "Spinning...", "selected": "Selected Student:", "randomStudent": "Random Student Selector", "randomStudentDesc": "Use the lucky wheel to randomly select a student for questions or participation", "openLuckyWheel": "Open Lucky Wheel"}, "dashboard": {"title": "Teacher Dashboard", "todayIs": "Today", "totalStudents": "Total Students", "totalStudentsDesc": "Across all classes", "classesToday": "Classes Today", "pendingAssignments": "Pending Assignments", "pendingAssignmentsDesc": "To be graded", "absencesToday": "Absences Today", "absencesTodayDesc": "Students absent", "upcomingTopics": "Upcoming Topics", "addNewTopic": "Add New Topic", "todaysSchedule": "Today's Schedule", "yourSubjects": "Your Subjects", "viewAllSubjects": "View All Subjects", "showLess": "Show Less", "noClasses": "No classes scheduled", "quickAttendance": "Quick Attendance", "quickAttendanceDesc": "Take attendance for today's classes", "topic": "Topic", "takeAttendance": "Take Attendance", "schoolOverview": "School Overview", "schoolReport": "Generate School Report", "enrolledStudents": "Enrolled Students", "totalTeachers": "Total Teachers", "activeTeachers": "Active Teachers", "averageAttendance": "Average Attendance", "thisMonth": "This Month", "averageGrade": "Average Grade", "allStudents": "All Students", "recentActivities": "Recent Activities", "newStudentRegistration": "New Student Registration", "gradesUpdated": "Grades Updated", "attendanceReport": "Attendance Report", "performanceReports": "Performance Reports", "performanceReportsDescription": "Generate comprehensive academic performance analytics and insights across all grades and subjects", "attendanceReports": "Attendance Reports", "attendanceReportsDescription": "Track student and teacher attendance patterns, absences, and punctuality metrics", "resourceAllocation": "Resource Allocation", "resourceAllocationDescription": "Monitor and analyze the distribution of educational resources and facilities", "generateReport": "Generate Report"}, "dashboardStudent": {"title": "Student Dashboard", "averageGrade": "Average Grade", "basedOn": "Based on ", "totalSubjects": "Total Subjects", "AllenrolledSubjects": "All active enrolled subjects", "upcomingAssignments": "Upcoming Assignments", "dueThisWeek": "Due this week", "absences": "Absences", "thisSemester": "This semester", "latestTopic": "Latest Topic", "understandingPrinciples": "Understanding the fundamental principles of classical mechanics.", "recommendedResources": "Recommended Resources", "article": "Article", "openArticle": "Open article", "video": "Video", "openVideo": "Open video"}, "dashboardParent": {"title": "Parent Dashboard", "viewSchedule": "View Schedule", "viewGrades": "View Grades", "childsAverage": "Child's Average Grade", "upcomingTests": "Upcoming  Tests", "totalAbsences": "Total Absences", "thisWeek": "this week", "currentLearning": " Current Learning Topics", "learningDesc": "  What your child is currently studying"}, "schedule": {"mySchedule": "My Schedule", "childsSchedule": "Child's Schedule", "scheduleDesc": "Your daily class schedule"}, "subjects": {"allGrades": "All Grades", "grade": "Grade", "allStatus": "All Status", "active": "Active", "archived": "Archived", "addNewSubject": "Add New Subject", "totalSubjects": "Total Subjects", "activeTeachers": "Active Teachers", "totalStudents": "Total Students", "assignTeacher": "Assign Teacher", "subjectSettings": "Subject Settings", "allSubjects": "All Subjects", "latestTopics": "Latest Topics", "childsSubjects": "Child's Subjects & Topics"}, "assignments": {"myAssignments": "My Assignments", "manageAssignments": "Manage Assignments", "createAssignment": "Create Assignment", "editAssignment": "Edit Assignment", "allAssignments": "All assignments", "loadingAssignments": "Loading assignments...", "searchAssignments": "Search assignments...", "all": "All", "title": "Title", "description": "Description", "dueDate": "Due Date", "points": "Points", "createDescription": "Create a new assignment for your students", "editDescription": "Modify the assignment details", "status": {"label": "Status", "completed": "Completed", "duesoon": "Due Soon", "overdue": "Overdue"}}, "grades": {"myGrades": "My Grades", "childsGrades": "Child's Grades", "addNewGrade": "Add New Grade", "gradePerformance": "Grade Performance", "gradeProgress": "Grade Progress", "gradeDesc": "Academic performance over time", "recentGrades": "Recent Grades", "gradesAllSubjects": "Latest grades across all subjects", "subject": "Subject", "date": "Date", "grade": "Grade", "student": "Student", "students": "students", "studentGrades": "Student Grades", "studentGradesTable": "Student Grades Table", "viewFullTable": "View Full Table", "fullGradesTable": "Full Grades Table", "selectSubject": "Select subject", "searchStudents": "Search students...", "noGradesFound": "No grades found matching your criteria", "confirmGradeAssignment": "Confirm Grade Assignment", "confirmGradeQuestion": "Are you sure you want to assign grade", "removeGradeQuestion": "Are you sure you want to remove grade", "subjectRequired": "Please select a subject", "confirm": "Confirm", "cancel": "Cancel", "to": "to", "for": "for", "period1": "Period 1", "period2": "Period 2"}, "attendance": {"myAttendance": "My Attendance", "studentAttendance": "Student Attendance", "absenceLog": "Absence Log", "recordabsences": "Record of student absences", "childsAttendance": "Child's Attendance", "searchBySubject": "Search by subject...", "searchStudents": "Search students...", "noRecordsFound": "No attendance records found matching your criteria", "recordAbsence": "Record Absence", "recordAttendance": "Record Attendance", "addAttendance": "Add Attendance", "markAllPresent": "<PERSON>", "student": "Student", "present": "Present", "absent": "Absent", "late": "Late", "reason": "Reason", "status": {"all": "All", "excused": "Excused", "unexcused": "Unexcused", "pending": "Pending"}, "reasons": {"sick": "Sick", "family": "Family", "leaveHour": "Leave Hour", "other": "Other"}}, "settings": {"profileInformation": "Profile Information", "UpdateInformation": "Update your personal information", "profileUpdated": "Profile updated successfully", "phoneNumber": "Phone Number", "security": "Security", "securityDesc": "Manage your password and account security", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "updatePassword": "Update Password", "preferences": "Preferences", "darkMode": "Dark Mode", "darkModeDesc": "Switch between light and dark theme", "language": "Language", "notifications": "Notifications", "notificationsDesc": "Manage how you receive notifications", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive notifications via email", "appNotifications": "In-App Notifications", "appNotificationsDesc": "Receive notifications within the application", "systemUpdates": "System Updates", "systemUpdatesDesc": "Get notified about system updates and new features", "nameRequired": "Name is required", "currentPasswordRequired": "Current password is required", "passwordRequirements": "Password must be at least 6 characters", "emailValidation": "Please enter a valid email address", "phoneValidation": "Please enter a valid phone number", "nameValidation": "Name must be at least 3 characters long", "passwordValidation": "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character", "confirmPasswordRequired": "Passwords do not match", "validationError": "Please fix the errors in the form before submitting"}, "role": {"student": "Student", "teacher": "Teacher", "parent": "Parent"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "analytics": {"dashboard": "Analytics Dashboard", "averageGrade": "Average Grade", "averageGradeDesc": "2.3% increase from last month", "attendanceRate": "Attendance Rate", "attendanceRateDesc": "0.5% decrease from last month", "activeStudents": "Active Students", "activeStudentsDesc": "12 new students this month", "performanceTrend": "Performance Trend", "performanceTrendDesc": "Positive growth trend", "attendanceTrends": "Attendance Trends", "gradeDistribution": "Grade Distribution"}, "teachers": {"title": "Teachers", "subtitle": "Manage and review all teachers in the school", "addTeacher": "Add Teacher", "teachingStaff": "Teaching Staff", "searchTeachers": "Search teachers...", "noTeachersFound": "No teachers found matching your criteria", "status": {"all": "All Teachers", "active": "Active", "leave": "Leave"}, "details": {"classes": "Classes", "experience": "Experience", "view": "View", "edit": "Edit"}}, "staffmembers": {"title": "Staff Members", "subtitle": "Manage and review all staff members in the school", "addTeacher": "Add Staff Member", "teachingStaff": "Teaching Staff", "searchTeachers": "Search staff members...", "noTeachersFound": "No staff members found matching your criteria", "noResults": "No results found", "filterByRole": "Filter by role", "filterByStatus": "Filter by status", "status": {"all": "All ", "active": "Active", "leave": "Leave"}, "roles": {"all": "All Staff Members", "teachers": "Teachers", "professors": "Professors", "inspectors": "Inspectors", "directors": "Directors"}, "details": {"classes": "Classes", "experience": "Experience", "view": "View", "edit": "Edit"}}, "reports": {"title": "Reports", "description": "Generate and access school performance reports", "newReport": "New Report", "generator": {"title": "Report Generator", "description": "Create custom reports based on your requirements", "reportType": "Report Type", "period": "Period", "customDate": "Pick up a date", "generate": "Generate Report"}, "available": {"title": "Available Reports", "search": "Search reports...", "filterType": "Filter Type", "allTypes": "All Types", "status": {"final": "Final", "draft": "Draft"}}, "types": {"academic": "Academic Performance", "attendance": "Attendance", "financial": "Financial", "personnel": "Personnel"}, "periods": {"currentSemester": "Current Semester", "lastSemester": "Last Semester", "currentYear": "Current Year", "custom": "Custom"}, "actions": {"download": "Download", "print": "Print"}, "size": "Size", "date": "Date", "category": "Category", "status": "Status"}, "curriculum": {"curriculumManagement": "Curriculum Management", "planning": "Curriculum Planning", "childCurriculum": "Child's Curriculum", "studentCurriculum": "My Curriculum", "totalCourses": "Total Courses", "activeCourses": "Active Courses", "activeTeachers": "Active Teachers", "teachingStaff": "Teaching Staff", "enrolledStudents": "Enrolled Students", "activeStudents": "Active Students", "completionRate": "Completion Rate", "averageCompletion": "Average Completion", "overallProgress": "Overall Progress", "academicProgress": "Academic Progress", "nextWeek": "Next Week", "thisWeek": "This Week", "upcomingTests": "Upcoming Tests", "nextAssessment": "Next Assessment", "dueDate": "Due Date", "completedAssignments": "Completed Assignments", "submissionRate": "Submission Rate", "pendingTasks": "Pending Tasks", "requiresAttention": "Requires Attention", "assignments": "Assignments", "homework": "Homework", "childProgress": "Child's Progress", "completed": "Completed", "inProgress": "In Progress", "notStarted": "Not Started", "addCurriculum": "Add Curriculum", "editCurriculum": "Edit <PERSON>", "settings": "Curriculum Settings", "addActivity": "Add Activity", "uploadMaterial": "Upload Material", "assignHomework": "Assign Homework", "planAssessment": "Plan Assessment", "viewDetails": "View Details", "materials": "Learning Materials", "exercises": "Exercises", "assessments": "Assessments", "subjects": "Subjects", "selectSubject": "Select Subject", "selectGrade": "Select Grade", "gradeLevel": "Grade Level", "schedule": "Weekly Schedule", "today": "Today", "upcoming": "Upcoming", "learningObjectives": "Learning Objectives", "lessonResources": "Lesson Resources", "lessonExercises": "Exercises", "lessonDue": "Due", "additionalMaterials": "Additional Materials", "contactTeacher": "Contact your teacher for additional study materials.", "noDescriptionAvailable": "No description available", "topicDetails": {"objective1": "Understand key concepts related to {{topic}}", "objective2": "Apply theoretical knowledge in practical scenarios", "objective3": "Analyze and evaluate information critically"}, "courseDescription": "Course Description", "expectedOutcomes": "Expected Outcomes", "status": "Status", "active": "Active", "archived": "Archived", "draft": "Draft", "teacherResources": "Teacher Resources", "lessonPlans": "Lesson Plans", "classManagement": "Class Management", "curriculumOverview": "Curriculum Overview", "departmentProgress": "Department Progress", "teacherPerformance": "Teacher Performance", "academicMetrics": "Academic Metrics", "childPerformance": "Child's Performance", "parentResources": "Parent Resources", "academicSupport": "Academic Support", "myProgress": "My Progress", "myAssignments": "My Assignments", "studyMaterials": "Study Materials", "upcomingDeadlines": "Upcoming Deadlines", "importantAnnouncements": "Important Announcements", "notifications": "Notifications", "filterBy": "Filter <PERSON>", "searchCurriculum": "Search Curriculum", "sortBy": "Sort By", "noCoursesFound": "No Courses Found", "loadingCurriculum": "Loading Curriculum...", "successMessage": "Changes Saved Successfully", "errorMessage": "Error Saving Changes", "currentLearningMaterials": "Current Learning Materials", "additionalResources": "Additional Resources", "materialTypes": {"pdf": "PDF", "video": "Video", "document": "Document"}, "fileSize": "{{size}} MB", "duration": "{{duration}} min", "pages": "{{count}} pages", "resources": {"physicsSimulator": "Interactive Physics Simulator", "physicsSimulatorDesc": "Practice Newton's Laws in real-time", "videoLibrary": "Video Tutorials Library", "videoLibraryDesc": "Step-by-step explanations of key concepts", "studyGuide": "Study Guide", "studyGuideDesc": "Comprehensive review materials"}, "progress": {"status": "Progress Status", "complete": "{{percent}}% Complete", "nextDue": "Next Due"}}, "manageStudents": {"title": "Student Management", "description": "View and manage all students by class and section", "totalStudents": "Total Students", "activeStudents": "Active Students", "totalClasses": "Total Classes", "totalSections": "Total Sections", "filter": "Filter", "searchPlaceholder": "Search students by name, ID or parent", "searchResults": "Search Results:", "section": "Section", "addStudent": "Add Student", "editStudent": "Edit Student", "studentDetails": "Student Details", "studentProgress": "Student Progress", "studentPerformance": "Student Performance", "studentAssessments": "Student Assessments", "studentGrades": "Student Grades", "studentGradesTable": "Student Grades Table", "studentId": "Student ID", "firstName": "First Name", "lastName": "Last Name", "dateOfBirth": "Date of Birth", "parent/guardian": "Parent/Guardian", "email": "Email", "phone": "Phone", "status": "Status", "actions": "Actions", "noStudentsFound": "No students found", "loadingStudents": "Loading students..."}, "notifications": {"title": "Notifications", "markAllAsRead": "Mark all as read", "noNotifications": "No notifications to display", "subject": "Subject", "urgent": "<PERSON><PERSON>"}, "submissions": {"viewSubmissions": "View Submissions", "viewFor": "Submissions for", "student": "Student", "date": "Date", "grade": "Grade", "actions": "Actions", "notGraded": "Not Graded", "review": "Review", "reviewSubmission": "Review Submission", "submissionContent": "Submission Content", "feedback": "<PERSON><PERSON><PERSON>", "provideFeedback": "Provide feedback for this submission", "submitReview": "Submit Review"}, "create": "Create", "save": "Save", "cancel": "Cancel", "close": "Close", "submit": "Submit", "loginSuccess": "Successfully logged in", "invalidCredentials": "Invalid email or password", "invalidPassword": "Password must be at least 6 characters", "resetLinkSent": "Password reset link has been sent to your email", "teachersMessages": {"teacherAdded": "Teacher added successfully", "teacherUpdated": "Teacher updated successfully", "addSuccess": "Teacher added successfully", "updateSuccess": "Teacher information updated successfully"}, "assignmentsMessages": {"assignmentCreated": "Assignment created successfully", "reviewSubmitted": "Review submitted successfully"}, "notificationsMessages": {"allMarkedAsRead": "All notifications marked as read", "notificationRead": "Notification marked as read"}, "dashboardMessages": {"attendanceRecorded": "Attendance recorded successfully"}, "quiz": {"title": "Quiz", "instructions": "Test your knowledge with these multiple-choice questions", "comingSoon": "Quiz feature coming soon!", "checkAnswer": "Check Answer", "nextQuestion": "Next Question", "seeResults": "See Results", "resultsTitle": "Quiz Results", "yourScore": "Your Score", "goodResult": "Great job! You've demonstrated a good understanding of this topic.", "tryAgainMessage": "Keep practicing! Try taking the quiz again to improve your score.", "retakeQuiz": "Retake Quiz", "explanation": "Explanation"}, "flashcards": {"title": "Flashcards", "instructions": "Review key concepts with these interactive flashcards", "previous": "Previous", "next": "Next", "markAsKnown": "<PERSON> as Known", "marked": "Marked", "clickToSeeAnswer": "Click to see answer", "clickToSeeQuestion": "Click to see question", "progress": "Progress", "shuffle": "Shuffle", "completionTitle": "Congratulations!", "completionMessage": "You've completed all the flashcards in this set!", "restart": "Start Over", "shuffleAndRestart": "Shuffle & Restart", "studyWithFlashcards": "Study with Flashcards"}, "resources": {"filePreview": "File Preview", "download": "Download File", "videoNotAvailable": "Video preview not available in demo mode", "watchVideo": "Watch Video", "premiumContent": "This is premium content. Unlock to access.", "unlock": "Unlock Content"}, "lessonEditDialog": {"title": "Edit Topic", "description": "Update the topic information below.", "learningObjectives": "Learning Objectives", "addObjective": "Add new objective", "objectivePlaceholder": "Objective", "resources": "Resources", "exercises": "Exercises", "save": "Save Changes", "cancel": "Cancel"}, "homeworkDialog": {"title": "Assign Homework", "description": "Create a new homework assignment for your students", "homeworkTitle": "Title", "homeworkDescription": "Description", "dueDate": "Due Date", "points": "Points", "instructions": "Instructions", "instructionsPlaceholder": "Detailed instructions for students", "allowLateSubmission": "Allow Late Submission", "maxDaysLate": "Maximum Days Late", "attachments": "Attachments", "addAttachment": "Add Attachment", "assign": "Assign Homework", "cancel": "Cancel"}, "activityDialog": {"title": "Add Activity", "description": "Create a new learning activity for your class.", "activityTitle": "Activity Title", "activityDescription": "Description", "activityType": "Activity Type", "individual": "Individual", "group": "Group", "class": "Class", "duration": "Duration (minutes)", "add": "Add Activity", "cancel": "Cancel"}, "materialDialog": {"title": "Upload Learning Material", "materialTitle": "Material Title", "enterTitle": "Enter material title", "materialType": "Material Type", "types": {"document": "Document", "presentation": "Presentation", "video": "Video", "audio": "Audio", "interactive": "Interactive", "other": "Other"}, "description": "Description", "descriptionPlaceholder": "Describe this material", "uploadFiles": "Upload Files", "dragAndDrop": "Click to upload or drag and drop", "fileTypes": "PDF, DOC, PPT, MP4, etc.", "externalLinks": "External Links (Optional)", "linkPlaceholder": "https://example.com", "cancel": "Cancel", "upload": "Upload Material"}, "setup": {"title": "School Management System", "subtitle": "Complete your school setup in a few simple steps", "previous": "Back", "next": "Next", "finish": "Finish", "basicInfo": "Basic Info", "staff": "Staff", "configuration": "Configuration", "classes": "Classes", "subjects": "Subjects", "students": "Students", "scheduling": "Scheduling", "shiftsAndScheduling": "Shifts & Scheduling", "addNewShift": "Add New Shift", "shifts": "Shifts", "shift": "Shift", "studentScheduling": "Student Scheduling", "studentSchedulingDesc": "After setup, you can create detailed class schedules for students, including:", "classPeriods": "Class periods with subjects", "teacherAssignments": "Teacher assignments", "roomLocations": "Room locations", "weeklySchedules": "Daily and weekly schedules", "staffScheduling": "Staff Scheduling", "staffSchedulingDesc": "Teacher and staff schedules can be managed, including:", "teachingHours": "Teaching hours and availability", "classAssignments": "Class assignments", "nonTeachingDuties": "Non-teaching duties", "substitutionManagement": "Substitution management", "setupCompleted": "Setup Completed", "setupComplete": "Setup Complete", "submitting": "Submitting setup data...", "setupCompleteDesc": "You've completed all the necessary setup steps for your school. After finishing, you'll be able to:", "createSchedules": "Create detailed schedules", "manageAttendance": "Manage attendance", "trackPerformance": "Track student performance", "generateReports": "Generate reports", "startTime": "Start Time", "endTime": "End Time", "selectShiftMode": "Select a shift mode", "basicSchoolInfo": "Basic School Information", "schoolName": "School Name", "city": "City", "schoolCategory": "School Category", "schoolLocation": "School Location", "requiredFields": "Required fields", "enterSchoolName": "Enter school name", "enterCity": "Enter city", "searchCity": "Search cities...", "searchVillage": "Search villages...", "selectCategory": "Select category", "primary": "Primary and Lower Secondary School", "secondary": "Upper Secondary School", "selectLocation": "Select location", "urban": "Urban ", "rural": "Rural", "villageName": "Enter village name", "enterVillageName": "Enter village name", "mission": "Mission", "missionAndVision": "Mission and Vision", "enterMission": "Enter mission", "vision": "Vision", "enterVision": "Enter vision", "missionDescription": "A mission statement is a clear, concise, and compelling declaration of your school's purpose, values, and goals. It should outline what your school aims to achieve and how it plans to make a positive impact on students, educators, and the community.", "visionDescription": "A vision statement is a forward-looking, aspirational description of your school's future state. It should articulate the long-term goals and aspirations of your school, inspiring stakeholders to work towards a shared vision of success.", "staffManagement": "Staff Management", "addStaff": "Add Staff", "addStaffMembers": "Add staff members based on your school category:", "availableRoles": "Available roles:", "name": "Name", "role": "Role", "email": "Email", "phone": "Phone", "actions": "Actions", "noStaffMembersAdded": "No staff members added yet", "addNewStaffMember": "Add New Staff Member", "firstName": "First Name", "lastName": "Last Name", "enterFirstName": "Enter first name", "enterLastName": "Enter last name", "selectRole": "Select role", "teacher": "Teacher", "subjectTeacher": "Subject Teacher", "professor": "Professor", "inspector": "Inspector", "optional": "optional", "enterPhoneNumber": "Enter phone number", "idNumber": "ID Number", "enterIdNumber": "Enter ID number", "cancel": "Cancel", "addStaffMember": "Add Staff Member", "saveStaffMember": "Save Staff Member", "schoolCategoryplaceholder": "Primary", "schoolConfiguration": "School Configuration", "numberOfPeriodsInSchoolYear": "Number of Periods in School Year", "numberOfGradingPeriodsPerSchoolYear": "Number of grading periods per school year", "academicPeriods": "Academic Periods", "defineAcademicPeriodsForSchoolYear": "Define academic periods for the school year", "addPeriod": "Add Period", "addParent": "Add Parent / Guardian", "savePeriod": "Save", "saveParent": "Save", "periodName": "Period Name", "enterPeriodName": "Enter period name", "startDate": "Start Date", "endDate": "End Date", "numberOfLanguages": "Select the languages your platform will support", "selectLanguages": "Select languages", "selectOneOrMoreLanguages": "Select one or more languages", "gradingSystem": "Grading System", "selectGradingSystem": "Select a grading system", "oneToFiveScale": "1-5 Scale", "oneToTenScale": "1-10 Scale", "aToFScale": "A-F Scale", "country": "Country", "selectCountry": "Select a country", "schoolCycle": "School Cycle", "selectSchoolCycle": "Select a school cycle", "elementaryGrades1To5": "Grades 1-5", "elementaryGrades1To9": "Grades 1-9", "middleGrades6To9": "Grades 6-9", "highGrades10To12": "Grades 10-12", "gradeLevel": "Grade Level", "selectGradeLevel": "Select a grade level", "selectAll": "Select all classes", "selectSchoolCycleFirst": "Select school cycle first", "noGradesAvailable": "No grades available", "grade1": "Grade 1", "grade2": "Grade 2", "grade3": "Grade 3", "grade4": "Grade 4", "grade5": "Grade 5", "grade6": "Grade 6", "grade7": "Grade 7", "grade8": "Grade 8", "grade9": "Grade 9", "grade10": "Grade 10", "grade11": "Grade 11", "grade12": "Grade 12", "gradeRangesThatYourSchoolCovers": "Grade ranges that your school covers", "classesAndSections": "Classes and Sections", "addClass": "Add Class", "responsibleTeacher": "Responsible teacher", "selectTeacher": "Select a teacher", "noTeachersAvailable": "No teachers available", "noTeacherAssigned": "No teacher assigned", "selectAllClasses": "Select all classes", "addClassesAndSections": "Add classes and parallel sections based on your school cycle", "notSet": "Not set", "forEachClass": "For each class, you can add multiple parallel sections (e.g., 1/1, 1/2, 1/3)", "noClassesAdded": "No classes added yet", "addNewClass": "Add New Class", "grade": "Grade", "selectGrade": "Select grade", "className": "Class Name", "classNamePlaceholder": "e.g., Class 1, Freshmen, etc.", "section": "Section", "sectionIdentifier": "Section identifier (e.g., /1, /2, /A, /B)", "initialStudentCount": "Initial Student Count", "addStudentsLater": "You can add students individually later", "subjectsAndCurriculum": "Subjects and Curriculum", "addSubject": "Add Subject", "noSubjectsAdded": "No subjects added yet", "addNewSubject": "Add New Subject", "subjectName": "Subject Name", "subjectNamePlaceholder": "e.g., Mathematics, Science, etc.", "curriculum": "Curriculum", "selectCurriculum": "Select curriculum", "nationalCurriculum": "National", "internationalCurriculum": "International", "customCurriculum": "Custom", "applicableClasses": "Applicable Classes", "applicableToGrades": "Applicable to grades", "studentRegistration": "Student Registration", "addStudent": "Add Student", "noStudentsRegistered": "No students registered yet", "addNewStudent": "Add New Student", "studentName": "Student Name", "studentFirstName": "Student First Name", "studentLastName": "Student Last Name", "studentBirthday": "Student Birthday", "enterStudentFirstName": "Enter student's first name", "enterStudentLastName": "Enter student's last name", "enterStudentFullName": "Enter student's full name", "studentIdCardNumber": "ID Card Number", "enterStudentIdCardNumber": "Enter student's ID card number", "class": "Class", "selectClass": "Select class", "selectSection": "Select section", "parentInformation": "Parent/Guardian Information", "parent": "Parent", "parentName": "Parent Name", "parentFirstName": "First Name", "parentLastName": "Last Name", "enterParentFirstName": "Enter first name", "enterParentLastName": "Enter  last name", "enterParentFullName": "Enter parent's full name", "parentEmail": "Email", "enterParentEmail": "Enter email address", "parentPhone": "Phone", "enterParentPhoneNumber": "Enter phone number", "parentIdCardNumber": "ID Card Number", "enterParentIdCardNumber": "Enter ID card number", "parentEditInfo": "To edit parent/guardian information, delete and re-add the student.", "parentInfo": "Add parent/guardian information for the student", "birthday": "Birthday", "saveStudent": "Save Student", "saveClass": "Save Class", "saveSubject": "Save Subject", "shiftNumber": "Shift Number", "shiftName": "Shift Name", "generalShiftInfo": "General Shift Info", "shortBreakLength": "Short Break Length", "shortBreakLengthPlaceholder": "Enter short break length in minutes", "longBreakLength": "Long Break Length", "longBreakLengthPlaceholder": "Enter long break length in minutes", "longBreakLocation": "Long Break Location", "longBreakLocationDesc": "Long break will occur after class nr", "maxClassesInShift": "Max Classes in Shift", "maxClassesPlaceholder": "Enter maximum number of classes", "noShiftsAddedYet": "No shifts added yet", "addShift": "Add Shift", "gender": "Gender", "selectGender": "Select gender", "male": "Male", "female": "Female", "parentGender": "Parent Gender", "parentGenderPlaceholder": "Select parent's gender"}, "staffMessages": {"staffAdded": "Staff member added successfully", "staffRemoved": "Staff member removed successfully", "staffUpdated": "Staff member updated successfully"}, "countries": {"albania": "Albania", "kosovo": "Kosovo", "northMacedonia": "North Macedonia", "montenegro": "Montenegro"}, "classMessages": {"classAdded": "Class added successfully", "classRemoved": "Class removed successfully", "classUpdated": "Class updated successfully"}, "subjectMessages": {"subjectAdded": "Subject added successfully", "subjectRemoved": "Subject removed successfully", "subjectUpdated": "Subject updated successfully"}, "studentMessages": {"studentAdded": "Student added successfully", "studentRemoved": "Student removed successfully", "studentUpdated": "Student updated successfully"}, "periodMessages": {"periodAdded": "Period added successfully", "periodRemoved": "Period removed successfully", "periodUpdated": "Period updated successfully"}, "validationSetup": {"addAtLeastOneStaff": "Please add at least one staff member", "addAtLeastOneClass": "Please add at least one class", "addAtLeastOneSubject": "Please add at least one subject", "addAtLeastOneShift": "Please add at least one shift", "classNameRequired": "Class name is required", "sectionRequired": "Section is required", "teacherRequired": "Teacher is required", "subjectNameRequired": "Subject name is required", "selectAtLeastOneClass": "Please select at least one class", "studentNameRequired": "Student name is required", "studentFirstNameRequired": "Student first name is required", "studentLastNameRequired": "Student last name is required", "studentBirthdayRequired": "Student birthday is required", "idCardNumberRequired": "ID card number is required", "gradeRequired": "Grade is required", "parentNameRequired": "Parent name is required", "parentFirstNameRequired": "Parent/Guardian first name is required", "parentLastNameRequired": "Parent/Guardian last name is required", "parentEmailRequired": "Parent/Guardian email is required", "parentPhoneRequired": "Parent/Guardian phone number is required", "parentPhoneTooShort": "Parent/Guardian phone number is too short", "parentPhoneTooLong": "Parent/Guardian phone number is too long", "parentPhoneInvalidFormat": "Invalid parent/guardian phone number format", "parentPhoneInvalid": "Invalid parent/guardian phone number", "parentPhoneFormats": "For example: +383 49 123 123", "parentIdCardNumberRequired": "Parent/Guardian ID card number is required", "parentsRequired": "At least one parent/guardian is required", "parentEditInfo": "To edit parent/guardian information, delete and re-add the student.", "invalidEmail": "Please enter a valid email address", "schoolNameRequired": "School name is required", "cityRequired": "City is required", "schoolCategoryRequired": "School category is required", "locationTypeRequired": "Location type is required", "villageNameRequired": "Village name is required", "missionRequired": "Mission is required", "visionRequired": "Vision is required", "staffFirstNameRequired": "Staff name is required", "staffLastNameRequired": "Staff last name is required", "staffEmailRequired": "Valid email address is required", "staffRoleRequired": "Staff role is required", "staffListRequired": "At least one staff member is required", "staffPhoneRequired": "Phone number is required", "staffPhoneTooShort": "Phone number is too short", "staffPhoneTooLong": "Phone number is too long", "staffPhoneInvalidFormat": "Invalid phone number format", "staffPhoneInvalid": "Invalid phone number", "staffPhoneFormats": "For example: +383 49 123 123", "staffIdNumberRequired": "ID Number is required", "staffIdNumberDuplicate": "This ID Number is already in use by another staff member", "academicYearRequired": "Number of periods is required", "periodsRequired": "At least one academic period is required", "languageRequired": "At least one language must be selected", "gradingSystemRequired": "Grading system is required", "countryRequired": "Country is required", "schoolCycleRequired": "School cycle is required", "gradeLevelRequired": "Grade level is required", "duplicateGradeSection": "A class with this grade and section already exists", "duplicateSubjectName": "A subject with this name already exists", "duplicateShiftTime": "A shift with this start time already exists", "duplicateStudentId": "A student with this ID number already exists", "duplicateParentId": "A parent/guardian with this ID number already exists", "shiftNameRequired": "Shift name is required", "startTimeRequired": "Start time is required", "maxClassesRequired": "Max classes is required", "shortBreakLengthRequired": "Short break length is required", "longBreakLengthRequired": "Long break length is required", "longBreakLocationRequired": "Long break location is required", "genderRequired": "Gender is required", "parentGenderRequired": "Parent gender is required", "cannotProceedWithoutShift": "You cannot proceed without adding at least one shift"}, "shiftMessages": {"shiftAdded": "Shift added successfully", "shiftRemoved": "Shift removed successfully", "shiftUpdated": "Shift updated successfully"}, "assistant": {"pageTitle": "AI Assistant", "welcomeTitle": "What can I help with?", "welcomeMessage": "I'm your E-ditar AI assistant. Ask me anything about the platform, and I'll help you find what you need.", "inputPlaceholder": "Ask anything...", "chatAddNew": "Create new chat", "startNewChat": "Start a new chat to begin", "noSearchResults": "No conversations found", "tryDifferentSearchTerm": "Try a different search term", "helpGuides": "Help Guides", "suggestions": "Suggestions", "recentConversations": "Recent Conversations", "noConversations": "No recent conversations", "helpTopics": "Help Topics", "suggestedQuestions": "Suggested Questions", "deleteChat": "Delete Chat", "deleteChatConfirm": "Are you sure you want to delete this chat?", "deleteAllChats": "Delete All Conversations", "deleteAllChatsConfirm": "Are you sure you want to delete all conversations?", "deletedAllConversations": "All conversations deleted successfully", "confirmDelete": "Confirm Delete", "confirmDeleteMessage": "Are you sure you want to delete this chat?", "confirmDeleteAll": "Confirm Delete All", "confirmDeleteAllMessage": "Are you sure you want to delete all conversations?", "delete": "Delete", "deleteAll": "Delete All", "cancel": "Cancel", "confirm": "Confirm"}, "deleteConfirmation": {"confirmDeleteStaff": "Confirm Staff Deletion", "confirmDeleteStaffMessage": "Are you sure you want to delete this staff member? This action cannot be undone.", "confirmDeleteSubject": "Confirm Subject Deletion", "confirmDeleteSubjectMessage": "Are you sure you want to delete this subject? This action cannot be undone.", "confirmDeleteStudent": "Confirm Student Deletion", "confirmDeleteStudentMessage": "Are you sure you want to delete this student? This action cannot be undone.", "confirmDeleteShift": "Confirm Shift Deletion", "confirmDeleteShiftMessage": "Are you sure you want to delete this shift? This action cannot be undone.", "confirmDeleteClass": "Confirm Class Deletion", "confirmDeleteClassMessage": "Are you sure you want to delete this class? This action cannot be undone.", "confirmDeletePeriod": "Confirm Period Deletion", "confirmDeletePeriodMessage": "Are you sure you want to delete this period? This action cannot be undone.", "delete": "Delete", "cancel": "Cancel"}, "lessonOutcomes": {"dialogTitle": "Lesson Learning Outcomes", "formTitle": "Learning Outcomes for", "formDescription": "Complete the form with all required information", "objective": "Lesson Objective", "objectivePlaceholder": "Students should understand the concept of...", "knowledge": "Knowledge", "knowledgeDescription": "Assess students' understanding of concepts and facts", "knowledgeItemPlaceholder": "Most students were able to clearly define...", "addKnowledgeItem": "Add Knowledge Observation", "skills": "Practical Skills", "skillsDescription": "Assess students' ability to apply knowledge", "skillsItemPlaceholder": "Students successfully completed practical exercises...", "addSkillsItem": "Add Skills Observation", "engagement": "Participation and Engagement", "engagementDescription": "Assess students' involvement in the lesson", "engagementItemPlaceholder": "The majority of students actively participated...", "addEngagementItem": "Add Engagement Observation", "assessment": "Immediate Assessment", "assessmentDescription": "Assess formative evaluation results", "assessmentItemPlaceholder": "Through verbal questions and exercises...", "addAssessmentItem": "Add Assessment Observation", "comprehensionRate": "Overall Comprehension Rate", "savedSuccess": "Learning outcomes saved successfully", "addOutcomes": "Add Learning Outcomes", "editOutcomes": "Edit Learning Outcomes", "completed": "Assessed", "remove": "Remove", "commonTechniques": "Common Teaching Techniques", "techniquesDescription": "Which teaching techniques were most effective", "techniquesPlaceholder": "Group discussion, practical demonstrations, collaborative learning..."}, "lessonObservation": {"title": "Lesson Observation", "formTitle": "Lesson Observation Form", "formDescription": "Complete this form to document your lesson observation.", "schoolName": "School Name", "teacherName": "Teacher Name and last name:", "teacherNamePlaceholder": "Enter teacher name and last name", "subject": "Observation in the subject: ", "subjectPlaceholder": "Enter subject", "date": "Date", "pickDate": "Pick a date", "class": "Class", "period": "Period", "hour": "Hour", "schoolYear": "School Year", "observationType": "Observation Type", "regular": "Regular", "special": "Special", "observationPurpose": "Purpose of Observation", "observationPurposePlaceholder": "Enter purpose of observation", "lessonTopic": "Lesson Topic", "lessonTopicPlaceholder": "Enter lesson topic", "lessonType": "Lesson Type", "lessonTypePlaceholder": "Enter lesson type", "evaluationItems": "Observation of the lesson hour", "rating": {"good": "Good", "average": "Average", "poor": "Poor"}, "notes": "Notes", "notesPlaceholder": "Enter notes", "selectTeacher": "Select Teacher", "searchTeacher": "Search for a teacher", "selectTeacherStartObservation": "Select a teacher to start the observation", "teacherFeedback": "Discussion with the teacher after the lesson ends", "teacherThoughts": "What are the teacher's thoughts on the development of the lesson?", "teacherThoughtsPlaceholder": "Teacher's thoughts on lesson development", "methodsUsed": "Which methods, forms, and techniques of teaching were used?", "methodsUsedPlaceholder": "Methods, forms and techniques used in teaching", "changesApplied": "What changes has the teacher made in teaching after the training?", "changesAppliedPlaceholder": "Changes made in teaching after training", "objectivesAchieved": "Were the lesson objectives achieved?", "objectivesAchievedPlaceholder": "Were the lesson objectives achieved?", "directorFeedbackTitle": "Comments and Advice from the School Director", "directorThoughtsPlaceholder": "Director's thoughts and comments", "adviceForTeacherPlaceholder": "Advice for the teacher", "directorThoughts": "Director's Thoughts", "adviceForTeacher": "Advice for Teacher", "directorName": "Director's Name", "directorSignature": "Director's Signature", "teacherSignature": "Teacher's Signature", "saveDraft": "Save Draft", "submit": "Submit", "cancel": "Cancel", "printForm": "Print Form", "saveForm": "Save Form", "signaturesTitle": "Signatures", "directorLabel": "Director", "directorNamePlaceholder": "Director name", "directorSignatureLabel": "Signature", "teacherLabel": "Teacher", "teacherSignatureLabel": "Signature", "signaturePlaceholder": "Signature", "observedOnLabel": "Observed on", "saveObservationForm": "Save Observation Form"}, "contactPage": {"title": "Get In Touch", "subtitle": "Have questions about <PERSON><PERSON><PERSON><PERSON>? We're here to help.", "formTitle": "Send us a message", "fullName": "Full Name", "email": "Email Address", "phone": "Phone Number", "institution": "Institution Name", "message": "Message", "sendMessage": "Send Message", "infoTitle": "Contact Information", "emailLabel": "Email", "phoneLabel": "Phone", "addressLabel": "Address", "officeHoursLabel": "Office Hours", "weekdays": "Monday - Friday", "officeHours": "9:00 AM - 5:00 PM PST", "messageSent": "Message sent successfully! We'll get back to you soon."}, "pricingPage": {"title": "Simple, Transparent Pricing", "subtitle": "Choose the plan that's right for your educational institution.", "mostPopular": "MOST POPULAR", "getStarted": "Get Started", "whatsIncluded": "What's included:", "perMonth": "/month", "needHelp": "Need help choosing the right plan?", "helpDescription": "Our education specialists can help you find the perfect solution for your institution's needs.", "contactSales": "Contact Sales", "scheduleDemo": "Schedule Demo", "plans": {"basic": {"name": "Basic", "price": "$99", "description": "Perfect for small schools just getting started with digital tools.", "features": ["Up to 200 student accounts", "Basic dashboard functions", "Attendance tracking", "Grade management", "Email support"]}, "professional": {"name": "Professional", "price": "$299", "description": "Our most popular plan for growing educational institutions.", "features": ["Up to 1000 student accounts", "Advanced analytics", "Lesson observation tools", "Parent portal access", "Priority email & phone support", "Custom reporting"]}, "enterprise": {"name": "Enterprise", "price": "$599", "description": "For large schools and districts with comprehensive needs.", "features": ["Unlimited student accounts", "Enterprise-level analytics", "Dedicated account manager", "API access for custom integrations", "24/7 priority support", "On-site training sessions", "Custom feature development"]}}}, "featuresPage": {"title": "Powerful Features for Modern Education", "subtitle": "E-ditar provides comprehensive tools for students, teachers, parents, and school directors to enhance the educational experience.", "featureSpotlight": "Feature Spotlight", "analyticsDashboard": {"title": "Analytics Dashboard", "description": "Our powerful analytics dashboard provides real-time insights into student performance, attendance trends, and curriculum progress. School directors and teachers can identify areas for improvement and make data-driven decisions.", "features": ["Interactive data visualizations", "Customizable reports and exports", "Performance trend analysis", "Actionable insights and recommendations"]}, "learnMore": "Learn More", "roleFeatures": {"title": "Features for Every Role", "subtitle": "E-ditar provides tailored features for different users in the educational ecosystem.", "forStudents": "For Students", "forTeachers": "For Teachers", "forParents": "For Parents", "forSchoolDirectors": "For School Directors", "students": ["Assignment tracking and submission", "Interactive timetable with notifications", "AI-powered learning assistant", "Grade monitoring and performance analytics", "Resource library and study materials"], "teachers": ["Lesson planning and curriculum management", "Attendance management", "Classroom observation reflections", "Student performance tracking", "Grading and feedback tools"], "parents": ["Child progress monitoring", "Attendance and assignment tracking", "Fee payment and receipt access", "Communication with teachers", "School event notifications"], "directors": ["School-wide analytics dashboard", "Curriculum oversight and planning", "Lesson observation management", "Teacher performance monitoring", "Resource allocation tools"]}, "ctaTitle": "Ready to transform your educational experience?", "startToday": "Start Today", "contactSales": "Contact Sales"}, "aboutPage": {"title": "About E-ditar", "subtitle": "Transforming education with technology", "ourMission": {"title": "Our Mission", "p1": "At E-ditar, we believe that technology can transform education, making it more accessible, engaging, and effective. Our mission is to empower educators, students, and parents with innovative tools that enhance the learning experience.", "p2": "We're committed to developing solutions that address the real challenges faced by educational institutions, creating meaningful impact in classrooms around the world."}, "ourValues": {"title": "Our Values", "values": [{"number": 1, "title": "Innovation", "description": "Continuously pushing the boundaries of educational technology"}, {"number": 2, "title": "Accessibility", "description": "Making quality education technology available to all"}, {"number": 3, "title": "Collaboration", "description": "Fostering partnerships between educators, students, and parents"}]}, "ourTeam": {"title": "Our Team", "members": [{"name": "Dr. <PERSON>", "role": "CEO & Founder", "description": "Former professor with 15+ years experience in education technology", "initials": "SJ"}, {"name": "<PERSON>", "role": "CTO", "description": "Software engineer passionate about creating tools for education", "initials": "MC"}, {"name": "<PERSON>", "role": "Head of Education", "description": "Former school principal with expertise in curriculum development", "initials": "OR"}]}}}