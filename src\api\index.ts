import axios from "axios";

const API_BASE_URL = import.meta.env.VITE_EDITAR_BASE_API_URL;
const api = axios.create({
  baseURL: API_BASE_URL,
});

// Function to get auth token from react-auth-kit cookie
const getAuthToken = () => {
  const cookies = document.cookie.split(";");
  const authCookie = cookies.find((cookie) =>
    cookie.trim().startsWith("_auth=")
  );
  if (authCookie) {
    return authCookie.split("=")[1];
  }
  return null;
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = getAuthToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid - react-auth-kit will handle refresh automatically
      // If refresh fails, user will be redirected to login
      console.warn("Authentication error:", error.response.data);
    } else if (error.response?.status === 403) {
      // Forbidden - redirect to unauthorized page
      window.location.href = "/unauthorized";
    }
    return Promise.reject(error);
  }
);

export default api;
