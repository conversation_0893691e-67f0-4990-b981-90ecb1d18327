import { useState, useEffect, useRef } from "react";
import { useStore } from "../../lib/store";
import { useTypedAuthUser } from "../../hooks/useAuth";
import { Navigate, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { School, ArrowLeft, ArrowRight } from "lucide-react";
import {
  BasicInformationStep,
  StaffManagementStep,
  SchoolConfigurationStep,
  SubjectsandCurriculumStep,
  ClassesandSectionsStep,
  StudentRegistrationStep,
  SchedulingandShiftStep,
} from "./steps";
import toast from "react-hot-toast";
import { SetupWizardProvider, useSetupWizard } from "./SetupWizardContext";

function SetupWizardContent() {
  const authUser = useTypedAuthUser();
  const { setSetupCompleted } = useStore();
  const { t } = useTranslation();
  const [currentStep, setCurrentStep] = useState(1);
  const navigate = useNavigate();
  const { setupData, clearValidationErrors, setValidationError } =
    useSetupWizard();
  const isInitialLoad = useRef(true);

  const getFirstIncompleteStep = (data: any): number => {
    // Step 1: Basic Information
    if (
      !data.basicInfo.schoolName?.trim() ||
      !data.basicInfo.city?.trim() ||
      !data.basicInfo.schoolCategory?.trim() ||
      !data.basicInfo.locationType?.trim() ||
      !data.basicInfo.mission?.trim() ||
      !data.basicInfo.vision?.trim() ||
      (data.basicInfo.locationType === "rural" &&
        !data.basicInfo.villageName?.trim())
    ) {
      return 1;
    }

    // Step 2: Staff Management
    if (data.staff.length === 0) {
      return 2;
    }

    // Step 3: School Configuration
    if (
      !data.configuration.periods ||
      data.configuration.periods.length === 0 ||
      !data.configuration.language?.trim() ||
      !data.configuration.timezone?.trim() ||
      !data.configuration.currency?.trim() ||
      !data.configuration.schoolCycle?.trim()
    ) {
      return 3;
    }

    if (data.classes.length === 0) {
      return 4;
    }

    if (data.subjects.length === 0) {
      return 5;
    }

    if (data.shifts.shiftList.length === 0) {
      return 7;
    }

    return 7;
  };

  useEffect(() => {
    if (isInitialLoad.current) {
      const savedStep = localStorage.getItem("setupWizardCurrentStep");
      const firstIncompleteStep = getFirstIncompleteStep(setupData);

      if (savedStep) {
        const parsedStep = parseInt(savedStep, 10);
        setCurrentStep(Math.max(parsedStep, firstIncompleteStep));
      } else {
        setCurrentStep(firstIncompleteStep);
      }

      isInitialLoad.current = false;
    }
  }, [setupData]);

  // Redirect non-admin users
  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();

  if (!authUser || normalizedRole !== "admin") {
    return <Navigate to="/dashboard" />;
  }

  const steps = [
    {
      id: 1,
      name: t("setup.basicInfo") || "Basic Info",
      component: <BasicInformationStep />,
    },
    {
      id: 2,
      name: t("setup.staff") || "Staff",
      component: <StaffManagementStep />,
    },
    {
      id: 3,
      name: t("setup.configuration") || "Configuration",
      component: <SchoolConfigurationStep />,
    },
    {
      id: 4,
      name: t("setup.classes") || "Classes",
      component: <ClassesandSectionsStep />,
    },
    {
      id: 5,
      name: t("setup.subjects") || "Subjects",
      component: <SubjectsandCurriculumStep />,
    },
    {
      id: 6,
      name: t("setup.students") || "Students",
      component: <StudentRegistrationStep />,
    },
    {
      id: 7,
      name: t("setup.scheduling") || "Scheduling",
      component: <SchedulingandShiftStep />,
    },
  ];

  const validateCurrentStep = () => {
    clearValidationErrors();

    switch (currentStep) {
      case 1: // Basic Information
        let isValid = true;
        if (!setupData.basicInfo.schoolName?.trim()) {
          setValidationError("schoolName", true);
          isValid = false;
        }
        if (!setupData.basicInfo.city?.trim()) {
          setValidationError("city", true);
          isValid = false;
        }
        if (!setupData.basicInfo.schoolCategory?.trim()) {
          setValidationError("schoolCategory", true);
          isValid = false;
        }
        if (!setupData.basicInfo.locationType?.trim()) {
          setValidationError("locationType", true);
          isValid = false;
        }
        if (!setupData.basicInfo.mission?.trim()) {
          setValidationError("mission", true);
          isValid = false;
        }
        if (!setupData.basicInfo.vision?.trim()) {
          setValidationError("vision", true);
          isValid = false;
        }

        if (
          setupData.basicInfo.locationType === "rural" &&
          !setupData.basicInfo.villageName?.trim()
        ) {
          setValidationError("villageName", true);
          isValid = false;
        }
        return isValid;

      case 2: // Staff Management
        if (setupData.staff.length === 0) {
          toast.error(t("validationSetup.addAtLeastOneStaff"));
          setValidationError("staffList", true);
          return false;
        }
        return true;
      case 3: // School Configuration
        let configValid = true;
        if (
          !setupData.configuration.periods ||
          setupData.configuration.periods.length === 0
        ) {
          setValidationError("periods", true);
          configValid = false;
        }
        if (!setupData.configuration.language?.trim()) {
          setValidationError("language", true);
          configValid = false;
        }
        if (!setupData.configuration.timezone?.trim()) {
          setValidationError("timezone", true);
          configValid = false;
        }
        if (!setupData.configuration.currency?.trim()) {
          setValidationError("currency", true);
          configValid = false;
        }
        if (!setupData.configuration.schoolCycle?.trim()) {
          setValidationError("schoolCycle", true);
          configValid = false;
        }
        return configValid;

      case 4: // Classes and Sections
        if (setupData.classes.length === 0) {
          toast.error(t("validationSetup.addAtLeastOneClass"));
          setValidationError("classList", true);
          return false;
        }
        return true;

      case 5: // Subjects and Curriculum
        if (setupData.subjects.length === 0) {
          toast.error(t("validationSetup.addAtLeastOneSubject"));
          setValidationError("subjectList", true);
          return false;
        }
        return true;

      case 6:
        return true;

      case 7: // Scheduling and Shifts
        if (setupData.shifts.shiftList.length === 0) {
          toast.error(t("validationSetup.addAtLeastOneShift"));
          setValidationError("shiftList", true);
          return false;
        }
        return true;

      default:
        return true;
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      if (validateCurrentStep()) {
        const nextStep = currentStep + 1;
        setCurrentStep(nextStep);

        localStorage.setItem("setupWizardCurrentStep", nextStep.toString());
      }
    } else if (currentStep === steps.length) {
      if (validateCurrentStep()) {
        console.log("Submitting setup data:", setupData);

        // Here you would typically send the data to your API
        submitSetupData(setupData);
      }
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      localStorage.setItem("setupWizardCurrentStep", prevStep.toString());
    }
  };

  const submitSetupData = async (data: any) => {
    try {
      toast.loading(t("setup.submitting"));

      // const response = await api.post('/setup/complete', data);
      await new Promise((resolve) => setTimeout(resolve, 1500));

      toast.dismiss();
      toast.success(
        t("setup.setupCompleted") || "Setup completed successfully!"
      );

      localStorage.setItem("setupData", JSON.stringify(data));
      localStorage.removeItem("setupWizardCurrentStep");

      setSetupCompleted(true);

      await new Promise((resolve) => setTimeout(resolve, 100));

      navigate("/dashboard", { replace: true });
    } catch (error) {
      toast.dismiss();
      toast.error(t("setup.setupFailed") || "Setup failed. Please try again.");
      console.error("Setup submission error:", error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center px-4 py-8">
      <div className="text-purple-600 mb-4">
        <School size={48} />
      </div>

      <h1 className="text-3xl font-bold text-center text-gray-800">
        {t("setup.title") || "School Management System"}
      </h1>
      <p className="text-gray-600 mb-8 text-center">
        {t("setup.subtitle") ||
          "Complete your school setup in a few simple steps"}
      </p>

      {/* Progress bar and steps */}
      <div className="w-full max-w-4xl mb-10">
        <div className="relative flex items-center justify-between">
          <div
            className="absolute left-10 right-10 h-1 bg-gray-200"
            style={{
              top: "40%",
              transform: "translateY(-50%)",
              zIndex: 0,
            }}
          ></div>

          <div
            className="absolute left-0  h-1 bg-green-400"
            style={{
              top: "40%",
              transform: "translateY(-48%)",
              zIndex: 1,
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%`,
              transition: "width 0.5s ease-in-out",
            }}
          ></div>

          {steps.map((step) => {
            const isActive = step.id === currentStep;
            const isCompleted = step.id < currentStep;
            const isPending = step.id > currentStep;

            return (
              <div
                key={step.id}
                className="relative flex flex-col items-center"
              >
                {/* Step circle */}
                <button
                  onClick={() => {
                    if (step.id < currentStep) {
                      setCurrentStep(step.id);
                      // Save the current step to localStorage
                      localStorage.setItem(
                        "setupWizardCurrentStep",
                        step.id.toString()
                      );
                    }
                  }}
                  disabled={isPending}
                  className={`
                    w-14 h-14 rounded-full flex items-center justify-center
                    transition-all duration-200 z-10
                    ${isActive ? "bg-purple-600 text-white" : ""}
                    ${
                      isCompleted
                        ? "bg-green-400 text-white cursor-pointer hover:bg-green-600"
                        : ""
                    }
                    ${
                      isPending
                        ? "bg-gray-200 text-gray-600 cursor-not-allowed"
                        : ""
                    }
                  `}
                  aria-current={isActive ? "step" : undefined}
                  aria-label={`Step ${step.id}: ${step.name}`}
                >
                  {isCompleted ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  ) : (
                    step.id
                  )}
                </button>

                <span
                  className={`
                  mt-2 text-xs font-medium
                  ${isActive ? "text-purple-600" : ""}
                  ${isCompleted ? "text-green-600" : ""}
                  ${isPending ? "text-gray-500" : ""}
                `}
                >
                  {step.name}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      <div className="bg-white shadow-md rounded-lg p-8 w-full max-w-4xl">
        {steps.find((step) => step.id === currentStep)?.component}
      </div>

      <div className="flex justify-between w-full max-w-4xl mt-6">
        <button
          onClick={handlePrevious}
          className={`px-4 py-2 flex items-center gap-2 rounded ${
            currentStep === 1
              ? "text-gray-400 bg-gray-100 cursor-not-allowed"
              : "text-purple-600 border border-purple-400 rounded-md hover:bg-gray-100"
          }`}
          disabled={currentStep === 1}
        >
          <ArrowLeft className="w-4 h-4" />
          {t("setup.previous") || "Previous"}
        </button>

        <button
          onClick={handleNext}
          className="cursor-pointer px-4 py-2 bg-purple-600 hover:bg-purple-500 text-white rounded flex items-center gap-2 hover:purple-blue-700"
        >
          {currentStep === steps.length
            ? t("setup.finish") || "Finish"
            : t("setup.next") || "Next"}
          {currentStep < steps.length && <ArrowRight className="w-4 h-4" />}
        </button>
      </div>
    </div>
  );
}

export default function SetupWizardPage() {
  return (
    <SetupWizardProvider>
      <SetupWizardContent />
    </SetupWizardProvider>
  );
}
