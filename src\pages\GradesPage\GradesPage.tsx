import { useTypedAuthUser } from "../../hooks/useAuth";
import DirectorGradePage from "./DirectorGradePage";
import TeacherGradePage from "./TeacherGradePage";
import StudentGradePage from "./StudentGradePage";
import ParentGradePage from "./ParentGradePage";

export default function GradesPage() {
  const authUser = useTypedAuthUser();

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return <StudentGradePage />;
    case "teacher":
      return <TeacherGradePage />;
    case "parent":
      return <ParentGradePage />;
    case "admin":
      return <DirectorGradePage />;
    default:
      console.warn("GradesPage: Invalid or unknown role", {
        originalRole: authUser?.role,
        normalizedRole,
      });
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Invalid Role
          </h2>
          <p className="text-gray-600">
            Your account role "{authUser?.role}" is not recognized. Please
            contact support.
          </p>
        </div>
      );
  }
}
