import { useState } from "react";
import StatCard from "../../components/StatCard";
import ReportCard from "../../components/ReportCard";
import {
  Users,
  GraduationCap,
  ClipboardList,
  BarChart3,
  Clock,
  School,
  UserCheck,
  ChartNoAxesColumnIncreasing,
  ChartPie,
  ChartSpline,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import AcademicPerformance from "../../components/AcademicPerformance";
import FormattedDate from "@/components/FormattedDate";
import WarningManagement from "../../components/WarningManagement";

export default function DirectorDashboard() {
  const { t } = useTranslation();
  const [selectedYears, setSelectedYears] = useState<string[]>([
    "2024",
    "2025",
  ]);

  const handleGenerateReport = () => {
    console.log("Generating report...");
  };

  const toggleYear = (year: string) => {
    if (selectedYears.includes(year)) {
      // Remove year if already selected
      setSelectedYears(selectedYears.filter((y) => y !== year));
    } else {
      // Add year if not selected
      setSelectedYears([...selectedYears, year]);
    }
  };

  const yearlyPerformanceData = [
    // 2024 data
    { month: "Janar", score: 1600, color: "#22c55e", year: "2024" },
    { month: "Shkurt", score: 1500, color: "#22c55e", year: "2024" },
    { month: "Mars", score: 1200, color: "#22c55e", year: "2024" },
    { month: "Prill", score: 950, color: "#22c55e", year: "2024" },
    { month: "Maj", score: 850, color: "#22c55e", year: "2024" },
    { month: "Qershor", score: 650, color: "#22c55e", year: "2024" },
    { month: "Korrik", score: 700, color: "#22c55e", year: "2024" },
    { month: "Gusht", score: 750, color: "#22c55e", year: "2024" },
    { month: "Shtator", score: 650, color: "#22c55e", year: "2024" },
    { month: "Tetor", score: 850, color: "#22c55e", year: "2024" },
    { month: "Nëntor", score: 1050, color: "#22c55e", year: "2024" },
    { month: "Dhjetor", score: 1650, color: "#22c55e", year: "2024" },
    // 2025 data
    { month: "Janar", score: 1850, color: "#7b3aed", year: "2025" },
    { month: "Shkurt", score: 1650, color: "#7b3aed", year: "2025" },
    { month: "Mars", score: 1450, color: "#7b3aed", year: "2025" },
    { month: "Prill", score: 1050, color: "#7b3aed", year: "2025" },
    { month: "Maj", score: 850, color: "#7b3aed", year: "2025" },
  ];

  // Filter data based on selected years
  const filteredYearlyData = yearlyPerformanceData.filter((item) =>
    selectedYears.includes(item.year)
  );

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-4 sm:mb-6">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold">
            {t("dashboard.schoolOverview")}
          </h1>
          <FormattedDate />
        </div>
        <div className="flex gap-4 mt-2 sm:mt-0">
          <button className="px-3 sm:px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 flex items-center gap-2 text-sm sm:text-base">
            <School className="w-4 h-4" />
            {t("dashboard.schoolReport")}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title={t("dashboard.totalStudents")}
          value="1,234"
          description={t("dashboard.enrolledStudents")}
          icon={<Users className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboard.totalTeachers")}
          value="89"
          description={t("dashboard.activeTeachers")}
          icon={<GraduationCap className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboard.averageAttendance")}
          value="95%"
          description={t("dashboard.thisMonth")}
          icon={<UserCheck className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboard.averageGrade")}
          value="8.5"
          description={t("dashboard.allStudents")}
          icon={<BarChart3 className="h-5 w-5" />}
        />
      </div>

      {/* School Performance section */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        <div className="col-span-1 lg:col-span-12 p-6 border border-gray-300 bg-white rounded-md">
          <div className="flex justify-between items-center mb-4">
            <div>
              <h2 className="text-xl font-semibold">
                {t("schoolPerformance")}
              </h2>
              <p className="text-sm text-gray-500">{t("yearlyComparison")}</p>
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => toggleYear("2024")}
                className={`px-3 py-1 text-sm rounded-md flex items-center gap-1 ${
                  selectedYears.includes("2024")
                    ? "bg-green-500 text-white"
                    : "bg-gray-200 text-gray-700"
                }`}
              >
                <div className="w-3 h-3 bg-green-500"></div>
                2024
              </button>
              <button
                onClick={() => toggleYear("2025")}
                className={`px-3 py-1 text-sm rounded-md flex items-center gap-1 ${
                  selectedYears.includes("2025")
                    ? "bg-[#7b3aed] text-white"
                    : "bg-gray-200 text-gray-700"
                }`}
              >
                <div className="w-3 h-3 bg-[#7b3aed]"></div>
                2025
              </button>
            </div>
          </div>
          <AcademicPerformance
            data={filteredYearlyData}
            title=""
            subtitle=""
            showYearLegend={true}
            isMonthly={true}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {/* New Reports Section */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-6">
          <ReportCard
            title={t("dashboard.performanceReports")}
            icon={
              <ChartNoAxesColumnIncreasing className="h-7 w-7 text-purple-700" />
            }
            description={t("dashboard.performanceReportsDescription")}
            buttonText={t("dashboard.generateReport")}
            onClick={handleGenerateReport}
          />

          <ReportCard
            title={t("dashboard.attendanceReports")}
            icon={<ChartPie className="h-7 w-7 text-purple-700" />}
            description={t("dashboard.attendanceReportsDescription")}
            buttonText={t("dashboard.generateReport")}
            onClick={handleGenerateReport}
          />

          <ReportCard
            title={t("dashboard.resourceAllocation")}
            icon={<ChartSpline className="h-7 w-7 text-purple-700" />}
            description={t("dashboard.resourceAllocationDescription")}
            buttonText={t("dashboard.generateReport")}
            onClick={handleGenerateReport}
          />
        </div>
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">
              {t("dashboard.recentActivities")}
            </h2>
          </div>
          <div className="space-y-4">
            {/* Activity items */}
            <ActivityItem
              icon={<Users className="h-5 w-5" />}
              title={t("dashboard.newStudentRegistration")}
              description="5 new students registered"
              time="2 hours ago"
            />
            <ActivityItem
              icon={<ClipboardList className="h-5 w-5" />}
              title={t("dashboard.gradesUpdated")}
              description="Mathematics department updated grades"
              time="3 hours ago"
            />
            <ActivityItem
              icon={<Clock className="h-5 w-5" />}
              title={t("dashboard.attendanceReport")}
              description="Daily attendance report generated"
              time="5 hours ago"
            />
          </div>
        </div>
      </div>
      <WarningManagement
        readOnly={false}
        title={t("warnings.schoolWarnings")}
        description={t("warnings.schoolDescription")}
      />
    </div>
  );
}

// Helper components
function ActivityItem({ icon, title, description, time }: any) {
  return (
    <div className="flex items-start space-x-4 p-3 hover:bg-gray-50 rounded-lg">
      <div className="p-2 bg-purple-100 rounded-full">{icon}</div>
      <div className="flex-1">
        <h3 className="font-medium">{title}</h3>
        <p className="text-sm text-gray-600">{description}</p>
        <span className="text-xs text-gray-500">{time}</span>
      </div>
    </div>
  );
}
