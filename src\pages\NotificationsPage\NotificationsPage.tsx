import { format } from "date-fns";
import { useState, useEffect } from "react";
import { useTypedAuthUser } from "../../hooks/useAuth";
import { FaRegBell } from "react-icons/fa";
import { FaRegCalendarAlt } from "react-icons/fa";
import { FiBookOpen } from "react-icons/fi";
import { FaAward } from "react-icons/fa";
import { FiAlertCircle } from "react-icons/fi";
import { GraduationCap } from "lucide-react";
import { MessageSquare } from "lucide-react";
import { UserCheck } from "lucide-react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";

export default function NotificationsPage() {
  const authUser = useTypedAuthUser();
  const [notifications, setNotifications] = useState<any[]>([]);
  const { t } = useTranslation();

  // Initialize notifications based on user role
  useEffect(() => {
    if (authUser?.role) {
      // Normalize role to lowercase for consistent comparison
      const normalizedRole = authUser.role.toLowerCase();

      const roleNotifications = getNotificationsByRole(normalizedRole);
      const sortedNotifications = roleNotifications.sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
      );
      setNotifications(sortedNotifications);
    }
  }, [authUser?.role]);

  // Function to mark all notifications as read
  const markAllAsRead = () => {
    const updatedNotifications = notifications.map((notification) => ({
      ...notification,
      read: true,
    }));
    setNotifications(updatedNotifications);
    toast.success(t("notificationsMessages.allMarkedAsRead"));
  };

  // Function to mark a single notification as read
  const markAsRead = (id: number) => {
    const updatedNotifications = notifications.map((notification) =>
      notification.id === id ? { ...notification, read: true } : notification
    );
    setNotifications(updatedNotifications);
    toast.success(t("notificationsMessages.notificationRead"));
  };

  const getIcon = (type: string) => {
    switch (type) {
      case "assignment":
        return <FiBookOpen className="w-5 h-5" />;
      case "deadline":
        return <FaRegCalendarAlt className="w-5 h-5" />;
      case "grade":
        return <GraduationCap className="w-5 h-5" />;
      case "achievement":
        return <FaAward className="w-5 h-5" />;
      case "attendance":
        return <UserCheck className="w-5 h-5" />;
      case "message":
        return <MessageSquare className="w-5 h-5" />;
      default:
        return <FaRegBell className="w-5 h-5" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600 bg-red-50";
      case "medium":
        return "text-yellow-600 bg-yellow-50";
      case "low":
        return "text-green-600 bg-green-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  // Check if there are any unread notifications
  const hasUnreadNotifications = notifications.some(
    (notification) => !notification.read
  );

  return (
    <div className="p-3 sm:p-4 md:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 sm:mb-6 gap-2">
        <h1 className="text-xl sm:text-2xl font-semibold text-gray-900">
          {t("notifications.title")}
        </h1>
        {hasUnreadNotifications && (
          <button
            className="text-sm text-indigo-600 hover:text-indigo-800 self-start sm:self-auto"
            onClick={markAllAsRead}
          >
            {t("notifications.markAllAsRead")}
          </button>
        )}
      </div>

      <div className="space-y-3 sm:space-y-4">
        {notifications.length === 0 ? (
          <div className="text-center py-6 sm:py-8 text-gray-500">
            {t("notifications.noNotifications")}
          </div>
        ) : (
          notifications.map((notification) => (
            <div
              key={notification.id}
              className={`bg-white rounded-lg shadow-md p-3 sm:p-4 ${
                !notification.read ? "border-l-4 border-indigo-600" : ""
              }`}
              onClick={() => markAsRead(notification.id)}
            >
              <div className="flex flex-col sm:flex-row sm:items-start">
                <div
                  className={`p-2 rounded-lg ${getPriorityColor(
                    notification.priority
                  )} mb-3 sm:mb-0 self-start`}
                >
                  {getIcon(notification.type)}
                </div>

                <div className="sm:ml-4 flex-1">
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {notification.title}
                      </h3>
                      <p className="mt-1 text-gray-600 text-sm sm:text-base">
                        {notification.message}
                      </p>
                    </div>
                    <span className="text-xs sm:text-sm text-gray-500 mt-2 sm:mt-0">
                      {format(new Date(notification.date), "MMM d, h:mm a")}
                    </span>
                  </div>

                  <div className="mt-2 flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-0">
                    {notification.subject && (
                      <span className="text-xs sm:text-sm text-gray-500">
                        {t("notifications.subject")}: {notification.subject}
                      </span>
                    )}
                    {notification.priority === "high" && !notification.read && (
                      <div className="flex items-center text-xs sm:text-sm text-red-600 sm:ml-4">
                        <FiAlertCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                        <span>{t("notifications.urgent")}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}

function getNotificationsByRole(role?: string) {
  // Normalize role to lowercase for consistent comparison
  const normalizedRole = role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return studentNotifications;
    case "teacher":
      return teacherNotifications;
    case "parent":
      return parentNotifications;
    case "admin":
      return directorNotifications;
    default:
      console.warn(
        "getNotificationsByRole: Unknown role, using student notifications",
        {
          originalRole: role,
          normalizedRole,
        }
      );
      return studentNotifications;
  }
}

// Student notifications
const studentNotifications = [
  {
    id: 1,
    type: "assignment",
    title: "New Assignment Posted",
    message: "Differential Equations Project has been posted",
    date: "2024-03-15T10:00:00",
    subject: "Mathematics",
    priority: "high",
    read: false,
  },
  {
    id: 2,
    type: "deadline",
    title: "Assignment Due Tomorrow",
    message: "Physics Lab Report is due tomorrow",
    date: "2024-03-14T15:30:00",
    subject: "Physics",
    priority: "high",
    read: false,
  },
  {
    id: 3,
    type: "grade",
    title: "New Grade Posted",
    message: "Your Chemistry Quiz has been graded",
    date: "2024-03-13T09:15:00",
    subject: "Chemistry",
    priority: "medium",
    read: true,
  },
  {
    id: 4,
    type: "achievement",
    title: "Achievement Unlocked",
    message: 'You earned the "Perfect Attendance" badge',
    date: "2024-03-12T14:20:00",
    subject: null,
    priority: "low",
    read: true,
  },
];

// Teacher notifications
const teacherNotifications = [
  {
    id: 1,
    type: "deadline",
    title: "Grades Due Tomorrow",
    message: "Final grades for Physics 101 are due by 5 PM",
    date: "2024-03-15T10:00:00",
    subject: "Physics",
    priority: "high",
    read: false,
  },
  {
    id: 2,
    type: "message",
    title: "New Message from Principal",
    message: "Faculty meeting scheduled for Friday at 3 PM",
    date: "2024-03-14T15:30:00",
    subject: null,
    priority: "medium",
    read: false,
  },
  {
    id: 3,
    type: "attendance",
    title: "Attendance Report Ready",
    message: "Weekly attendance report is ready for review",
    date: "2024-03-13T09:15:00",
    subject: null,
    priority: "medium",
    read: true,
  },
  {
    id: 4,
    type: "assignment",
    title: "Assignment Submissions",
    message: "15 new submissions for Algebra homework",
    date: "2024-03-12T14:20:00",
    subject: "Mathematics",
    priority: "medium",
    read: true,
  },
];

// Parent notifications
const parentNotifications = [
  {
    id: 1,
    type: "grade",
    title: "New Grade for Alex",
    message: "Alex received an A on the Biology exam",
    date: "2024-03-15T10:00:00",
    subject: "Biology",
    priority: "medium",
    read: false,
  },
  {
    id: 2,
    type: "attendance",
    title: "Absence Recorded",
    message: "Alex was marked absent in Physics class today",
    date: "2024-03-14T15:30:00",
    subject: "Physics",
    priority: "high",
    read: false,
  },
  {
    id: 3,
    type: "deadline",
    title: "Upcoming Test",
    message: "Mathematics test scheduled for next Monday",
    date: "2024-03-13T09:15:00",
    subject: "Mathematics",
    priority: "medium",
    read: true,
  },
  {
    id: 4,
    type: "message",
    title: "Message from Teacher",
    message: "Parent-teacher conference scheduled for next week",
    date: "2024-03-12T14:20:00",
    subject: null,
    priority: "medium",
    read: true,
  },
];

// Director/Admin notifications
const directorNotifications = [
  {
    id: 1,
    type: "attendance",
    title: "School Attendance Alert",
    message: "Overall attendance dropped below 90% this week",
    date: "2024-03-15T10:00:00",
    subject: null,
    priority: "high",
    read: false,
  },
  {
    id: 2,
    type: "message",
    title: "District Office Message",
    message: "Budget approval for science lab equipment",
    date: "2024-03-14T15:30:00",
    subject: null,
    priority: "high",
    read: false,
  },
  {
    id: 3,
    type: "grade",
    title: "Grade Report Ready",
    message: "Quarterly grade reports ready for review",
    date: "2024-03-13T09:15:00",
    subject: null,
    priority: "medium",
    read: true,
  },
  {
    id: 4,
    type: "deadline",
    title: "Teacher Evaluations Due",
    message: "Complete teacher evaluations by end of month",
    date: "2024-03-12T14:20:00",
    subject: null,
    priority: "medium",
    read: true,
  },
];
