import { useTypedAuthUser } from "../../hooks/useAuth";
import TeacherAttendancePage from "./TeacherAttendancePage";
import StudentAttendancePage from "./StudentAttendancePage";
import ParentAttendancePage from "./ParentAttendancePage";
import DirectorAttendancePage from "./DirectorAttendancePage";

export default function AttendancePage() {
  const authUser = useTypedAuthUser();

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return <StudentAttendancePage />;
    case "teacher":
      return <TeacherAttendancePage />;
    case "parent":
      return <ParentAttendancePage />;
    case "admin":
      return <DirectorAttendancePage />;
    default:
      console.warn("AttendancePage: Invalid or unknown role", {
        originalRole: authUser?.role,
        normalizedRole,
      });
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Invalid Role
          </h2>
          <p className="text-gray-600">
            Your account role "{authUser?.role}" is not recognized. Please
            contact support.
          </p>
        </div>
      );
  }
}
