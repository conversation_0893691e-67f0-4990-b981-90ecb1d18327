import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Pencil, Clock } from "lucide-react";
import { useTranslation } from "react-i18next";
import LessonEditDialog from "@/components/LessonEditDialog/LessonEditDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FormattedDate from "@/components/FormattedDate";

interface LessonCard {
  title: string;
  description: string;
  objectives: string[];
  resources: number;
  exercises: number;
  progress?: number;
  status?: "not-started" | "in-progress" | "completed";
  dueDate?: string;
  hasOutcomes?: boolean;
  outcomeButton?: React.ReactNode;
}

interface ScheduleItem {
  day: string;
  date: string;
  lessons: {
    subject: string;
    color: string;
  }[];
}

interface CurriculumViewProps {
  title: string;
  actionButtons?: React.ReactNode;
  schedule: ScheduleItem[];
  lessonCards: LessonCard[];
  onLessonEdit?: (lesson: LessonCard) => void;
  gradeLevel?: string[];
  subjects?: string[];
}

export default function CurriculumView({
  title,
  actionButtons,
  schedule,
  lessonCards,
  onLessonEdit,
  gradeLevel = ["Grade 7"],
  subjects = ["Science"],
}: CurriculumViewProps) {
  const { t } = useTranslation();
  const [selectedLesson, setSelectedLesson] = useState<LessonCard | null>(null);
  const [selectedGrade, setSelectedGrade] = useState(gradeLevel[0]);
  const [selectedSubject, setSelectedSubject] = useState(subjects[0]);

  return (
    <main>
      <div className="mb-6 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold pb-2">{title}</h1>
          <p className="text-gray-500">
            <FormattedDate />
          </p>
        </div>
        <div className="flex flex-wrap gap-2">{actionButtons}</div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 w-full pb-6">
        <div className="flex flex-col w-full sm:w-1/2">
          <label className="font-medium mb-1">
            {t("curriculum.gradeLevel")}
          </label>
          <Select value={selectedGrade} onValueChange={setSelectedGrade}>
            <SelectTrigger className="w-full bg-white">
              <SelectValue placeholder={t("curriculum.selectGrade")} />
            </SelectTrigger>
            <SelectContent>
              {gradeLevel.map((grade) => (
                <SelectItem key={grade} value={grade}>
                  {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex flex-col w-full sm:w-1/2">
          <label className="font-medium mb-1">{t("curriculum.subjects")}</label>
          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="w-full bg-white">
              <SelectValue placeholder={t("curriculum.selectSubject")} />
            </SelectTrigger>
            <SelectContent>
              {subjects.map((subject) => (
                <SelectItem key={subject} value={subject}>
                  {subject}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="bg-white border border-gray-300 rounded-md p-4 sm:p-5 mb-6">
        <h2 className="text-lg sm:text-xl font-semibold pb-5">
          {t("curriculum.schedule")}
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {schedule.map(({ day, date, lessons }) => (
            <div key={day} className="border border-gray-300 rounded-lg p-4">
              <h3 className="font-medium">{day}</h3>
              <p className="text-sm text-gray-500">{date}</p>
              <div className="mt-2 space-y-2">
                {lessons.map(({ subject, color }) => (
                  <div
                    key={subject}
                    className={`rounded px-2 py-1 text-sm font-medium ${color}`}
                  >
                    {subject}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
        {lessonCards.map((lesson) => (
          <div
            key={lesson.title}
            className="border border-gray-300 flex flex-col justify-between rounded-lg p-4 sm:p-6 bg-white shadow-sm"
          >
            <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
              <h3 className="text-lg font-semibold">{lesson.title}</h3>
              {lesson.status && (
                <span
                  className={`px-3 py-1 rounded-full text-sm font-medium ${
                    lesson.status === "completed"
                      ? "bg-green-100 text-green-700"
                      : lesson.status === "in-progress"
                      ? "bg-purple-100 text-purple-700"
                      : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {lesson.status === "completed"
                    ? "Completed"
                    : lesson.status === "in-progress"
                    ? "In Progress"
                    : "Not Started"}
                </span>
              )}
            </div>

            <p className="mt-2 text-gray-600">{lesson.description}</p>

            {lesson.objectives && lesson.objectives.length > 0 && (
              <div className="mt-3">
                <h4 className="font-medium text-gray-800">
                  {t("curriculum.learningObjectives")}:
                </h4>
                <ul className="list-disc pl-5 mt-1 text-gray-700">
                  {lesson.objectives.map((objective, idx) => (
                    <li key={idx}>{objective}</li>
                  ))}
                </ul>
              </div>
            )}

            <div className="mt-4 text-sm text-gray-500 flex gap-4">
              <span className="flex items-center">
                <Link />{" "}
                <p className="pl-1">
                  {lesson.resources} {t("curriculum.lessonResources")}
                </p>
              </span>
              <span className="flex items-center">
                <NotebookPen />
                <p className="pl-1">
                  {lesson.exercises} {t("curriculum.lessonExercises")}
                </p>
              </span>
              {lesson.dueDate && (
                <span className="flex items-center">
                  <Clock />
                  <p className="pl-1">
                    {t("curriculum.lessonDue")}: {lesson.dueDate}
                  </p>
                </span>
              )}
            </div>

            <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
              {onLessonEdit && (
                <LessonEditDialog
                  lesson={selectedLesson || lesson}
                  setLesson={setSelectedLesson}
                  trigger={
                    <div
                      onClick={() => setSelectedLesson(lesson)}
                      className="flex items-center text-blue-600 text-sm hover:underline cursor-pointer"
                    >
                      <Pencil size={16} />
                      <p className="pl-1">{t("edit")}</p>
                    </div>
                  }
                />
              )}

              <div className="flex items-center gap-2">
                {lesson.hasOutcomes && (
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                    {t("lessonOutcomes.completed")}
                  </span>
                )}
                {lesson.outcomeButton}
              </div>
            </div>
          </div>
        ))}
      </div>
    </main>
  );
}
