export const validateEmail = (email: string): boolean => {
  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
  return emailPattern.test(email);
};

export const validatePassword = (password: string): boolean => {
  return password.length >= 6;
};

export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

export const validatePhone = (phone: string): boolean => {
  const phonePattern = /^\+?[0-9]{10,15}$/;
  return phonePattern.test(phone);
};

// Enhanced phone validation functions
export const isValidPhoneCharacter = (char: string): boolean => {
  // Allow digits, plus sign, spaces, hyphens, parentheses, and periods
  return /[0-9+\s\-().]/g.test(char);
};

export const extractPhoneDigits = (phone: string): string => {
  // Extract only digits from phone number (excluding formatting characters)
  return phone.replace(/[^0-9]/g, "");
};

export const validatePhoneLength = (
  phone: string
): { isValid: boolean; error?: string } => {
  const digits = extractPhoneDigits(phone);

  if (digits.length < 10) {
    return { isValid: false, error: "tooShort" };
  }

  if (digits.length > 15) {
    return { isValid: false, error: "tooLong" };
  }

  return { isValid: true };
};

export const validatePhoneFormat = (
  phone: string
): { isValid: boolean; error?: string } => {
  if (!phone.trim()) {
    return { isValid: false, error: "required" };
  }

  // Enhanced phone patterns to support various formats
  const phonePatterns = [
    /^\+?[1-9]\d{9,14}$/, // International format: +1234567890
    /^\([0-9]{3}\)\s?[0-9]{3}-?[0-9]{4}$/, // US format with parentheses: (*************
    /^[0-9]{3}-[0-9]{3}-[0-9]{4}$/, // US format with hyphens: ************
    /^\+?[1-9]\s?\([0-9]{3}\)\s?[0-9]{3}-?[0-9]{4}$/, // Mixed format: +1 (*************
    /^\+?[1-9]\s?[0-9]{3}\s?[0-9]{3}\s?[0-9]{4}$/, // Spaced format: ****** 456 7890
    /^\+?[1-9][0-9\s\-().]{8,18}$/, // General international format with formatting
  ];

  const isValidFormat = phonePatterns.some(pattern => pattern.test(phone));

  if (!isValidFormat) {
    return { isValid: false, error: "invalidFormat" };
  }

  // Check length after extracting digits
  const lengthValidation = validatePhoneLength(phone);
  if (!lengthValidation.isValid) {
    return lengthValidation;
  }

  return { isValid: true };
};

// Unique ID validation functions
export const validateUniqueStudentId = (
  idCardNumber: string,
  students: any[],
  excludeId?: string
): { isValid: boolean; error?: string } => {
  if (!idCardNumber.trim()) {
    return { isValid: false, error: "required" };
  }

  const isDuplicate = students.some(
    student =>
      student.id !== excludeId &&
      student.idCardNumber.trim().toLowerCase() ===
        idCardNumber.trim().toLowerCase()
  );

  if (isDuplicate) {
    return { isValid: false, error: "duplicateStudentId" };
  }

  return { isValid: true };
};

// Unique Staff ID validation function
export const validateUniqueStaffId = (
  idNumber: string,
  staff: any[],
  excludeId?: string
): { isValid: boolean; error?: string } => {
  if (!idNumber.trim()) {
    return { isValid: false, error: "required" };
  }

  const isDuplicate = staff.some(
    member =>
      member.id !== excludeId &&
      member.idNumber &&
      member.idNumber.trim().toLowerCase() === idNumber.trim().toLowerCase()
  );

  if (isDuplicate) {
    return { isValid: false, error: "duplicateStaffId" };
  }

  return { isValid: true };
};

export const validateUniqueParentId = (
  idCardNumber: string,
  students: any[],
  excludeStudentId?: string,
  excludeParentId?: string
): { isValid: boolean; error?: string } => {
  if (!idCardNumber.trim()) {
    return { isValid: false, error: "required" };
  }

  // Check for duplicates across all parents in all students
  for (const student of students) {
    if (student.id === excludeStudentId) {
      // For the current student, check parents excluding the one being edited
      const isDuplicate = student.parents?.some(
        (parent: any) =>
          parent.id !== excludeParentId &&
          parent.idCardNumber.trim().toLowerCase() ===
            idCardNumber.trim().toLowerCase()
      );
      if (isDuplicate) {
        return { isValid: false, error: "duplicateParentId" };
      }
    } else {
      // For other students, check all parents
      const isDuplicate = student.parents?.some(
        (parent: any) =>
          parent.idCardNumber.trim().toLowerCase() ===
          idCardNumber.trim().toLowerCase()
      );
      if (isDuplicate) {
        return { isValid: false, error: "duplicateParentId" };
      }
    }
  }

  return { isValid: true };
};
