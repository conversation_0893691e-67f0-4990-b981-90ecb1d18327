import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { BookOpen, Plus, Trash2, Save, Edit, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import toast from "react-hot-toast";
import { useSetupWizard } from "../SetupWizardContext";

interface Subject {
  id: string;
  name: string;
  applicableClasses: string[];
}

export default function SubjectsAndCurriculumStep() {
  const { t } = useTranslation();
  const {
    setupData,
    updateSubjects,
    validationErrors,
    setValidationError,
    clearValidationErrors,
  } = useSetupWizard();
  const [isAddingSubject, setIsAddingSubject] = useState(false);
  const [editingSubjectId, setEditingSubjectId] = useState<string | null>(null);
  const [newSubject, setNewSubject] = useState<Subject>({
    id: "",
    name: "",
    applicableClasses: [],
  });
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState<string | null>(null);

  // Get class options from the classes created in the Classes step
  const getClassOptions = () => {
    return setupData.classes.map((classItem) => {
      // Get grade label for display
      const gradeLabel = getGradeLabelByValue(classItem.grade);
      // Create a descriptive class identifier
      const classIdentifier = `${gradeLabel} - ${classItem.className} - ${classItem.section}`;
      return {
        value: classItem.id, // Use class ID as value for uniqueness
        label: classIdentifier,
        classData: classItem,
      };
    });
  };

  // Helper function to get grade label by value
  const getGradeLabelByValue = (gradeValue: string) => {
    const gradeLabels: { [key: string]: string } = {
      "1": t("setup.grade1") || "Grade 1",
      "2": t("setup.grade2") || "Grade 2",
      "3": t("setup.grade3") || "Grade 3",
      "4": t("setup.grade4") || "Grade 4",
      "5": t("setup.grade5") || "Grade 5",
      "6": t("setup.grade6") || "Grade 6",
      "7": t("setup.grade7") || "Grade 7",
      "8": t("setup.grade8") || "Grade 8",
      "9": t("setup.grade9") || "Grade 9",
      "10": t("setup.grade10") || "Grade 10",
      "11": t("setup.grade11") || "Grade 11",
      "12": t("setup.grade12") || "Grade 12",
    };
    return gradeLabels[gradeValue] || `Grade ${gradeValue}`;
  };

  const classOptions = getClassOptions();
  const classes = classOptions.map((option) => option.value); // Use class IDs

  // Helper function to convert class IDs to readable names for display
  const getClassNamesFromIds = (classIds: string[]) => {
    return classIds
      .map((id) => {
        const classOption = classOptions.find((option) => option.value === id);
        return classOption ? classOption.label : id;
      })
      .join(", ");
  };

  const handleAddSubject = () => {
    setIsAddingSubject(true);
    clearValidationErrors();
    setNewSubject({
      id: "",
      name: "",
      applicableClasses: [],
    });
  };

  const handleCancelAdd = () => {
    setIsAddingSubject(false);
    clearValidationErrors();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewSubject({
      ...newSubject,
      [name]: value,
    });

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(name, false);
      // Also clear duplicate name error when user types in name field
      if (name === "name") {
        setValidationError("duplicateSubjectName", false);
      }
    }
  };

  const handleClassToggle = (classId: string) => {
    const updatedClasses = newSubject.applicableClasses.includes(classId)
      ? newSubject.applicableClasses.filter((id) => id !== classId)
      : [...newSubject.applicableClasses, classId];

    setNewSubject({
      ...newSubject,
      applicableClasses: updatedClasses,
    });

    // Clear validation error when user selects classes
    if (updatedClasses.length > 0) {
      setValidationError("applicableClasses", false);
    }
  };

  const handleSubmit = () => {
    clearValidationErrors();
    let isValid = true;

    // Validate form data
    if (!newSubject.name.trim()) {
      setValidationError("name", true);
      isValid = false;
    }

    // Check for duplicate subject names
    const isDuplicateName = setupData.subjects.some(
      (existingSubject) =>
        existingSubject.name.toLowerCase().trim() ===
        newSubject.name.toLowerCase().trim()
    );

    if (isDuplicateName) {
      setValidationError("duplicateSubjectName", true);
      isValid = false;
    }

    if (newSubject.applicableClasses.length === 0) {
      setValidationError("applicableClasses", true);
      isValid = false;
    }

    if (!isValid) return;

    // Add new subject
    const subjectToAdd: Subject = {
      ...newSubject,
      id: Date.now().toString(),
    };

    const updatedSubjects = [...setupData.subjects, subjectToAdd];
    updateSubjects(updatedSubjects);
    setIsAddingSubject(false);
    toast.success(t("subjectMessages.subjectAdded"));
  };

  // Update parent state when deleting a subject
  const handleDeleteSubject = (id: string) => {
    setSubjectToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteSubject = () => {
    if (subjectToDelete) {
      const updatedSubjects = setupData.subjects.filter(
        (subject) => subject.id !== subjectToDelete
      );
      updateSubjects(updatedSubjects);
      toast.success(t("subjectMessages.subjectRemoved"));
      setDeleteDialogOpen(false);
      setSubjectToDelete(null);
    }
  };

  const cancelDeleteSubject = () => {
    setDeleteDialogOpen(false);
    setSubjectToDelete(null);
  };

  const handleEditSubject = (subject: Subject) => {
    setEditingSubjectId(subject.id);
    setEditingSubject({ ...subject });
    clearValidationErrors();
  };

  const handleCancelEdit = () => {
    setEditingSubjectId(null);
    setEditingSubject(null);
    clearValidationErrors();
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingSubject) return;

    const { name, value } = e.target;
    setEditingSubject({
      ...editingSubject,
      [name]: value,
    });

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(name, false);
      // Also clear duplicate name error when user types in name field
      if (name === "name") {
        setValidationError("duplicateSubjectName", false);
      }
    }
  };

  const handleEditClassToggle = (classId: string) => {
    if (!editingSubject) return;

    const updatedClasses = editingSubject.applicableClasses.includes(classId)
      ? editingSubject.applicableClasses.filter((id) => id !== classId)
      : [...editingSubject.applicableClasses, classId];

    setEditingSubject({
      ...editingSubject,
      applicableClasses: updatedClasses,
    });

    // Clear validation error when user selects at least one class
    if (updatedClasses.length > 0) {
      setValidationError("applicableClasses", false);
    }
  };

  const handleSaveEdit = () => {
    if (!editingSubject) return;

    clearValidationErrors();
    let isValid = true;

    // Validate form data
    if (!editingSubject.name.trim()) {
      setValidationError("name", true);
      isValid = false;
    }

    // Check for duplicate subject names (excluding current subject)
    const isDuplicateName = setupData.subjects.some(
      (existingSubject) =>
        existingSubject.id !== editingSubject.id &&
        existingSubject.name.toLowerCase().trim() ===
          editingSubject.name.toLowerCase().trim()
    );

    if (isDuplicateName) {
      setValidationError("duplicateSubjectName", true);
      isValid = false;
    }

    if (editingSubject.applicableClasses.length === 0) {
      setValidationError("applicableClasses", true);
      isValid = false;
    }

    if (!isValid) return;

    // Update subject
    const updatedSubjects = setupData.subjects.map((subject) =>
      subject.id === editingSubject.id ? editingSubject : subject
    );

    updateSubjects(updatedSubjects);
    setEditingSubjectId(null);
    setEditingSubject(null);
    toast.success(t("subjectMessages.subjectUpdated"));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center text-gray-800">
          <BookOpen className="h-5 w-5 text-purple-600 mr-2" />
          {t("setup.subjectsAndCurriculum")}
        </h2>
        {!isAddingSubject && (
          <Button
            onClick={handleAddSubject}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="h-4 w-4 mr-2" /> {t("setup.addSubject")}
          </Button>
        )}
      </div>

      {isAddingSubject && (
        <Card className="mb-6 border-purple-200 shadow-sm">
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium mb-4">
              {t("setup.addNewSubject")}
            </h3>
            <div className="grid gap-4">
              <div>
                <label
                  htmlFor="name"
                  className="text-sm font-medium mb-1 block"
                >
                  {t("setup.subjectName")}*
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder={t("setup.subjectNamePlaceholder")}
                  value={newSubject.name}
                  onChange={handleInputChange}
                  className={
                    validationErrors.name ||
                    validationErrors.duplicateSubjectName
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }
                />
                {validationErrors.name && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationSetup.subjectNameRequired")}
                  </p>
                )}
                {validationErrors.duplicateSubjectName && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationSetup.duplicateSubjectName")}
                  </p>
                )}
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  {t("setup.applicableClasses")}*
                </label>
                <div
                  className={`mb-2 ${
                    validationErrors.applicableClasses
                      ? "border border-red-500 rounded-md p-2"
                      : ""
                  }`}
                >
                  <div className="flex items-center space-x-2 mb-3 pb-2 border-b">
                    <Checkbox
                      id="select-all-classes"
                      checked={
                        newSubject.applicableClasses.length === classes.length
                      }
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setNewSubject({
                            ...newSubject,
                            applicableClasses: [...classes],
                          });
                          setValidationError("applicableClasses", false);
                        } else {
                          setNewSubject({
                            ...newSubject,
                            applicableClasses: [],
                          });
                        }
                      }}
                    />
                    <label
                      htmlFor="select-all-classes"
                      className="text-sm font-medium leading-none"
                    >
                      {t("setup.selectAllClasses")}
                    </label>
                  </div>
                  <div className="space-y-2">
                    {classOptions.length === 0 ? (
                      <p className="text-sm text-gray-500 italic">
                        {t("setup.noClassesAvailable") ||
                          "No classes available. Please add classes in the Classes step first."}
                      </p>
                    ) : (
                      classOptions.map((classOption) => (
                        <div
                          key={classOption.value}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`class-${classOption.value}`}
                            checked={newSubject.applicableClasses.includes(
                              classOption.value
                            )}
                            onCheckedChange={() =>
                              handleClassToggle(classOption.value)
                            }
                          />
                          <label
                            htmlFor={`class-${classOption.value}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {classOption.label}
                          </label>
                        </div>
                      ))
                    )}
                  </div>
                </div>
                {validationErrors.applicableClasses && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("validationSetup.selectAtLeastOneClass")}
                  </p>
                )}
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={handleCancelAdd}>
                  {t("setup.cancel")}
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t("setup.saveSubject")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {setupData.subjects.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {setupData.subjects.map((subject) => (
            <div key={subject.id} className="border rounded-md p-4 relative">
              {editingSubjectId === subject.id ? (
                // Edit mode
                <div className="space-y-3">
                  <div className="flex justify-end gap-1 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSaveEdit}
                      className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                    >
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCancelEdit}
                      className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div>
                    <Input
                      name="name"
                      value={editingSubject?.name || ""}
                      onChange={handleEditInputChange}
                      placeholder={t("setup.subjectName")}
                      className={`text-sm ${
                        validationErrors.name ||
                        validationErrors.duplicateSubjectName
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    />
                    {validationErrors.name && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.subjectNameRequired")}
                      </p>
                    )}
                    {validationErrors.duplicateSubjectName && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.duplicateSubjectName")}
                      </p>
                    )}
                  </div>

                  <div>
                    <p className="text-xs font-medium text-gray-600 mb-2">
                      {t("setup.applicableClasses")}*
                    </p>
                    <div
                      className={`space-y-2 ${
                        validationErrors.applicableClasses
                          ? "border border-red-500 rounded-md p-2"
                          : ""
                      }`}
                    >
                      <div className="flex items-center space-x-2 mb-2 pb-1 border-b">
                        <Checkbox
                          id={`edit-select-all-${subject.id}`}
                          checked={
                            editingSubject?.applicableClasses.length ===
                            classes.length
                          }
                          onCheckedChange={(checked) => {
                            if (!editingSubject) return;
                            if (checked) {
                              setEditingSubject({
                                ...editingSubject,
                                applicableClasses: [...classes],
                              });
                              setValidationError("applicableClasses", false);
                            } else {
                              setEditingSubject({
                                ...editingSubject,
                                applicableClasses: [],
                              });
                            }
                          }}
                        />
                        <label
                          htmlFor={`edit-select-all-${subject.id}`}
                          className="text-xs font-medium leading-none"
                        >
                          {t("setup.selectAll")}
                        </label>
                      </div>
                      <div className="space-y-1">
                        {classOptions.length === 0 ? (
                          <p className="text-xs text-gray-500 italic">
                            {t("setup.noClassesAvailable") ||
                              "No classes available. Please add classes in the Classes step first."}
                          </p>
                        ) : (
                          classOptions.map((classOption) => (
                            <div
                              key={classOption.value}
                              className="flex items-center space-x-1"
                            >
                              <Checkbox
                                id={`edit-class-${subject.id}-${classOption.value}`}
                                checked={
                                  editingSubject?.applicableClasses.includes(
                                    classOption.value
                                  ) || false
                                }
                                onCheckedChange={() =>
                                  handleEditClassToggle(classOption.value)
                                }
                              />
                              <label
                                htmlFor={`edit-class-${subject.id}-${classOption.value}`}
                                className="text-xs leading-none"
                              >
                                {classOption.label}
                              </label>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                    {validationErrors.applicableClasses && (
                      <p className="text-red-500 text-xs mt-1">
                        {t("validationSetup.selectAtLeastOneClass")}
                      </p>
                    )}
                  </div>
                </div>
              ) : (
                // Display mode
                <>
                  <div className="flex justify-end gap-1 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditSubject(subject)}
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteSubject(subject.id)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <h3 className="font-medium text-gray-900">{subject.name}</h3>
                  <p className="text-sm text-gray-500">
                    {t("setup.applicableToClasses")}:{" "}
                    {getClassNamesFromIds(subject.applicableClasses) ||
                      t("setup.noClassesSelected")}
                  </p>
                </>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="border rounded-md p-8 text-center text-gray-500">
          {t("setup.noSubjectsAdded")}
        </div>
      )}

      <p className="text-sm text-gray-500 mt-6">
        * {t("setup.requiredFields")}
      </p>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteConfirmation.confirmDeleteSubject")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteConfirmation.confirmDeleteSubjectMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteSubject}>
              {t("deleteConfirmation.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteSubject}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("deleteConfirmation.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
