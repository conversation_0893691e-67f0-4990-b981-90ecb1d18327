import { useState } from "react";
import { useTranslation } from "react-i18next";
import AttendanceCard from "../../components/AttendanceCard";
import WarningManagement from "../../components/WarningManagement/WarningManagement";

const attendanceData = [
  {
    subject: "Mathematics",
    date: "Mon, Mar 10, 2025",
    reason: "Doctor appointment",
    status: "Excused",
  },
  {
    subject: "Physics",
    date: "Tue, Mar 11, 2025",
    reason: "Missed bus",
    status: "Unexcused",
  },
  {
    subject: "Biology",
    date: "Wed, Mar 12, 2025",
    reason: "Family emergency",
    status: "Pending",
  },
];

export default function StudentAttendancePage() {
  const { t } = useTranslation();
  const [filterStatus, setFilterStatus] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");

  const statusOptions = ["All", "Excused", "Unexcused", "Pending"];

  const filteredAttendanceData = attendanceData.filter((item) => {
    const matchesStatus =
      filterStatus === "All" || item.status === filterStatus;
    const matchesSearch = item.subject
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl sm:text-3xl font-bold">
          {t("attendance.myAttendance")}
        </h2>
      </div>

      <div className="bg-white p-4 sm:p-5 border border-gray-300 rounded-md">
        <div className="pb-4">
          <p className="text-xl font-bold">{t("attendance.absenceLog")}</p>
          <p className="text-sm sm:text-base">
            {t("attendance.recordabsences")}
          </p>
        </div>

        {/* Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-grow">
            <input
              type="text"
              placeholder={t("attendance.searchBySubject")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border rounded-md px-4 py-2 w-full"
            />
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilterStatus("All")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "All"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.all")}
            </button>
            <button
              onClick={() => setFilterStatus("Excused")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "Excused"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.excused")}
              {filterStatus !== "Excused" && (
                <span className="ml-1">
                  (
                  {
                    attendanceData.filter((item) => item.status === "Excused")
                      .length
                  }
                  )
                </span>
              )}
            </button>
            <button
              onClick={() => setFilterStatus("Unexcused")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "Unexcused"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.unexcused")}
              {filterStatus !== "Unexcused" && (
                <span className="ml-1">
                  (
                  {
                    attendanceData.filter((item) => item.status === "Unexcused")
                      .length
                  }
                  )
                </span>
              )}
            </button>
            <button
              onClick={() => setFilterStatus("Pending")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "Pending"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.pending")}
              {filterStatus !== "Pending" && (
                <span className="ml-1">
                  (
                  {
                    attendanceData.filter((item) => item.status === "Pending")
                      .length
                  }
                  )
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Attendance Cards */}
        <div className="space-y-2 sm:space-y-4">
          {filteredAttendanceData.map((item, index) => (
            <AttendanceCard
              key={index}
              subject={item.subject}
              date={item.date}
              reason={item.reason}
              status={item.status}
            />
          ))}
        </div>

        {filteredAttendanceData.length === 0 && (
          <p className="text-center text-gray-500 py-4">
            {t("attendance.noRecordsFound")}
          </p>
        )}
      </div>

      <WarningManagement
        readOnly={true}
        studentId="current-user-id" // In a real app, get this from auth context
        title={t("warnings.myWarnings")}
        description={t("warnings.studentDescription")}
      />
    </div>
  );
}
