import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Search, Check } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface Student {
  id: string;
  name: string;
}

interface AddGradeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

export default function AddGradeDialog({
  isOpen,
  onClose,
  onSubmit,
}: AddGradeDialogProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmingGrade, setConfirmingGrade] = useState<{
    studentId: string;
    studentName: string;
    grade: string;
  } | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Mock student data - replace with actual data from your API
  const students: Student[] = [
    { id: "1", name: "John Smith" },
    { id: "2", name: "Alice Johnson" },
    { id: "3", name: "Michael Brown" },
    { id: "4", name: "Emily Wilson" },
    { id: "5", name: "Daniel Miller" },
    { id: "6", name: "Olivia Martinez" },
    { id: "7", name: "James Anderson" },
  ];

  // Filter students based on search term
  const filteredStudents = students
    .filter((student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  // Mock subjects - replace with actual data
  const subjects = [
    "Mathematics",
    "Literature",
    "Physics",
    "Chemistry",
    "Biology",
  ];

  const [formData, setFormData] = useState({
    subject: "",
    date: new Date().toISOString().split("T")[0],
    students: students.map((student) => ({
      studentId: student.id,
      grade: "",
    })),
  });

  const handleSubjectChange = (value: string) => {
    setFormData({
      ...formData,
      subject: value,
    });

    // Clear validation error when subject is selected
    setValidationError(null);
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      date: e.target.value,
    });
  };

  const handleGradeChange = (
    studentId: string,
    studentName: string,
    grade: string
  ) => {
    const currentGrade = formData.students.find(
      (record) => record.studentId === studentId
    )?.grade;

    if (!formData.subject) {
      setValidationError(t("grades.subjectRequired"));
      return;
    }

    setValidationError(null);

    const isRemoving = currentGrade === grade;

    setConfirmingGrade({
      studentId,
      studentName,
      grade: isRemoving ? "" : grade,
    });

    setConfirmDialogOpen(true);
  };

  const confirmGradeAssignment = () => {
    if (!confirmingGrade) return;

    // Update the grade in the form data
    setFormData((prevData) => ({
      ...prevData,
      students: prevData.students.map((record) =>
        record.studentId === confirmingGrade.studentId
          ? {
              ...record,
              grade:
                record.grade === confirmingGrade.grade
                  ? ""
                  : confirmingGrade.grade,
            }
          : record
      ),
    }));

    // Reset confirmation state
    setConfirmDialogOpen(false);
    setConfirmingGrade(null);
  };

  const cancelGradeAssignment = () => {
    // Just reset confirmation state
    setConfirmDialogOpen(false);
    setConfirmingGrade(null);
  };

  const handleSubmit = () => {
    // Validate before submission
    if (!formData.subject) {
      setValidationError(t("grades.subjectRequired"));
      return;
    }

    onSubmit(formData);
    onClose();
  };

  return (
    <>
      <Dialog
        open={isOpen}
        onOpenChange={(open) => {
          // Only close the main dialog when explicitly requested
          if (!open) onClose();
        }}
      >
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">
              {t("grades.addNewGrade")}
            </DialogTitle>
          </DialogHeader>

          <div className="mt-4 space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  {t("grades.subject")}
                </label>
                <Select
                  value={formData.subject}
                  onValueChange={handleSubjectChange}
                >
                  <SelectTrigger
                    className={`w-full ${
                      !formData.subject && validationError
                        ? "border-red-500 ring-red-500"
                        : ""
                    }`}
                  >
                    <SelectValue placeholder={t("grades.selectSubject")} />
                  </SelectTrigger>
                  <SelectContent>
                    {subjects.map((subject) => (
                      <SelectItem key={subject} value={subject}>
                        {subject}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationError && (
                  <p className="text-red-500 text-sm mt-1">{validationError}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  {t("grades.date")}
                </label>
                <Input
                  type="date"
                  value={formData.date}
                  onChange={handleDateChange}
                />
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-sm font-medium mb-2">
                {t("grades.studentGrades")}
              </h3>

              {/* Add search input */}
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder={t("grades.searchStudents")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="border rounded-md overflow-hidden overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t("grades.student")}
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t("grades.grade")}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredStudents.map((student) => {
                      const record = formData.students.find(
                        (r) => r.studentId === student.id
                      )!;
                      return (
                        <tr key={student.id} className="hover:bg-gray-50">
                          <td className="px-4 py-3 text-sm">{student.name}</td>
                          <td className="px-4 py-3 text-sm">
                            <div className="flex flex-wrap gap-2 sm:gap-3">
                              {[1, 2, 3, 4, 5].map((gradeValue) => (
                                <button
                                  key={gradeValue}
                                  type="button"
                                  onClick={() =>
                                    handleGradeChange(
                                      student.id,
                                      student.name,
                                      gradeValue.toString()
                                    )
                                  }
                                  className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-colors ${
                                    record.grade === gradeValue.toString()
                                      ? "bg-purple-600 text-white"
                                      : "bg-white border border-gray-300 hover:bg-gray-100"
                                  }`}
                                >
                                  {gradeValue}
                                </button>
                              ))}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Button
              onClick={handleSubmit}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white"
            >
              {t("submit")}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      {confirmDialogOpen && (
        <AlertDialog
          open={confirmDialogOpen}
          onOpenChange={setConfirmDialogOpen}
        >
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>
                {t("grades.confirmGradeAssignment")}
              </AlertDialogTitle>
              <AlertDialogDescription>
                {confirmingGrade?.grade
                  ? `${t("grades.confirmGradeQuestion")} ${
                      confirmingGrade.grade
                    } ${t("grades.to")} ${confirmingGrade.studentName} ${t(
                      "grades.for"
                    )} ${formData.subject || "this subject"}?`
                  : `${t("grades.removeGradeQuestion")} ${
                      confirmingGrade?.studentName
                    } ${t("grades.for")} ${
                      formData.subject || "this subject"
                    }?`}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={cancelGradeAssignment}>
                {t("grades.cancel")}
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmGradeAssignment}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Check className="mr-1 h-4 w-4" />
                {t("grades.confirm")}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}
    </>
  );
}
