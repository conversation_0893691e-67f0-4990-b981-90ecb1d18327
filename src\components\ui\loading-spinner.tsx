import { SlGraduation } from "react-icons/sl";
import { FaCheckCircle, FaCalendarAlt, FaMedal, FaBook } from "react-icons/fa";

interface LoadingSpinnerProps {
  text?: string;
  size?: "tiny" | "small" | "medium" | "large";
  showIcons?: boolean;
  className?: string;
}

export function LoadingSpinner({
  text = "Loading...",
  size = "medium",
  showIcons = true,
  className = "",
}: LoadingSpinnerProps) {
  // Size mappings
  const sizeMap = {
    tiny: {
      container: "w-8 h-8",
      icon: "w-3 h-3",
      text: "text-xs",
      floatingIcons: "w-16 h-16",
      floatingIconSize: "w-2 h-2",
      floatingDistance: 8,
    },
    small: {
      container: "w-12 h-12",
      icon: "w-4 h-4",
      text: "text-sm",
      floatingIcons: "w-24 h-24",
      floatingIconSize: "w-3 h-3",
      floatingDistance: 10,
    },
    medium: {
      container: "w-16 h-16",
      icon: "w-6 h-6",
      text: "text-base",
      floatingIcons: "w-32 h-32",
      floatingIconSize: "w-4 h-4",
      floatingDistance: 14,
    },
    large: {
      container: "w-20 h-20",
      icon: "w-8 h-8",
      text: "text-lg",
      floatingIcons: "w-40 h-40",
      floatingIconSize: "w-5 h-5",
      floatingDistance: 16,
    },
  };

  const currentSize = sizeMap[size];

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className="relative">
        {/* Outer circle */}
        <div
          className={`${currentSize.container} border-4 border-purple-200 rounded-full`}
        ></div>

        {/* Spinning loader */}
        <div
          className={`absolute top-0 left-0 ${currentSize.container} border-4 border-transparent border-t-purple-600 rounded-full animate-spin`}
        ></div>

        {/* Center icon */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-purple-600">
          <SlGraduation className={currentSize.icon} />
        </div>
      </div>

      {text && (
        <p className={`mt-3 text-gray-600 font-medium ${currentSize.text}`}>
          {text}
        </p>
      )}

      {/* Small floating icons around the loader */}
      {showIcons && (
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className={`relative ${currentSize.floatingIcons}`}>
            <div
              className={`absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-${currentSize.floatingDistance} p-1.5 rounded-lg bg-purple-500 animate-ping-slow`}
            >
              <FaCheckCircle
                className={`text-white ${currentSize.floatingIconSize}`}
              />
            </div>

            <div
              className={`absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-${currentSize.floatingDistance} p-1.5 rounded-lg bg-green-500 animate-ping-slow`}
              style={{ animationDelay: "0.5s" }}
            >
              <FaCalendarAlt
                className={`text-white ${currentSize.floatingIconSize}`}
              />
            </div>

            <div
              className={`absolute left-0 top-1/2 transform -translate-x-${currentSize.floatingDistance} -translate-y-1/2 p-1.5 rounded-lg bg-blue-500 animate-ping-slow`}
              style={{ animationDelay: "1s" }}
            >
              <FaBook
                className={`text-white ${currentSize.floatingIconSize}`}
              />
            </div>

            <div
              className={`absolute right-0 top-1/2 transform translate-x-${currentSize.floatingDistance} -translate-y-1/2 p-1.5 rounded-lg bg-orange-500 animate-ping-slow`}
              style={{ animationDelay: "1.5s" }}
            >
              <FaMedal
                className={`text-white ${currentSize.floatingIconSize}`}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
