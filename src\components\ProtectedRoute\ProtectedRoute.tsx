import { Navigate, Outlet } from "react-router-dom";
import { useIsAuthenticated, useTypedAuthUser } from "../../hooks/useAuth";
import {
  studentNavItems,
  teacherNavItems,
  parentNavItems,
  directorNavItems,
} from "../../components/Sidebar/SideNav";

const ProtectedRoute = () => {
  const isAuthenticated = useIsAuthenticated();
  const authUser = useTypedAuthUser();

  // Check if user is authenticated
  if (!isAuthenticated || !authUser) {
    return <Navigate to="/login" replace />;
  }

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser.role?.trim().toLowerCase();

  // // If user is admin and setup is not completed, redirect to setup wizard
  // if (normalizedRole === "admin" && !setupCompleted) {

  //   return <Navigate to="/setup" replace />;
  // }

  // Define role-based allowed paths for each user role
  const allowedPaths = {
    student: [
      ...studentNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
      "/debug-auth", // Debug page for troubleshooting
    ],
    teacher: [
      ...teacherNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
      "/debug-auth", // Debug page for troubleshooting
      "/roadmap", // Roadmap page for teachers
    ],
    parent: [
      ...parentNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
      "/debug-auth", // Debug page for troubleshooting
    ],
    admin: [
      ...directorNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
      "/debug-auth", // Debug page for troubleshooting
      "/setup", // Setup wizard for admin users
    ],
  };

  // Use normalized role for path lookup
  const userAllowedPaths =
    allowedPaths[normalizedRole as keyof typeof allowedPaths] || [];

  // Check if current path is allowed for the user's role
  if (!userAllowedPaths.includes(window.location.pathname)) {
    return <Navigate to="/unauthorized" />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
