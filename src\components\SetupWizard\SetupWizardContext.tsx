import React, {
  create<PERSON>ontext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";

// Define interfaces for periods
export interface Period {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
}

// Define interface for parents
export interface Parent {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  idCardNumber: string;
  gender: string;
}

// Define the shared state interface
export interface SetupWizardState {
  basicInfo: {
    schoolName: string;
    city: string;
    schoolCategory: string;
    locationType: string;
    villageName: string;
    mission: string;
    vision: string;
  };
  staff: Array<{
    id: string;
    firstName: string;
    lastName: string;
    role: string;
    email: string;
    phone: string;
    gender: string;
    idNumber: string;
  }>;
  configuration: {
    periods: Period[];
    language: string;
    timezone: string;
    currency: string;
    schoolCycle: string;
  };
  classes: Array<{
    id: string;
    grade: string;
    className: string;
    section: string;
    responsibleTeacher?: string;
  }>;
  subjects: Array<{
    id: string;
    name: string;
    applicableClasses: string[];
  }>;
  students: Array<{
    id: string;
    firstName: string;
    lastName: string;
    birthday: string;
    idCardNumber: string;
    grade: string;
    section: string;
    gender: string;
    parents: Parent[];
  }>;
  shifts: {
    generalInfo: {
      shortBreakLength: number;
      longBreakLength: number;
      longBreakLocation: number;
    };
    shiftList: Array<{
      id: string;
      number: number;
      name: string;
      startTime: string;
      maxClasses: number;
    }>;
  };
}

interface ValidationErrors {
  [key: string]: boolean;
}

interface SetupWizardContextType {
  setupData: SetupWizardState;
  updateBasicInfo: (data: Partial<SetupWizardState["basicInfo"]>) => void;
  updateStaff: (data: SetupWizardState["staff"]) => void;
  updateConfiguration: (
    data: Partial<SetupWizardState["configuration"]>
  ) => void;
  updateClasses: (data: SetupWizardState["classes"]) => void;
  updateSubjects: (data: SetupWizardState["subjects"]) => void;
  updateStudents: (data: SetupWizardState["students"]) => void;
  updateShifts: (data: SetupWizardState["shifts"]) => void;
  validationErrors: ValidationErrors;
  setValidationError: (field: string, hasError: boolean) => void;
  clearValidationErrors: () => void;
}

// Create the context
const SetupWizardContext = createContext<SetupWizardContextType | undefined>(
  undefined
);

// Initial state
const initialState: SetupWizardState = {
  basicInfo: {
    schoolName: "",
    city: "",
    schoolCategory: "",
    locationType: "",
    villageName: "",
    mission: "",
    vision: "",
  },
  staff: [],
  configuration: {
    periods: [],
    language: "",
    timezone: "",
    currency: "",
    schoolCycle: "",
  },
  classes: [],
  subjects: [],
  students: [],
  shifts: {
    generalInfo: {
      shortBreakLength: 5,
      longBreakLength: 15,
      longBreakLocation: 3,
    },
    shiftList: [],
  },
};

// Provider component
export function SetupWizardProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  // Initialize state from localStorage if available
  const savedData = localStorage.getItem("setupData");
  const parsedData = savedData ? JSON.parse(savedData) : initialState;

  const [setupData, setSetupData] = useState<SetupWizardState>(parsedData);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {}
  );

  const setValidationError = useCallback((field: string, hasError: boolean) => {
    setValidationErrors(prev => ({
      ...prev,
      [field]: hasError,
    }));
  }, []);

  const clearValidationErrors = () => {
    setValidationErrors({});
  };

  // Save to localStorage whenever state changes
  useEffect(() => {
    localStorage.setItem("setupData", JSON.stringify(setupData));
  }, [setupData]);

  const updateBasicInfo = (data: Partial<SetupWizardState["basicInfo"]>) => {
    setSetupData(prev => ({
      ...prev,
      basicInfo: { ...prev.basicInfo, ...data },
    }));
  };

  const updateStaff = (data: SetupWizardState["staff"]) => {
    setSetupData(prev => ({ ...prev, staff: data }));
  };

  const updateConfiguration = (
    data: Partial<SetupWizardState["configuration"]>
  ) => {
    setSetupData(prev => ({
      ...prev,
      configuration: { ...prev.configuration, ...data },
    }));
  };

  const updateClasses = (data: SetupWizardState["classes"]) => {
    setSetupData(prev => ({ ...prev, classes: data }));
  };

  const updateSubjects = (data: SetupWizardState["subjects"]) => {
    setSetupData(prev => ({ ...prev, subjects: data }));
  };

  const updateStudents = (data: SetupWizardState["students"]) => {
    setSetupData(prev => ({ ...prev, students: data }));
  };

  const updateShifts = (data: SetupWizardState["shifts"]) => {
    setSetupData(prev => ({ ...prev, shifts: data }));
  };

  return (
    <SetupWizardContext.Provider
      value={{
        setupData,
        updateBasicInfo,
        updateStaff,
        updateConfiguration,
        updateClasses,
        updateSubjects,
        updateStudents,
        updateShifts,
        validationErrors,
        setValidationError,
        clearValidationErrors,
      }}
    >
      {children}
    </SetupWizardContext.Provider>
  );
}

// Custom hook to use the context
export function useSetupWizard() {
  const context = useContext(SetupWizardContext);
  if (context === undefined) {
    throw new Error("useSetupWizard must be used within a SetupWizardProvider");
  }
  return context;
}
