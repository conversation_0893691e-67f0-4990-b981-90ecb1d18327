import { useStore, type AppState } from "@/lib/store";
import { LoadingSpinner } from "./loading-spinner";

interface LoadingIndicatorProps {
  type?: keyof AppState["loadingStates"];
  size?: "small" | "medium" | "large";
  showIcons?: boolean;
  text?: string;
}

export function LoadingIndicator({
  type,
  size = "small",
  showIcons = false,
  text = "Loading...",
}: LoadingIndicatorProps) {
  const isLoading = useStore((state) =>
    type
      ? state.loadingStates[type as keyof typeof state.loadingStates]
      : Object.values(state.loadingStates).some(Boolean)
  );

  if (!isLoading) return null;

  return (
    <div className="flex items-center justify-center p-2">
      <LoadingSpinner size={size} showIcons={showIcons} text={text} />
    </div>
  );
}
