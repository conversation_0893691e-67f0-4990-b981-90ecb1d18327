import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  Cell,
  Legend,
  CartesianGrid,
} from "recharts";

interface PerformanceData {
  subject: string;
  score: number;
  color: string;
  year?: string;
  month?: string;
  school?: string;
}

interface AcademicPerformanceProps {
  data: PerformanceData[];
  title?: string;
  subtitle?: string;
  showYearLegend?: boolean;
  isMonthly?: boolean;
  isSchoolPerformance?: boolean;
}

export default function AcademicPerformance({
  data,
  title,
  subtitle,
  showYearLegend = false,
  isMonthly = false,
  isSchoolPerformance = false,
}: AcademicPerformanceProps) {
  const { t } = useTranslation();

  // Group data by subject/month and year if years are present
  const hasYears = data.some((item) => item.year);
  const hasSchools = data.some((item) => item.school);

  // Get unique subjects/months and years/schools
  const categories = isMonthly
    ? [
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "Nëntor",
        "<PERSON><PERSON><PERSON><PERSON>",
      ]
    : [...new Set(data.map((item) => item.subject))];

  const years = hasYears
    ? [...new Set(data.map((item) => item.year).filter(Boolean))]
    : [];

  const schools = hasSchools
    ? [...new Set(data.map((item) => item.school).filter(Boolean))]
    : [];

  const transformedData = hasYears
    ? categories.map((category) => {
        const result: any = { category };

        if (isMonthly) {
          years.forEach((year) => {
            result[year as string] = 0;
          });

          data.forEach((item) => {
            if (item.month === category && item.year) {
              result[item.year] = item.score;
            }
          });
        } else {
          data.forEach((item) => {
            if (item.subject === category && item.year) {
              result[item.year] = item.score;
              result[`${item.year}Color`] = item.color;
            }
          });
        }
        return result;
      })
    : hasSchools && isSchoolPerformance
    ? categories.map((category) => {
        const result: any = { category };

        // Initialize with zero values
        schools.forEach((school) => {
          result[school as string] = 0;
        });

        // Fill in actual data where available
        data.forEach((item) => {
          if (item.month === category && item.school) {
            result[item.school] = item.score;
          }
        });
        return result;
      })
    : isMonthly
    ? categories.map((month) => {
        const monthData = data.find((item) => item.month === month);
        return {
          category: month,
          score: monthData?.score || 0,
          color: monthData?.color || "#cccccc",
        };
      })
    : data.map((item) => ({ ...item, category: item.subject }));

  return (
    <div className="bg-white rounded-md border p-6 border-gray-300">
      <h2 className="text-xl font-semibold text-gray-900 mb-2">
        {title || t("academicPerformance")}
      </h2>
      <h3 className="text-md font-medium text-gray-700 mb-4">
        {subtitle ||
          (isMonthly ? t("monthlyPerformance") : t("subjectPerformance"))}
      </h3>
      <ResponsiveContainer width="100%" height={400}>
        <BarChart
          data={transformedData}
          margin={{ top: 20, right: 20, left: 20, bottom: 20 }}
          barGap={hasYears || hasSchools ? 0 : 0}
        >
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis
            dataKey="category"
            tick={{ fontSize: 15 }}
            tickMargin={15}
            angle={-45}
            textAnchor="end"
            height={120}
            interval={0}
          />
          <YAxis
            domain={[0, "dataMax + 10"]}
            tickCount={5}
            axisLine={false}
            tickLine={false}
          />
          <Tooltip
            formatter={(value: number) =>
              isMonthly ? `${value}` : `${value}%`
            }
            cursor={{ fill: "transparent" }}
          />

          {hasYears ? (
            // Render multiple bars for each year
            years.map((year) => (
              <Bar
                key={year}
                dataKey={year as string}
                name={year as string}
                radius={[4, 4, 0, 0]}
                fill={year === "2024" ? "#22c55e" : "#7b3aed"}
                maxBarSize={40}
              />
            ))
          ) : hasSchools && isSchoolPerformance ? (
            schools.map((school, index) => (
              <Bar
                key={school}
                dataKey={school as string}
                name={school as string}
                radius={[4, 4, 0, 0]}
                fill={index === 0 ? "#22c55e" : "#3b82f6"}
                maxBarSize={40}
              />
            ))
          ) : (
            <Bar dataKey="score" radius={[4, 4, 0, 0]} maxBarSize={40}>
              {transformedData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Bar>
          )}

          {(showYearLegend && hasYears) || (showYearLegend && hasSchools) ? (
            <Legend />
          ) : null}
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
}
