import { useTypedAuthUser } from "../../hooks/useAuth";
import StudentSchedule from "./StudentSchedule";
import ParentSchedule from "./ParentSchedule";
import DirectorSchedule from "./DirectorSchedule";
import TeacherSchedule from "./TeacherSchedule";

export default function SchedulePage() {
  const authUser = useTypedAuthUser();

  const normalizedRole = authUser?.role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return <StudentSchedule />;
    case "teacher":
      return <TeacherSchedule />;
    case "parent":
      return <ParentSchedule />;
    case "admin":
      return <DirectorSchedule />;
    default:
      console.warn("SchedulePage: Invalid or unknown role", {
        originalRole: authUser?.role,
        normalizedRole,
      });
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Invalid Role
          </h2>
          <p className="text-gray-600">
            Your account role "{authUser?.role}" is not recognized. Please
            contact support.
          </p>
        </div>
      );
  }
}
