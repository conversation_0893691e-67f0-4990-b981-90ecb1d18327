import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { BookO<PERSON>, Plus, Trash2, Save, Edit, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import toast from "react-hot-toast";
import { useSetupWizard } from "../SetupWizardContext";

interface ClassSection {
  id: string;
  grade: string;
  className: string;
  section: string;
  responsibleTeacher?: string;
}

export default function ClassesAndSectionsStep() {
  const { t } = useTranslation();
  const {
    setupData,
    updateClasses,
    validationErrors,
    setValidationError,
    clearValidationErrors,
  } = useSetupWizard();

  // Get teachers from staff members with teaching roles
  const getAvailableTeachers = () => {
    return setupData.staff
      .filter((staff) =>
        ["Teacher", "Subject Teacher", "Professor"].includes(staff.role)
      )
      .map((staff) => ({
        id: staff.id,
        name: `${staff.firstName} ${staff.lastName}`,
        fullData: staff,
      }));
  };

  const availableTeachers = getAvailableTeachers();

  // Helper function to get teacher name by ID
  const getTeacherNameById = (teacherId: string) => {
    const teacher = availableTeachers.find((t) => t.id === teacherId);
    return teacher ? teacher.name : t("setup.noTeacherAssigned");
  };

  const [isAddingClass, setIsAddingClass] = useState(false);
  const [editingClassId, setEditingClassId] = useState<string | null>(null);
  const [newClass, setNewClass] = useState({
    grade: "",
    className: "",
    section: "",
    responsibleTeacher: "",
  });
  const [editingClass, setEditingClass] = useState<ClassSection | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [classToDelete, setClassToDelete] = useState<string | null>(null);

  // Get grade options based on selected school cycle
  const getGradeOptions = () => {
    const schoolCycle = setupData.configuration.schoolCycle;

    if (schoolCycle === "elementary-1-5") {
      return [
        { value: "1", label: t("setup.grade1") || "Grade 1" },
        { value: "2", label: t("setup.grade2") || "Grade 2" },
        { value: "3", label: t("setup.grade3") || "Grade 3" },
        { value: "4", label: t("setup.grade4") || "Grade 4" },
        { value: "5", label: t("setup.grade5") || "Grade 5" },
      ];
    } else if (schoolCycle === "elementary-1-9") {
      return [
        { value: "1", label: t("setup.grade1") || "Grade 1" },
        { value: "2", label: t("setup.grade2") || "Grade 2" },
        { value: "3", label: t("setup.grade3") || "Grade 3" },
        { value: "4", label: t("setup.grade4") || "Grade 4" },
        { value: "5", label: t("setup.grade5") || "Grade 5" },
        { value: "6", label: t("setup.grade6") || "Grade 6" },
        { value: "7", label: t("setup.grade7") || "Grade 7" },
        { value: "8", label: t("setup.grade8") || "Grade 8" },
        { value: "9", label: t("setup.grade9") || "Grade 9" },
      ];
    } else if (schoolCycle === "middle-6-9") {
      return [
        { value: "6", label: t("setup.grade6") || "Grade 6" },
        { value: "7", label: t("setup.grade7") || "Grade 7" },
        { value: "8", label: t("setup.grade8") || "Grade 8" },
        { value: "9", label: t("setup.grade9") || "Grade 9" },
      ];
    } else if (schoolCycle === "high-10-12") {
      return [
        { value: "10", label: t("setup.grade10") || "Grade 10" },
        { value: "11", label: t("setup.grade11") || "Grade 11" },
        { value: "12", label: t("setup.grade12") || "Grade 12" },
      ];
    }

    return [];
  };

  const gradeOptions = getGradeOptions();
  const isGradeDisabled =
    !setupData.configuration.schoolCycle || gradeOptions.length === 0;

  const handleAddClass = () => {
    setIsAddingClass(true);
    setNewClass({
      grade: "",
      className: "",
      section: "",
      responsibleTeacher: "",
    });
  };

  const handleCancelAdd = () => {
    setIsAddingClass(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewClass({
      ...newClass,
      [name]: value,
    });

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(name, false);
    }

    if (name === "section") {
      setValidationError("duplicateGradeSection", false);
    }
  };

  const handleTeacherChange = (value: string) => {
    setNewClass({
      ...newClass,
      responsibleTeacher: value,
    });

    if (value.trim() !== "") {
      setValidationError("responsibleTeacher", false);
    }
  };

  const handleGradeChange = (value: string) => {
    setNewClass({
      ...newClass,
      grade: value,
    });

    if (value.trim() !== "") {
      setValidationError("grade", false);
    }

    setValidationError("duplicateGradeSection", false);
  };

  const handleSubmit = () => {
    clearValidationErrors();
    let isValid = true;

    // Validate form data
    if (!newClass.grade.trim()) {
      setValidationError("grade", true);
      isValid = false;
    }

    if (!newClass.className.trim()) {
      setValidationError("className", true);
      isValid = false;
    }

    if (!newClass.section.trim()) {
      setValidationError("section", true);
      isValid = false;
    }

    if (!newClass.responsibleTeacher.trim()) {
      setValidationError("responsibleTeacher", true);
      isValid = false;
    }

    const isDuplicate = setupData.classes.some(
      (existingClass) =>
        existingClass.grade === newClass.grade &&
        existingClass.section === newClass.section
    );

    if (isDuplicate) {
      setValidationError("duplicateGradeSection", true);
      isValid = false;
    }

    if (!isValid) return;

    // Add new class
    const newClassSection: ClassSection = {
      id: Date.now().toString(),
      ...newClass,
    };

    const updatedClasses = [...setupData.classes, newClassSection];
    updateClasses(updatedClasses);
    setIsAddingClass(false);
    toast.success(t("classMessages.classAdded"));
  };

  const handleDeleteClass = (id: string) => {
    setClassToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteClass = () => {
    if (classToDelete) {
      const updatedClasses = setupData.classes.filter(
        (cls: ClassSection) => cls.id !== classToDelete
      );
      updateClasses(updatedClasses);
      toast.success(t("classMessages.classRemoved"));
      setDeleteDialogOpen(false);
      setClassToDelete(null);
    }
  };

  const cancelDeleteClass = () => {
    setDeleteDialogOpen(false);
    setClassToDelete(null);
  };

  const handleEditClass = (classSection: ClassSection) => {
    setEditingClassId(classSection.id);
    setEditingClass({ ...classSection });
    clearValidationErrors();
  };

  const handleCancelEdit = () => {
    setEditingClassId(null);
    setEditingClass(null);
    clearValidationErrors();
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingClass) return;

    const { name, value } = e.target;
    setEditingClass({
      ...editingClass,
      [name]: value,
    });

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(
        `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
        false
      );
    }

    if (name === "section") {
      setValidationError("editDuplicateGradeSection", false);
    }
  };

  const handleEditSelectChange = (name: string, value: string) => {
    if (!editingClass) return;

    setEditingClass({
      ...editingClass,
      [name]: value,
    });

    // Clear validation error when user selects
    setValidationError(
      `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
      false
    );

    if (name === "grade") {
      setValidationError("editDuplicateGradeSection", false);
    }
  };

  const handleSaveEdit = () => {
    if (!editingClass) return;

    clearValidationErrors();
    let isValid = true;

    // Validate form data
    if (!editingClass.grade.trim()) {
      setValidationError("editGrade", true);
      isValid = false;
    }

    if (!editingClass.className.trim()) {
      setValidationError("editClassName", true);
      isValid = false;
    }

    if (!editingClass.section.trim()) {
      setValidationError("editSection", true);
      isValid = false;
    }

    if (!editingClass.responsibleTeacher?.trim()) {
      setValidationError("editResponsibleTeacher", true);
      isValid = false;
    }

    // Check for duplicate grade/section combination (excluding current class)
    const isDuplicate = setupData.classes.some(
      (existingClass) =>
        existingClass.id !== editingClass.id &&
        existingClass.grade === editingClass.grade &&
        existingClass.section === editingClass.section
    );

    if (isDuplicate) {
      setValidationError("editDuplicateGradeSection", true);
      isValid = false;
    }

    if (!isValid) return;

    // Update class
    const updatedClasses = setupData.classes.map((cls) =>
      cls.id === editingClass.id ? editingClass : cls
    );

    updateClasses(updatedClasses);
    setEditingClassId(null);
    setEditingClass(null);
    toast.success(t("classMessages.classUpdated"));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center text-gray-800">
          <BookOpen className="h-5 w-5 text-purple-600 mr-2" />
          {t("setup.classesAndSections")}
        </h2>
        {!isAddingClass && (
          <Button
            onClick={handleAddClass}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="h-4 w-4 mr-2" /> {t("setup.addClass")}
          </Button>
        )}
      </div>

      <p className="text-gray-600 mb-2">
        {t("setup.addClassesAndSections")}: {t("setup.notSet")}
      </p>
      <p className="text-gray-500 text-sm mb-4">{t("setup.forEachClass")}</p>

      {isAddingClass && (
        <Card className="mb-6 border-purple-200 shadow-sm">
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium mb-4">
              {t("setup.addNewClass")}
            </h3>
            <div className="grid gap-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="grade"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.gradeLevel")}*
                  </label>
                  {isGradeDisabled ? (
                    <Input
                      value={
                        !setupData.configuration.schoolCycle
                          ? t("setup.selectSchoolCycleFirst") ||
                            "Select school cycle first"
                          : t("setup.noGradesAvailable") ||
                            "No grades available"
                      }
                      disabled
                      className="w-full bg-gray-100"
                    />
                  ) : (
                    <Select
                      value={newClass.grade}
                      onValueChange={handleGradeChange}
                    >
                      <SelectTrigger
                        className={`w-full ${
                          validationErrors.grade
                            ? "border-red-500 focus-visible:ring-red-500"
                            : ""
                        }`}
                      >
                        <SelectValue
                          placeholder={t("setup.selectGradeLevel")}
                        />
                      </SelectTrigger>
                      <SelectContent>
                        {gradeOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  {validationErrors.grade && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.gradeLevelRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="className"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.className")}*
                  </label>
                  <Input
                    id="className"
                    name="className"
                    placeholder={t("setup.classNamePlaceholder")}
                    value={newClass.className}
                    onChange={handleInputChange}
                    className={
                      validationErrors.className
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {validationErrors.className && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.classNameRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="responsibleTeacher"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.responsibleTeacher")}
                  </label>
                  <Select
                    value={newClass.responsibleTeacher}
                    onValueChange={handleTeacherChange}
                  >
                    <SelectTrigger
                      className={`w-full ${
                        validationErrors.responsibleTeacher
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    >
                      <SelectValue placeholder={t("setup.selectTeacher")} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableTeachers.length > 0 ? (
                        availableTeachers.map((teacher) => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.name}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="" disabled>
                          {t("setup.noTeachersAvailable")}
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {validationErrors.responsibleTeacher && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.teacherRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="section"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.section")}*
                  </label>
                  <Input
                    id="section"
                    name="section"
                    placeholder={t("setup.sectionIdentifier")}
                    value={newClass.section}
                    onChange={handleInputChange}
                    className={
                      validationErrors.section
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {validationErrors.section && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.sectionRequired")}
                    </p>
                  )}
                  {validationErrors.duplicateGradeSection && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.duplicateGradeSection")}
                    </p>
                  )}
                </div>
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={handleCancelAdd}>
                  {t("setup.cancel")}
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t("setup.saveClass")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {setupData.classes.length > 0 ? (
        <div className="border rounded-md overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.grade")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.className")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.section")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.responsibleTeacher")}
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("setup.actions")}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {setupData.classes.map((cls) => (
                <tr key={cls.id}>
                  {editingClassId === cls.id ? (
                    // Edit mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Select
                          value={editingClass?.grade || ""}
                          onValueChange={(value) =>
                            handleEditSelectChange("grade", value)
                          }
                        >
                          <SelectTrigger
                            className={`w-full text-sm ${
                              validationErrors.editGrade
                                ? "border-red-500 focus-visible:ring-red-500"
                                : ""
                            }`}
                          >
                            <SelectValue
                              placeholder={t("setup.selectGradeLevel")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {gradeOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Input
                          name="className"
                          value={editingClass?.className || ""}
                          onChange={handleEditInputChange}
                          placeholder={t("setup.className")}
                          className={`text-sm ${
                            validationErrors.editClassName
                              ? "border-red-500 focus-visible:ring-red-500"
                              : ""
                          }`}
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <Input
                            name="section"
                            value={editingClass?.section || ""}
                            onChange={handleEditInputChange}
                            placeholder={t("setup.section")}
                            className={`text-sm ${
                              validationErrors.editSection
                                ? "border-red-500 focus-visible:ring-red-500"
                                : ""
                            }`}
                          />
                          {validationErrors.editDuplicateGradeSection && (
                            <p className="text-red-500 text-xs mt-1">
                              {t("validationSetup.duplicateGradeSection")}
                            </p>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Select
                          value={editingClass?.responsibleTeacher || ""}
                          onValueChange={(value) =>
                            handleEditSelectChange("responsibleTeacher", value)
                          }
                        >
                          <SelectTrigger
                            className={`w-full text-sm ${
                              validationErrors.editResponsibleTeacher
                                ? "border-red-500 focus-visible:ring-red-500"
                                : ""
                            }`}
                          >
                            <SelectValue
                              placeholder={t("setup.selectTeacher")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            {availableTeachers.length > 0 ? (
                              availableTeachers.map((teacher) => (
                                <SelectItem key={teacher.id} value={teacher.id}>
                                  {teacher.name}
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="" disabled>
                                {t("setup.noTeachersAvailable")}
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleSaveEdit}
                            className="text-green-600 hover:text-green-800 hover:bg-green-50"
                          >
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleCancelEdit}
                            className="text-gray-600 hover:text-gray-800 hover:bg-gray-50"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </>
                  ) : (
                    // Display mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {gradeOptions.find(
                          (option) => option.value === cls.grade
                        )?.label || cls.grade}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {cls.className}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {cls.section}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {cls.responsibleTeacher
                          ? getTeacherNameById(cls.responsibleTeacher)
                          : t("setup.noTeacherAssigned")}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditClass(cls)}
                            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteClass(cls.id)}
                            className="text-red-600 hover:text-red-800 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="border rounded-md p-8 text-center text-gray-500">
          {t("setup.noClassesAdded")}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteConfirmation.confirmDeleteClass")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteConfirmation.confirmDeleteClassMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteClass}>
              {t("deleteConfirmation.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteClass}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("deleteConfirmation.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
