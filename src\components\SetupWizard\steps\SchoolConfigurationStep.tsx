import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Settings, X, Plus, Calendar, Trash2, Edit, Save } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useSetupWizard, Period } from "../SetupWizardContext";
import toast from "react-hot-toast";

export default function SchoolConfigurationStep() {
  const { t } = useTranslation();
  const {
    setupData,
    updateConfiguration,
    validationErrors,
    setValidationError,
  } = useSetupWizard();

  // State for managing period creation
  const [isAddingPeriod, setIsAddingPeriod] = useState(false);
  const [editingPeriodId, setEditingPeriodId] = useState<string | null>(null);
  const [newPeriod, setNewPeriod] = useState<Omit<Period, "id">>({
    name: "",
    startDate: "",
    endDate: "",
  });
  const [editingPeriod, setEditingPeriod] = useState<{
    id: string;
    name: string;
    startDate: string;
    endDate: string;
  } | null>(null);

  // State for controlling the language select dropdown
  const [languageSelectValue, setLanguageSelectValue] = useState<string>("");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [periodToDelete, setPeriodToDelete] = useState<string | null>(null);

  // Available languages
  const languages = [
    { value: "Albanian", label: "Albanian" },
    { value: "English", label: "English" },
    { value: "German", label: "German" },
    { value: "Italian", label: "Italian" },
    { value: "French", label: "French" },
    { value: "Spanish", label: "Spanish" },
  ];

  // Period management functions
  const handleAddPeriod = () => {
    if (newPeriod.name.trim() && newPeriod.startDate && newPeriod.endDate) {
      const periodWithId: Period = {
        ...newPeriod,
        id: Date.now().toString(),
      };

      const currentPeriods = setupData.configuration.periods || [];
      const updatedPeriods = [...currentPeriods, periodWithId];
      updateConfiguration({ periods: updatedPeriods });

      // Reset form
      setNewPeriod({ name: "", startDate: "", endDate: "" });
      setIsAddingPeriod(false);

      // Clear validation error
      setValidationError("periods", false);

      toast.success(t("periodMessages.periodAdded"));
    }
  };

  const handleRemovePeriod = (periodId: string) => {
    setPeriodToDelete(periodId);
    setDeleteDialogOpen(true);
  };

  const confirmDeletePeriod = () => {
    if (periodToDelete) {
      const updatedPeriods = setupData.configuration.periods?.filter(
        (period: { id: string }) => period.id !== periodToDelete
      );
      updateConfiguration({ periods: updatedPeriods });
      toast.success(t("periodMessages.periodRemoved"));
      setDeleteDialogOpen(false);
      setPeriodToDelete(null);
    }
  };

  const handleEditPeriod = (period: {
    id: string;
    name: string;
    startDate: string;
    endDate: string;
  }) => {
    setEditingPeriodId(period.id);
    setEditingPeriod({ ...period });
  };

  const handleCancelEditPeriod = () => {
    setEditingPeriodId(null);
    setEditingPeriod(null);
  };

  const handleEditPeriodInputChange = (field: string, value: string) => {
    if (!editingPeriod) return;
    setEditingPeriod({
      ...editingPeriod,
      [field]: value,
    });
  };

  const handleSaveEditPeriod = () => {
    if (!editingPeriod) return;

    // Validate the edited period
    if (
      !editingPeriod.name.trim() ||
      !editingPeriod.startDate ||
      !editingPeriod.endDate
    ) {
      return;
    }

    const currentPeriods = setupData.configuration.periods || [];
    const updatedPeriods = currentPeriods.map((period) =>
      period.id === editingPeriod.id ? editingPeriod : period
    );

    updateConfiguration({ periods: updatedPeriods });

    setEditingPeriodId(null);
    setEditingPeriod(null);

    toast.success(t("periodMessages.periodUpdated"));
  };

  const handlePeriodInputChange = (
    field: keyof Omit<Period, "id">,
    value: string
  ) => {
    setNewPeriod((prev) => ({ ...prev, [field]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    updateConfiguration({
      [name]: value,
    });

    // Clear validation error when user selects a value
    if (value.trim() !== "") {
      setValidationError(name, false);
    }
  };

  const handleLanguageSelect = (value: string) => {
    // Get current languages array from comma-separated string
    const currentLanguages = setupData.configuration.language
      ? setupData.configuration.language
          .split(",")
          .filter((lang) => lang.trim() !== "")
      : [];

    // Add language if not already selected
    if (!currentLanguages.includes(value)) {
      const updatedLanguages = [...currentLanguages, value];
      updateConfiguration({
        language: updatedLanguages.join(","),
      });

      // Clear validation error
      if (updatedLanguages.length > 0) {
        setValidationError("language", false);
      }
    }

    // Reset the select dropdown to show placeholder
    setLanguageSelectValue("");
  };

  const handleRemoveLanguage = (languageToRemove: string) => {
    // Get current languages array from comma-separated string
    const currentLanguages = setupData.configuration.language
      ? setupData.configuration.language
          .split(",")
          .filter((lang) => lang.trim() !== "")
      : [];

    // Remove the language
    const updatedLanguages = currentLanguages.filter(
      (lang) => lang !== languageToRemove
    );

    updateConfiguration({
      language: updatedLanguages.length > 0 ? updatedLanguages.join(",") : "",
    });

    // Set validation error if no languages left
    if (updatedLanguages.length === 0) {
      setValidationError("language", true);
    }

    // Reset the select dropdown to show placeholder
    setLanguageSelectValue("");
  };

  // Get school cycle options based on school category
  const getSchoolCycleOptions = () => {
    const schoolCategory = setupData.basicInfo.schoolCategory;

    if (schoolCategory === "primary") {
      return [
        { value: "elementary-1-5", label: t("setup.elementaryGrades1To5") },
        { value: "elementary-1-9", label: t("setup.elementaryGrades1To9") },
        { value: "middle-6-9", label: t("setup.middleGrades6To9") },
      ];
    } else if (schoolCategory === "secondary") {
      return [{ value: "high-10-12", label: t("setup.highGrades10To12") }];
    }

    // Default: show all options if no category selected
    return [
      { value: "elementary-1-5", label: t("setup.elementaryGrades1To5") },
      { value: "elementary-1-9", label: t("setup.elementaryGrades1To9") },
      { value: "middle-6-9", label: t("setup.middleGrades6To9") },
      { value: "high-10-12", label: t("setup.highGrades10To12") },
    ];
  };

  const schoolCycleOptions = getSchoolCycleOptions();
  const isSchoolCycleDisabled = schoolCycleOptions.length === 1;

  // Auto-select school cycle if there's only one option
  React.useEffect(() => {
    if (isSchoolCycleDisabled && schoolCycleOptions.length === 1) {
      const singleOption = schoolCycleOptions[0];
      if (setupData.configuration.schoolCycle !== singleOption.value) {
        updateConfiguration({ schoolCycle: singleOption.value });
      }
    }
  }, [
    isSchoolCycleDisabled,
    schoolCycleOptions,
    setupData.configuration.schoolCycle,
    updateConfiguration,
  ]);

  // Convert language value to array for rendering
  const selectedLanguages = setupData.configuration.language
    ? setupData.configuration.language
        .split(",")
        .filter((lang) => lang.trim() !== "")
    : [];

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold flex items-center text-gray-800">
          <Settings className="h-5 w-5 text-purple-600 mr-2" />
          {t("setup.schoolConfiguration")}
        </h2>
      </div>

      {/* Periods Management Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <label className="text-sm font-medium block">
              {t("setup.academicPeriods")}*
            </label>
            <p className="text-xs text-gray-500 mt-1">
              {t("setup.defineAcademicPeriodsForSchoolYear")}
            </p>
          </div>
        </div>

        {/* Display existing periods */}
        {setupData.configuration.periods &&
          setupData.configuration.periods.length > 0 && (
            <div className="space-y-3 mb-4">
              {setupData.configuration.periods.map((period) => (
                <div
                  key={period.id}
                  className="p-3 border rounded-lg bg-gray-50"
                >
                  {editingPeriodId === period.id ? (
                    // Edit mode
                    <div className="space-y-3">
                      <div className="flex justify-end gap-1 mb-2">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={handleSaveEditPeriod}
                          className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                        >
                          <Save className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={handleCancelEditPeriod}
                          className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        <div>
                          <label className="text-xs font-medium mb-1 block">
                            {t("setup.periodName")}*
                          </label>
                          <Input
                            value={editingPeriod?.name || ""}
                            onChange={(e) =>
                              handleEditPeriodInputChange(
                                "name",
                                e.target.value
                              )
                            }
                            placeholder={t("setup.enterPeriodName")}
                            className="text-sm"
                          />
                        </div>
                        <div>
                          <label className="text-xs font-medium mb-1 block">
                            {t("setup.startDate")}*
                          </label>
                          <Input
                            type="date"
                            value={editingPeriod?.startDate || ""}
                            onChange={(e) =>
                              handleEditPeriodInputChange(
                                "startDate",
                                e.target.value
                              )
                            }
                            className="text-sm"
                          />
                        </div>
                        <div>
                          <label className="text-xs font-medium mb-1 block">
                            {t("setup.endDate")}*
                          </label>
                          <Input
                            type="date"
                            value={editingPeriod?.endDate || ""}
                            onChange={(e) =>
                              handleEditPeriodInputChange(
                                "endDate",
                                e.target.value
                              )
                            }
                            className="text-sm"
                          />
                        </div>
                      </div>
                    </div>
                  ) : (
                    // Display mode
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Calendar className="h-4 w-4 text-gray-500" />
                        <div>
                          <p className="font-medium text-sm">{period.name}</p>
                          <p className="text-xs text-gray-500">
                            {period.startDate} - {period.endDate}
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditPeriod(period)}
                          className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemovePeriod(period.id)}
                          className="text-red-500 hover:text-red-700 p-1 h-auto"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

        {/* Add period form */}
        {isAddingPeriod && (
          <div className="border rounded-lg p-4 bg-purple-50 space-y-4 mb-2">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-1 block">
                  {t("setup.periodName")}*
                </label>
                <Input
                  value={newPeriod.name}
                  onChange={(e) =>
                    handlePeriodInputChange("name", e.target.value)
                  }
                  placeholder={t("setup.enterPeriodName")}
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-1 block">
                  {t("setup.startDate")}*
                </label>
                <Input
                  type="date"
                  value={newPeriod.startDate}
                  onChange={(e) =>
                    handlePeriodInputChange("startDate", e.target.value)
                  }
                />
              </div>
              <div>
                <label className="text-sm font-medium mb-1 block">
                  {t("setup.endDate")}*
                </label>
                <Input
                  type="date"
                  value={newPeriod.endDate}
                  onChange={(e) =>
                    handlePeriodInputChange("endDate", e.target.value)
                  }
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                type="button"
                onClick={handleAddPeriod}
                className="bg-purple-600 hover:bg-purple-700"
                disabled={
                  !newPeriod.name.trim() ||
                  !newPeriod.startDate ||
                  !newPeriod.endDate
                }
              >
                {t("setup.savePeriod")}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddingPeriod(false);
                  setNewPeriod({ name: "", startDate: "", endDate: "" });
                }}
              >
                {t("setup.cancel")}
              </Button>
            </div>
          </div>
        )}

        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => setIsAddingPeriod(true)}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          {t("setup.addPeriod")}
        </Button>

        {validationErrors.periods && (
          <p className="text-red-500 text-xs mt-1">
            {t("validationSetup.periodsRequired")}
          </p>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="languages" className="text-sm font-medium mb-1 block">
            {t("setup.numberOfLanguages")}*
          </label>
          <Select
            value={languageSelectValue}
            onValueChange={(value) => handleLanguageSelect(value)}
          >
            <SelectTrigger
              className={`w-full ${
                validationErrors.language
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }`}
            >
              <SelectValue placeholder={t("setup.selectLanguages")} />
            </SelectTrigger>
            <SelectContent>
              {languages.map((language) => (
                <SelectItem
                  key={language.value}
                  value={language.value}
                  disabled={selectedLanguages.includes(language.value)}
                >
                  {language.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Display selected languages as badges */}
          {selectedLanguages.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {selectedLanguages.map((lang) => {
                const language = languages.find((l) => l.value === lang);
                return (
                  <Badge key={lang} variant="secondary" className="px-2 py-1">
                    {language?.label || lang}
                    <button
                      onClick={() => handleRemoveLanguage(lang)}
                      className="ml-1 text-gray-500 hover:text-gray-700"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                );
              })}
            </div>
          )}

          {validationErrors.language && (
            <p className="text-red-500 text-xs mt-1">
              {t("validationSetup.languageRequired")}
            </p>
          )}
          <p className="text-xs text-gray-500 mt-1">
            {t("setup.selectOneOrMoreLanguages")}
          </p>
        </div>

        <div>
          <label htmlFor="grading" className="text-sm font-medium mb-1 block">
            {t("setup.gradingSystem")}*
          </label>
          <Select
            defaultValue="1-5"
            value={setupData.configuration.timezone}
            onValueChange={(value) => handleSelectChange("timezone", value)}
          >
            <SelectTrigger
              className={`w-full ${
                validationErrors.timezone
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }`}
            >
              <SelectValue placeholder={t("setup.selectGradingSystem")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1-5">{t("setup.oneToFiveScale")}</SelectItem>
              <SelectItem value="1-10">{t("setup.oneToTenScale")}</SelectItem>
              <SelectItem value="A-F">{t("setup.aToFScale")}</SelectItem>
            </SelectContent>
          </Select>
          {validationErrors.timezone && (
            <p className="text-red-500 text-xs mt-1">
              {t("validationSetup.gradingSystemRequired")}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="country" className="text-sm font-medium mb-1 block">
            {t("setup.country")}*
          </label>
          <Select
            value={setupData.configuration.currency || ""}
            onValueChange={(value) => handleSelectChange("currency", value)}
          >
            <SelectTrigger
              className={`w-full ${
                validationErrors.currency
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }`}
            >
              <SelectValue placeholder={t("setup.selectCountry")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="albania">{t("countries.albania")}</SelectItem>
              <SelectItem value="kosovo">{t("countries.kosovo")}</SelectItem>
              <SelectItem value="northMacedonia">
                {t("countries.northMacedonia")}
              </SelectItem>
              <SelectItem value="montenegro">
                {t("countries.montenegro")}
              </SelectItem>
            </SelectContent>
          </Select>
          {validationErrors.currency && (
            <p className="text-red-500 text-xs mt-1">
              {t("validationSetup.countryRequired")}
            </p>
          )}
        </div>

        <div>
          <label htmlFor="cycle" className="text-sm font-medium mb-1 block">
            {t("setup.schoolCycle")}*
          </label>
          {isSchoolCycleDisabled ? (
            <Input
              value={schoolCycleOptions[0]?.label || ""}
              disabled
              className="w-full bg-gray-100"
            />
          ) : (
            <Select
              value={setupData.configuration.schoolCycle || ""}
              onValueChange={(value) =>
                handleSelectChange("schoolCycle", value)
              }
            >
              <SelectTrigger
                className={`w-full ${
                  validationErrors.schoolCycle
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }`}
              >
                <SelectValue placeholder={t("setup.selectSchoolCycle")} />
              </SelectTrigger>
              <SelectContent>
                {schoolCycleOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          {validationErrors.schoolCycle && (
            <p className="text-red-500 text-xs mt-1">
              {t("validationSetup.schoolCycleRequired")}
            </p>
          )}
          <p className="text-xs text-gray-500 mt-1">
            {t("setup.gradeRangesThatYourSchoolCovers")}
          </p>
        </div>
      </div>

      <p className="text-sm text-gray-500 mt-6">
        * {t("setup.requiredFields")}
      </p>

      {/* Delete Confirmation Dialog */}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteConfirmation.confirmDeletePeriod")}
            </AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogDescription>
            {t("deleteConfirmation.confirmDeletePeriodMessage")}
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeleteDialogOpen(false)}>
              {t("deleteConfirmation.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              className="bg-red-600 hover:bg-red-700 text-white"
              onClick={confirmDeletePeriod}
            >
              {t("deleteConfirmation.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
