import React, { useState, useEffect, useRef } from "react";
import { Bot } from "lucide-react";
import { motion } from "framer-motion";
import { Message } from "./types";
import { User } from "../../types";

interface ChatMessageProps {
  message: Message;
  user: User | null;
  isNewMessage?: boolean; // Add this prop to indicate if message is new
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  user,
  isNewMessage = false, // Default to false for old messages
}) => {
  const [displayedContent, setDisplayedContent] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const messageEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Always scroll when content changes, not just during typing
    if (messageEndRef.current) {
      messageEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [displayedContent]);

  useEffect(() => {
    if (message.role === "assistant" && isNewMessage) {
      // Only apply typing effect for new assistant messages
      setIsTyping(true);
      setDisplayedContent("");

      const fullContent = message.content;
      let currentIndex = 0;

      const typingInterval = setInterval(() => {
        if (currentIndex < fullContent.length) {
          setDisplayedContent((prev) => prev + fullContent[currentIndex]);
          currentIndex++;
        } else {
          clearInterval(typingInterval);
          setIsTyping(false);
        }
      }, 15);

      return () => clearInterval(typingInterval);
    } else {
      // For old messages or user messages, show content immediately
      setDisplayedContent(message.content);
    }
  }, [message, isNewMessage]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`flex ${
        message.role === "user" ? "justify-end" : "justify-start"
      } mb-6`}
    >
      <div className="flex max-w-3xl">
        {message.role === "assistant" && (
          <div className="flex-shrink-0 mr-3">
            <div className="w-9 h-9 rounded-full bg-purple-600 flex items-center justify-center shadow-md">
              <Bot size={18} className="text-white" />
            </div>
          </div>
        )}

        <div
          className={`p-4 rounded-lg ${
            message.role === "user"
              ? "bg-purple-100 text-gray-800"
              : "bg-white border border-gray-200 shadow-sm"
          }`}
        >
          <p className="text-gray-800 whitespace-pre-wrap">
            {displayedContent}
            {isTyping && (
              <span className="inline-block w-1 h-4 ml-1 bg-gray-500 animate-pulse"></span>
            )}
          </p>

          <div ref={messageEndRef} />
        </div>

        {message.role === "user" && (
          <div className="flex-shrink-0 ml-3">
            <div className="w-9 h-9 rounded-full bg-gray-200 flex items-center justify-center shadow-sm">
              <span className="text-sm font-medium">
                {user?.name?.charAt(0) || "U"}
              </span>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};
