import { NavLink } from "react-router-dom";
import {
  studentNavItems,
  teacherNavItems,
  parentNavItems,
  directorNavItems,
} from "./SideNav";
import { School } from "lucide-react";
import { useTypedAuthUser } from "../../hooks/useAuth";
import { useTranslation } from "react-i18next";
import { useState, useEffect, useRef } from "react";

export default function Sidebar({
  toggleSidebar,
  isSidebarOpen,
}: {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
}) {
  const authUser = useTypedAuthUser();
  const { t, i18n } = useTranslation();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const sidebarRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        isMobile &&
        isSidebarOpen &&
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        toggleSidebar();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobile, isSidebarOpen, toggleSidebar]);

  const normalizedRole = authUser?.role?.trim().toLowerCase();

  let navItems;
  switch (normalizedRole) {
    case "teacher":
      navItems = teacherNavItems;
      break;
    case "parent":
      navItems = parentNavItems;
      break;
    case "admin":
      navItems = directorNavItems;
      break;
    case "student":
    default:
      navItems = studentNavItems;
      break;
  }

  const mainItems = navItems.filter(
    (item) => item.label !== "sidebar.settings"
  );
  const settingsItem = navItems.find(
    (item) => item.label === "sidebar.settings"
  );

  const changeLanguage = (lng: any) => {
    i18n.changeLanguage(lng);
    localStorage.setItem("language", lng);
  };

  return (
    <div
      ref={sidebarRef}
      className={`
        md:flex flex-col bg-[#f6f1fe] text-black w-64
        fixed md:relative top-0 left-0 z-50 h-screen
        transition-transform duration-300 ease-in-out
        ${
          isSidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        }
        shadow-lg md:shadow-none flex flex-col select-none
      `}
    >
      <div className="p-4">
        <div className="pb-4 text-center">
          <div className="flex justify-center items-center pb-3">
            <School className="w-8 h-8 text-[#8b5cf6]" />
            <p className="text-[#6e59a5] font-semibold pl-2">E-ditar</p>
          </div>
          <span className="px-6 py-1 rounded-full bg-[#e5deff] text-[#6e59a5] capitalize">
            {t(`${authUser?.role}`)}
          </span>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto px-4 ">
        <nav className="space-y-2">
          {mainItems.map((item) => (
            <NavLink
              key={`${item.path}-${item.label}`}
              to={item.path}
              onClick={() => isMobile && toggleSidebar()}
              className={({ isActive }) =>
                `flex items-center gap-3 px-3 py-2 rounded-lg transition  ${
                  isActive ? "bg-[#e5deff]" : "hover:bg-[#e5deff]"
                }`
              }
            >
              {item.icon}
              <span>{t(item.label)}</span>
            </NavLink>
          ))}
        </nav>
      </div>

      {/* Footer section with language and settings */}
      <div className="mt-auto p-4">
        <div className="flex justify-between text-xs gap-4 mb-4">
          <button
            className={`text-center w-full border-r-2 p-2 border-gray-300 ${
              i18n.language === "sq"
                ? "text-purple-700 font-bold"
                : "text-gray-600"
            }`}
            onClick={() => changeLanguage("sq")}
          >
            SQ
          </button>

          <button
            className={`text-center w-full p-2 rounded-md ${
              i18n.language === "en"
                ? "text-purple-700 font-bold"
                : "text-gray-600"
            }`}
            onClick={() => changeLanguage("en")}
          >
            EN
          </button>
        </div>

        {settingsItem && (
          <div className="pt-4 border-t border-[#dcd2ff]">
            <NavLink
              key={`${settingsItem.path}-${settingsItem.label}`}
              to={settingsItem.path}
              onClick={() => isMobile && toggleSidebar()}
              className={({ isActive }) =>
                `flex items-center gap-3 px-3 py-2 rounded-lg transition ${
                  isActive ? "bg-[#e5deff]" : "hover:bg-[#e5deff]"
                }`
              }
            >
              {settingsItem.icon}
              <span>{t(settingsItem.label)}</span>
            </NavLink>
          </div>
        )}
      </div>
    </div>
  );
}
