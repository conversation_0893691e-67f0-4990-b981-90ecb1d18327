import { useTranslation } from "react-i18next";
import Error404Image from "../../assets/images/FrameError.png";
import { useNavigate } from "react-router-dom";
export default function NotFoundPage() {
  const navigate = useNavigate();
  const handleGoBack = () => {
    navigate("/dashboard");
  };

  const { t } = useTranslation();

  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="flex flex-col justify-center items-center">
        <img
          src={Error404Image}
          alt="error404-image"
          className="w-34 h-34 pb-10"
        />
        <h3 className="font-bold text-2xl text-center">{t("notFound")}</h3>
        <p className="text-gray-400 py-2">{t("notFoundMessage")}</p>
        <button
          className="text-blue-500 underline font-semibold cursor-pointer"
          onClick={handleGoBack}
        >
          {t("goBack")}
        </button>
      </div>
    </div>
  );
}
