import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Users, Plus, Trash2, Save, Edit, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import toast from "react-hot-toast";
import { Card, CardContent } from "@/components/ui/card";
import { useSetupWizard, Parent } from "../SetupWizardContext";
import {
  isValidPhoneCharacter,
  validatePhoneFormat,
  validateUniqueStudentId,
  validateUniqueParentId,
} from "../../../lib/validation";

interface Student {
  id: string;
  firstName: string;
  lastName: string;
  birthday: string;
  idCardNumber: string;
  grade: string;
  section: string;
  gender: string;
  parents: Parent[];
}

export default function StudentRegistrationStep() {
  const { t } = useTranslation();
  const {
    setupData,
    updateStudents,
    validationErrors,
    setValidationError,
    clearValidationErrors,
  } = useSetupWizard();
  const [isAddingStudent, setIsAddingStudent] = useState(false);
  const [editingStudentId, setEditingStudentId] = useState<string | null>(null);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);

  // Parent management states
  const [isAddingParent, setIsAddingParent] = useState(false);
  const [editingParentId, setEditingParentId] = useState<string | null>(null);
  const [newParent, setNewParent] = useState<Omit<Parent, "id">>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    idCardNumber: "",
    gender: "",
  });
  const [editingParent, setEditingParent] = useState<Parent | null>(null);
  const [parentPhoneValidationError, setParentPhoneValidationError] =
    useState("");

  // Parent editing states for existing students
  const [editingExistingParentId, setEditingExistingParentId] = useState<
    string | null
  >(null);
  const [editingExistingParent, setEditingExistingParent] =
    useState<Parent | null>(null);
  const [isAddingParentToExisting, setIsAddingParentToExisting] =
    useState(false);
  const [newParentForExisting, setNewParentForExisting] = useState<
    Omit<Parent, "id">
  >({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    idCardNumber: "",
    gender: "",
  });
  const [
    existingParentPhoneValidationError,
    setExistingParentPhoneValidationError,
  ] = useState("");
  const [
    newParentForExistingPhoneValidationError,
    setNewParentForExistingPhoneValidationError,
  ] = useState("");
  const [newStudent, setNewStudent] = useState<Student>({
    id: "",
    firstName: "",
    lastName: "",
    birthday: "",
    idCardNumber: "",
    grade: "",
    section: "",
    gender: "",
    parents: [],
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [studentToDelete, setStudentToDelete] = useState<string | null>(null);

  // Get grades and sections from the Classes step data
  const availableGrades = [
    ...new Set(setupData.classes.map((cls) => cls.grade)),
  ];
  const availableSections = [
    ...new Set(setupData.classes.map((cls) => cls.section)),
  ];

  const handleAddStudent = () => {
    setIsAddingStudent(true);
    clearValidationErrors();
    setParentPhoneValidationError("");
    setNewStudent({
      id: "",
      firstName: "",
      lastName: "",
      birthday: "",
      idCardNumber: "",
      grade: availableGrades[0] || "",
      section: availableSections[0] || "",
      gender: "",
      parents: [],
    });
    setIsAddingParent(false);
    setNewParent({
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      idCardNumber: "",
      gender: "",
    });
  };

  const handleCancelAdd = () => {
    setIsAddingStudent(false);
    clearValidationErrors();
    setParentPhoneValidationError("");
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewStudent({
      ...newStudent,
      [name]: value,
    });

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(name, false);
    }
  };

  const handleGradeChange = (value: string) => {
    setNewStudent({
      ...newStudent,
      grade: value,
    });
    setValidationError("grade", false);
  };

  const handleSectionChange = (value: string) => {
    setNewStudent({
      ...newStudent,
      section: value,
    });
    setValidationError("section", false);
  };

  const handleGenderChange = (value: string) => {
    setNewStudent({
      ...newStudent,
      gender: value,
    });
    setValidationError("gender", false);
  };

  // Parent management functions
  const handleAddParent = () => {
    if (
      newParent.firstName.trim() &&
      newParent.lastName.trim() &&
      newParent.email.trim() &&
      newParent.phone.trim() &&
      newParent.idCardNumber.trim() &&
      newParent.gender
    ) {
      // Validate phone format
      const phoneValidation = validatePhoneFormat(newParent.phone);
      if (!phoneValidation.isValid) {
        setParentPhoneValidationError(phoneValidation.error || "invalid");
        return;
      }

      // Validate unique parent ID
      const parentIdValidation = validateUniqueParentId(
        newParent.idCardNumber,
        setupData.students
      );
      if (!parentIdValidation.isValid) {
        return;
      }

      const parentWithId: Parent = {
        ...newParent,
        id: Date.now().toString(),
      };

      const updatedParents = [...newStudent.parents, parentWithId];
      setNewStudent({ ...newStudent, parents: updatedParents });

      // Reset form
      setNewParent({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        idCardNumber: "",
        gender: "",
      });
      setIsAddingParent(false);
      setParentPhoneValidationError("");

      // Clear validation error
      setValidationError("parents", false);
    }
  };

  const handleRemoveParent = (parentId: string) => {
    const updatedParents = newStudent.parents.filter(
      (parent) => parent.id !== parentId
    );
    setNewStudent({ ...newStudent, parents: updatedParents });

    // Set validation error if no parents left
    if (updatedParents.length === 0) {
      setValidationError("parents", true);
    }
  };

  const handleEditParent = (parent: Parent) => {
    setEditingParentId(parent.id);
    setEditingParent({ ...parent });
  };

  const handleCancelEditParent = () => {
    setEditingParentId(null);
    setEditingParent(null);
  };

  const handleEditParentInputChange = (field: string, value: string) => {
    if (!editingParent) return;
    setEditingParent({
      ...editingParent,
      [field]: value,
    });
  };

  const handleSaveEditParent = () => {
    if (!editingParent) return;

    // Validate the edited parent
    if (
      !editingParent.firstName.trim() ||
      !editingParent.lastName.trim() ||
      !editingParent.email.trim() ||
      !editingParent.phone.trim() ||
      !editingParent.idCardNumber.trim() ||
      !editingParent.gender
    ) {
      return;
    }

    // Validate unique parent ID
    const parentIdValidation = validateUniqueParentId(
      editingParent.idCardNumber,
      setupData.students,
      undefined, // No student exclusion for new student
      editingParent.id // Exclude current parent
    );
    if (!parentIdValidation.isValid) {
      return;
    }

    const updatedParents = newStudent.parents.map((parent) =>
      parent.id === editingParent.id ? editingParent : parent
    );

    setNewStudent({ ...newStudent, parents: updatedParents });

    setEditingParentId(null);
    setEditingParent(null);
  };

  const handleParentInputChange = (
    field: keyof Omit<Parent, "id">,
    value: string
  ) => {
    setNewParent((prev) => ({ ...prev, [field]: value }));
  };

  const handleParentPhoneInputChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { value } = e.target;
    const filteredValue = value
      .split("")
      .filter((char) => isValidPhoneCharacter(char))
      .join("");

    setNewParent((prev) => ({ ...prev, phone: filteredValue }));

    // Real-time validation
    if (filteredValue.trim() === "") {
      setParentPhoneValidationError("");
    } else {
      const validation = validatePhoneFormat(filteredValue);
      if (!validation.isValid) {
        setParentPhoneValidationError(validation.error || "invalid");
      } else {
        setParentPhoneValidationError("");
      }
    }
  };

  const handleParentPhoneKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (
      !isValidPhoneCharacter(e.key) &&
      ![
        "Backspace",
        "Delete",
        "Tab",
        "Escape",
        "Enter",
        "ArrowLeft",
        "ArrowRight",
        "ArrowUp",
        "ArrowDown",
      ].includes(e.key)
    ) {
      e.preventDefault();
    }
  };

  const getParentPhoneErrorMessage = (error: string) => {
    switch (error) {
      case "required":
        return t("validationSetup.parentPhoneRequired");
      case "tooShort":
        return t("validationSetup.parentPhoneTooShort");
      case "tooLong":
        return t("validationSetup.parentPhoneTooLong");
      case "invalidFormat":
        return t("validationSetup.parentPhoneInvalidFormat");
      default:
        return t("validationSetup.parentPhoneInvalid");
    }
  };

  const getParentIdErrorMessage = (error: string) => {
    switch (error) {
      case "required":
        return t("validationSetup.parentIdCardNumberRequired");
      case "duplicateParentId":
        return t("validationSetup.duplicateParentId");
      default:
        return t("validationSetup.parentIdCardNumberRequired");
    }
  };

  // Parent editing functions for existing students
  const handleEditExistingParent = (parent: Parent) => {
    setEditingExistingParentId(parent.id);
    setEditingExistingParent({ ...parent });
    setExistingParentPhoneValidationError("");
  };

  const handleCancelEditExistingParent = () => {
    setEditingExistingParentId(null);
    setEditingExistingParent(null);
    setExistingParentPhoneValidationError("");
  };

  const handleEditExistingParentInputChange = (
    field: string,
    value: string
  ) => {
    if (!editingExistingParent) return;

    // Handle phone input with character restriction
    if (field === "phone") {
      const filteredValue = value
        .split("")
        .filter((char) => isValidPhoneCharacter(char))
        .join("");
      setEditingExistingParent({
        ...editingExistingParent,
        [field]: filteredValue,
      });

      // Real-time validation
      if (filteredValue.trim() === "") {
        setExistingParentPhoneValidationError("");
      } else {
        const validation = validatePhoneFormat(filteredValue);
        if (!validation.isValid) {
          setExistingParentPhoneValidationError(validation.error || "invalid");
        } else {
          setExistingParentPhoneValidationError("");
        }
      }
    } else {
      setEditingExistingParent({
        ...editingExistingParent,
        [field]: value,
      });
    }
  };

  const handleSaveEditExistingParent = () => {
    if (!editingExistingParent || !editingStudent) return;

    // Validate the edited parent
    if (
      !editingExistingParent.firstName.trim() ||
      !editingExistingParent.lastName.trim() ||
      !editingExistingParent.email.trim() ||
      !editingExistingParent.phone.trim() ||
      !editingExistingParent.idCardNumber.trim() ||
      !editingExistingParent.gender
    ) {
      return;
    }

    // Validate phone format
    const phoneValidation = validatePhoneFormat(editingExistingParent.phone);
    if (!phoneValidation.isValid) {
      setExistingParentPhoneValidationError(phoneValidation.error || "invalid");
      return;
    }

    // Validate unique parent ID
    const parentIdValidation = validateUniqueParentId(
      editingExistingParent.idCardNumber,
      setupData.students,
      editingStudent.id, // Exclude current student
      editingExistingParent.id // Exclude current parent
    );
    if (!parentIdValidation.isValid) {
      return;
    }

    const updatedParents = editingStudent.parents.map((parent) =>
      parent.id === editingExistingParent.id ? editingExistingParent : parent
    );

    setEditingStudent({ ...editingStudent, parents: updatedParents });

    setEditingExistingParentId(null);
    setEditingExistingParent(null);
    setExistingParentPhoneValidationError("");
  };

  const handleRemoveExistingParent = (parentId: string) => {
    if (!editingStudent) return;

    const updatedParents = editingStudent.parents.filter(
      (parent) => parent.id !== parentId
    );
    setEditingStudent({ ...editingStudent, parents: updatedParents });

    // Set validation error if no parents left
    if (updatedParents.length === 0) {
      setValidationError("editParents", true);
    }
  };

  const handleAddParentToExisting = () => {
    if (!editingStudent) return;

    if (
      newParentForExisting.firstName.trim() &&
      newParentForExisting.lastName.trim() &&
      newParentForExisting.email.trim() &&
      newParentForExisting.phone.trim() &&
      newParentForExisting.idCardNumber.trim() &&
      newParentForExisting.gender
    ) {
      // Validate phone format
      const phoneValidation = validatePhoneFormat(newParentForExisting.phone);
      if (!phoneValidation.isValid) {
        setNewParentForExistingPhoneValidationError(
          phoneValidation.error || "invalid"
        );
        return;
      }

      // Validate unique parent ID
      const parentIdValidation = validateUniqueParentId(
        newParentForExisting.idCardNumber,
        setupData.students,
        editingStudent.id // Exclude current student
      );
      if (!parentIdValidation.isValid) {
        return;
      }

      const parentWithId: Parent = {
        ...newParentForExisting,
        id: Date.now().toString(),
      };

      const updatedParents = [...editingStudent.parents, parentWithId];
      setEditingStudent({ ...editingStudent, parents: updatedParents });

      // Reset form
      setNewParentForExisting({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        idCardNumber: "",
        gender: "",
      });
      setIsAddingParentToExisting(false);
      setNewParentForExistingPhoneValidationError("");

      // Clear validation error
      setValidationError("editParents", false);
    }
  };

  const handleNewParentForExistingInputChange = (
    field: keyof Omit<Parent, "id">,
    value: string
  ) => {
    // Handle phone input with character restriction
    if (field === "phone") {
      const filteredValue = value
        .split("")
        .filter((char) => isValidPhoneCharacter(char))
        .join("");
      setNewParentForExisting((prev) => ({ ...prev, [field]: filteredValue }));

      // Real-time validation
      if (filteredValue.trim() === "") {
        setNewParentForExistingPhoneValidationError("");
      } else {
        const validation = validatePhoneFormat(filteredValue);
        if (!validation.isValid) {
          setNewParentForExistingPhoneValidationError(
            validation.error || "invalid"
          );
        } else {
          setNewParentForExistingPhoneValidationError("");
        }
      }
    } else {
      setNewParentForExisting((prev) => ({ ...prev, [field]: value }));
    }
  };

  const handleSubmit = () => {
    clearValidationErrors();
    let isValid = true;

    // Validate student fields
    if (!newStudent.firstName.trim()) {
      setValidationError("firstName", true);
      isValid = false;
    }

    if (!newStudent.lastName.trim()) {
      setValidationError("lastName", true);
      isValid = false;
    }

    if (!newStudent.birthday.trim()) {
      setValidationError("birthday", true);
      isValid = false;
    }

    if (!newStudent.idCardNumber.trim()) {
      setValidationError("idCardNumber", true);
      isValid = false;
    } else {
      // Validate unique student ID
      const studentIdValidation = validateUniqueStudentId(
        newStudent.idCardNumber,
        setupData.students
      );
      if (!studentIdValidation.isValid) {
        setValidationError("idCardNumber", true);
        isValid = false;
      }
    }

    if (!newStudent.grade) {
      setValidationError("grade", true);
      isValid = false;
    }

    if (!newStudent.section) {
      setValidationError("section", true);
      isValid = false;
    }

    if (!newStudent.gender) {
      setValidationError("gender", true);
      isValid = false;
    }

    // Validate parents - at least one parent is required
    if (newStudent.parents.length === 0) {
      setValidationError("parents", true);
      isValid = false;
    } else {
      // Validate unique parent IDs
      for (let i = 0; i < newStudent.parents.length; i++) {
        const parent = newStudent.parents[i];
        const parentIdValidation = validateUniqueParentId(
          parent.idCardNumber,
          setupData.students
        );
        if (!parentIdValidation.isValid) {
          setValidationError("parents", true);
          isValid = false;
          break;
        }
      }
    }

    if (!isValid) return;

    // Add new student
    const studentToAdd: Student = {
      ...newStudent,
      id: Date.now().toString(),
    };

    updateStudents([...setupData.students, studentToAdd]);
    setIsAddingStudent(false);
    toast.success(t("studentMessages.studentAdded"));
  };

  const handleDeleteStudent = (id: string) => {
    setStudentToDelete(id);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteStudent = () => {
    if (studentToDelete) {
      updateStudents(
        setupData.students.filter((student) => student.id !== studentToDelete)
      );
      toast.success(t("studentMessages.studentRemoved"));
      setDeleteDialogOpen(false);
      setStudentToDelete(null);
    }
  };

  const cancelDeleteStudent = () => {
    setDeleteDialogOpen(false);
    setStudentToDelete(null);
  };

  const handleEditStudent = (student: Student) => {
    setEditingStudentId(student.id);
    setEditingStudent({ ...student });
    clearValidationErrors();
  };

  const handleCancelEdit = () => {
    setEditingStudentId(null);
    setEditingStudent(null);
    clearValidationErrors();
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingStudent) return;

    const { name, value } = e.target;
    setEditingStudent({
      ...editingStudent,
      [name]: value,
    });

    // Clear validation error when user types
    if (value.trim() !== "") {
      setValidationError(
        `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
        false
      );
    }
  };

  const handleEditSelectChange = (name: string, value: string) => {
    if (!editingStudent) return;

    setEditingStudent({
      ...editingStudent,
      [name]: value,
    });

    // Clear validation error when user selects
    setValidationError(
      `edit${name.charAt(0).toUpperCase() + name.slice(1)}`,
      false
    );
  };

  const handleSaveEdit = () => {
    if (!editingStudent) return;

    clearValidationErrors();
    let isValid = true;

    // Validate student fields
    if (!editingStudent.firstName.trim()) {
      setValidationError("editFirstName", true);
      isValid = false;
    }

    if (!editingStudent.lastName.trim()) {
      setValidationError("editLastName", true);
      isValid = false;
    }

    if (!editingStudent.birthday.trim()) {
      setValidationError("editBirthday", true);
      isValid = false;
    }

    if (!editingStudent.idCardNumber.trim()) {
      setValidationError("editIdCardNumber", true);
      isValid = false;
    } else {
      // Validate unique student ID (excluding current student)
      const studentIdValidation = validateUniqueStudentId(
        editingStudent.idCardNumber,
        setupData.students,
        editingStudent.id
      );
      if (!studentIdValidation.isValid) {
        setValidationError("editIdCardNumber", true);
        isValid = false;
      }
    }

    if (!editingStudent.grade) {
      setValidationError("editGrade", true);
      isValid = false;
    }

    if (!editingStudent.section) {
      setValidationError("editSection", true);
      isValid = false;
    }

    if (!editingStudent.gender) {
      setValidationError("editGender", true);
      isValid = false;
    }

    // Validate parents - at least one parent is required
    if (editingStudent.parents.length === 0) {
      setValidationError("editParents", true);
      isValid = false;
    } else {
      // Validate unique parent IDs (excluding current student's parents)
      for (let i = 0; i < editingStudent.parents.length; i++) {
        const parent = editingStudent.parents[i];
        const parentIdValidation = validateUniqueParentId(
          parent.idCardNumber,
          setupData.students,
          editingStudent.id
        );
        if (!parentIdValidation.isValid) {
          setValidationError("editParents", true);
          isValid = false;
          break;
        }
      }
    }

    if (!isValid) return;

    // Update student
    const updatedStudents = setupData.students.map((student) =>
      student.id === editingStudent.id ? editingStudent : student
    );

    updateStudents(updatedStudents);
    setEditingStudentId(null);
    setEditingStudent(null);
    toast.success(t("studentMessages.studentUpdated"));
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center text-gray-800">
          <Users className="h-5 w-5 text-purple-600 mr-2" />
          {t("setup.studentRegistration")}
        </h2>
        {!isAddingStudent && (
          <Button
            onClick={handleAddStudent}
            className="bg-purple-600 hover:bg-purple-700"
          >
            <Plus className="h-4 w-4 mr-2" /> {t("setup.addStudent")}
          </Button>
        )}
      </div>

      {isAddingStudent && (
        <Card className="mb-6 border-purple-200 shadow-sm">
          <CardContent className="pt-6">
            <h3 className="text-lg font-medium mb-4">
              {t("setup.addNewStudent")}
            </h3>
            <div className="grid gap-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="firstName"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.studentFirstName")}*
                  </label>
                  <Input
                    id="firstName"
                    name="firstName"
                    placeholder={t("setup.enterStudentFirstName")}
                    value={newStudent.firstName}
                    onChange={handleInputChange}
                    className={
                      validationErrors.firstName
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {validationErrors.firstName && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.studentFirstNameRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="lastName"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.studentLastName")}*
                  </label>
                  <Input
                    id="lastName"
                    name="lastName"
                    placeholder={t("setup.enterStudentLastName")}
                    value={newStudent.lastName}
                    onChange={handleInputChange}
                    className={
                      validationErrors.lastName
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {validationErrors.lastName && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.studentLastNameRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="birthday"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.studentBirthday")}*
                  </label>
                  <Input
                    id="birthday"
                    name="birthday"
                    type="date"
                    value={newStudent.birthday}
                    onChange={handleInputChange}
                    className={
                      validationErrors.birthday
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {validationErrors.birthday && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.studentBirthdayRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="idCardNumber"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.studentIdCardNumber")}*
                  </label>
                  <Input
                    id="idCardNumber"
                    name="idCardNumber"
                    placeholder={t("setup.enterStudentIdCardNumber")}
                    value={newStudent.idCardNumber}
                    onChange={handleInputChange}
                    className={
                      validationErrors.idCardNumber
                        ? "border-red-500 focus-visible:ring-red-500"
                        : ""
                    }
                  />
                  {validationErrors.idCardNumber && (
                    <p className="text-red-500 text-xs mt-1">
                      {setupData.students.some(
                        (student) =>
                          student.idCardNumber.trim().toLowerCase() ===
                          newStudent.idCardNumber.trim().toLowerCase()
                      )
                        ? t("validationSetup.duplicateStudentId")
                        : t("validationSetup.idCardNumberRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="gender"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.gender")}*
                  </label>
                  <Select
                    value={newStudent.gender}
                    onValueChange={handleGenderChange}
                  >
                    <SelectTrigger
                      className={`w-full ${
                        validationErrors.gender
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    >
                      <SelectValue placeholder={t("setup.selectGender")} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">{t("setup.male")}</SelectItem>
                      <SelectItem value="female">
                        {t("setup.female")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {validationErrors.gender && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.genderRequired")}
                    </p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="grade"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.grade")}*
                  </label>
                  <Select
                    value={newStudent.grade}
                    onValueChange={handleGradeChange}
                  >
                    <SelectTrigger
                      className={`w-full ${
                        validationErrors.grade
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    >
                      <SelectValue placeholder={t("setup.selectGrade")} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableGrades.map((grade) => (
                        <SelectItem key={grade} value={grade}>
                          {grade}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.grade && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.gradeRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="section"
                    className="text-sm font-medium mb-1 block"
                  >
                    {t("setup.section")}*
                  </label>
                  <Select
                    value={newStudent.section}
                    onValueChange={handleSectionChange}
                  >
                    <SelectTrigger
                      className={`w-full ${
                        validationErrors.section
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    >
                      <SelectValue placeholder={t("setup.selectSection")} />
                    </SelectTrigger>
                    <SelectContent>
                      {availableSections.map((section) => (
                        <SelectItem key={section} value={section}>
                          {section}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.section && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("validationSetup.sectionRequired")}
                    </p>
                  )}
                </div>
              </div>
              {/* Parents Management Section */}
              <div className="border-t pt-4 mt-2">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="font-medium">
                      {t("setup.parentInformation")}*
                    </h3>
                    <p className="text-xs text-gray-500 mt-1">
                      {t("setup.parentInfo")}
                    </p>
                  </div>
                </div>

                {/* Display existing parents */}
                {newStudent.parents && newStudent.parents.length > 0 && (
                  <div className="space-y-3 mb-4">
                    {newStudent.parents.map((parent) => (
                      <div
                        key={parent.id}
                        className="p-3 border rounded-lg bg-gray-50"
                      >
                        {editingParentId === parent.id ? (
                          // Edit mode
                          <div className="space-y-3">
                            <div className="flex justify-end gap-1 mb-2">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={handleSaveEditParent}
                                className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                              >
                                <Save className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={handleCancelEditParent}
                                className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              <div>
                                <label className="text-xs font-medium mb-1 block">
                                  {t("setup.parentFirstName")}*
                                </label>
                                <Input
                                  value={editingParent?.firstName || ""}
                                  onChange={(e) =>
                                    handleEditParentInputChange(
                                      "firstName",
                                      e.target.value
                                    )
                                  }
                                  placeholder={t("setup.enterParentFirstName")}
                                  className="text-sm"
                                />
                              </div>
                              <div>
                                <label className="text-xs font-medium mb-1 block">
                                  {t("setup.parentLastName")}*
                                </label>
                                <Input
                                  value={editingParent?.lastName || ""}
                                  onChange={(e) =>
                                    handleEditParentInputChange(
                                      "lastName",
                                      e.target.value
                                    )
                                  }
                                  placeholder={t("setup.enterParentLastName")}
                                  className="text-sm"
                                />
                              </div>
                            </div>
                          </div>
                        ) : (
                          // Display mode
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Users className="h-4 w-4 text-gray-500" />
                              <div>
                                <p className="font-medium text-sm">
                                  {parent.firstName} {parent.lastName}
                                </p>
                                <p className="text-xs text-gray-500">
                                  {parent.phone}
                                </p>
                              </div>
                            </div>
                            <div className="flex gap-1">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleEditParent(parent)}
                                className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveParent(parent.id)}
                                className="text-red-500 hover:text-red-700 p-1 h-auto"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Add parent form */}
                {isAddingParent && (
                  <div className="border rounded-lg p-4 bg-purple-50 space-y-4 mb-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          {t("setup.parentFirstName")}*
                        </label>
                        <Input
                          value={newParent.firstName}
                          onChange={(e) =>
                            handleParentInputChange("firstName", e.target.value)
                          }
                          placeholder={t("setup.enterParentFirstName")}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          {t("setup.parentLastName")}*
                        </label>
                        <Input
                          value={newParent.lastName}
                          onChange={(e) =>
                            handleParentInputChange("lastName", e.target.value)
                          }
                          placeholder={t("setup.enterParentLastName")}
                        />
                      </div>

                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          {t("setup.gender")}*
                        </label>
                        <Select
                          value={newParent.gender}
                          onValueChange={(value) =>
                            handleParentInputChange("gender", value)
                          }
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue
                              placeholder={t("setup.selectGender")}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="male">
                              {t("setup.male")}
                            </SelectItem>
                            <SelectItem value="female">
                              {t("setup.female")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          {t("setup.parentEmail")}*
                        </label>
                        <Input
                          type="email"
                          value={newParent.email}
                          onChange={(e) =>
                            handleParentInputChange("email", e.target.value)
                          }
                          placeholder={t("setup.enterParentEmail")}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          {t("setup.parentPhone")}*
                        </label>
                        <Input
                          type="tel"
                          value={newParent.phone}
                          onChange={handleParentPhoneInputChange}
                          onKeyDown={handleParentPhoneKeyDown}
                          placeholder="+383 49123123"
                        />
                        {parentPhoneValidationError && (
                          <p className="text-red-500 text-xs mt-1">
                            {getParentPhoneErrorMessage(
                              parentPhoneValidationError
                            )}
                          </p>
                        )}
                        {!newParent.phone && (
                          <p className="text-gray-500 text-xs mt-1">
                            {t("validationSetup.parentPhoneFormats")}
                          </p>
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-1 block">
                          {t("setup.parentIdCardNumber")}*
                        </label>
                        <Input
                          value={newParent.idCardNumber}
                          onChange={(e) =>
                            handleParentInputChange(
                              "idCardNumber",
                              e.target.value
                            )
                          }
                          placeholder={t("setup.enterParentIdCardNumber")}
                        />
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        onClick={handleAddParent}
                        className="bg-purple-600 hover:bg-purple-700"
                        disabled={
                          !newParent.firstName.trim() ||
                          !newParent.lastName.trim() ||
                          !newParent.email.trim() ||
                          !newParent.phone.trim() ||
                          !newParent.idCardNumber.trim() ||
                          !newParent.gender ||
                          !!parentPhoneValidationError
                        }
                      >
                        {t("setup.saveParent")}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          setIsAddingParent(false);
                          setNewParent({
                            firstName: "",
                            lastName: "",
                            email: "",
                            phone: "",
                            idCardNumber: "",
                            gender: "",
                          });
                          setParentPhoneValidationError("");
                        }}
                      >
                        {t("setup.cancel")}
                      </Button>
                    </div>
                  </div>
                )}

                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsAddingParent(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  {t("setup.addParent")}
                </Button>

                {validationErrors.parents && (
                  <p className="text-red-500 text-xs mt-1">
                    {newStudent.parents.length === 0
                      ? t("validationSetup.parentsRequired")
                      : t("validationSetup.duplicateParentId")}
                  </p>
                )}
              </div>
              <div className="flex justify-end gap-2 mt-4">
                <Button variant="outline" onClick={handleCancelAdd}>
                  {t("setup.cancel")}
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t("setup.saveStudent")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {setupData.students.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {setupData.students.map((student) => (
            <div key={student.id} className="border rounded-md p-4 relative">
              {editingStudentId === student.id ? (
                // Edit mode
                <div className="space-y-3">
                  <div className="flex justify-end gap-1 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleSaveEdit}
                      className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                    >
                      <Save className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCancelEdit}
                      className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-2">
                    <div className="flex gap-2">
                      <Input
                        name="firstName"
                        value={editingStudent?.firstName || ""}
                        onChange={handleEditInputChange}
                        placeholder={t("setup.firstName")}
                        className={`text-sm ${
                          validationErrors.editFirstName
                            ? "border-red-500 focus-visible:ring-red-500"
                            : ""
                        }`}
                      />
                      <Input
                        name="lastName"
                        value={editingStudent?.lastName || ""}
                        onChange={handleEditInputChange}
                        placeholder={t("setup.lastName")}
                        className={`text-sm ${
                          validationErrors.editLastName
                            ? "border-red-500 focus-visible:ring-red-500"
                            : ""
                        }`}
                      />
                    </div>

                    <Input
                      name="birthday"
                      type="date"
                      value={editingStudent?.birthday || ""}
                      onChange={handleEditInputChange}
                      className={`text-sm ${
                        validationErrors.editBirthday
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    />

                    <Input
                      name="idCardNumber"
                      value={editingStudent?.idCardNumber || ""}
                      onChange={handleEditInputChange}
                      placeholder={t("setup.idCardNumber")}
                      className={`text-sm ${
                        validationErrors.editIdCardNumber
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    />
                    {validationErrors.editIdCardNumber && (
                      <p className="text-red-500 text-xs mt-1">
                        {editingStudent &&
                        setupData.students.some(
                          (student) =>
                            student.id !== editingStudent.id &&
                            student.idCardNumber.trim().toLowerCase() ===
                              editingStudent.idCardNumber.trim().toLowerCase()
                        )
                          ? t("validationSetup.duplicateStudentId")
                          : t("validationSetup.idCardNumberRequired")}
                      </p>
                    )}

                    <div className="flex gap-2">
                      <Select
                        value={editingStudent?.grade || ""}
                        onValueChange={(value) =>
                          handleEditSelectChange("grade", value)
                        }
                      >
                        <SelectTrigger className="text-sm w-full">
                          <SelectValue placeholder={t("setup.grade")} />
                        </SelectTrigger>
                        <SelectContent>
                          {availableGrades.map((grade) => (
                            <SelectItem key={grade} value={grade}>
                              {grade}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={editingStudent?.section || ""}
                        onValueChange={(value) =>
                          handleEditSelectChange("section", value)
                        }
                      >
                        <SelectTrigger className="text-sm w-full">
                          <SelectValue placeholder={t("setup.section")} />
                        </SelectTrigger>
                        <SelectContent>
                          {availableSections.map((section) => (
                            <SelectItem key={section} value={section}>
                              {section}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Select
                      value={editingStudent?.gender || ""}
                      onValueChange={(value) =>
                        handleEditSelectChange("gender", value)
                      }
                    >
                      <SelectTrigger className="text-sm w-full">
                        <SelectValue placeholder={t("setup.gender")} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">{t("setup.male")}</SelectItem>
                        <SelectItem value="female">
                          {t("setup.female")}
                        </SelectItem>
                      </SelectContent>
                    </Select>

                    {/* Parent Information Section */}
                    <div className="border-t pt-3 mt-3">
                      <p className="text-xs font-medium text-gray-600 mb-2">
                        {t("setup.parentInformation")}:
                      </p>

                      {/* Display existing parents */}
                      {editingStudent &&
                        editingStudent.parents &&
                        editingStudent.parents.length > 0 && (
                          <div className="space-y-3 mb-4">
                            {editingStudent.parents.map((parent) => (
                              <div
                                key={parent.id}
                                className="p-3 border rounded-lg bg-gray-50"
                              >
                                {editingExistingParentId === parent.id ? (
                                  // Edit mode for existing parent
                                  <div className="space-y-3">
                                    <div className="flex justify-end gap-1 mb-2">
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={handleSaveEditExistingParent}
                                        className="text-green-600 hover:text-green-800 hover:bg-green-50 p-1 h-auto"
                                      >
                                        <Save className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={handleCancelEditExistingParent}
                                        className="text-gray-600 hover:text-gray-800 hover:bg-gray-50 p-1 h-auto"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Input
                                          value={
                                            editingExistingParent?.firstName ||
                                            ""
                                          }
                                          onChange={(e) =>
                                            handleEditExistingParentInputChange(
                                              "firstName",
                                              e.target.value
                                            )
                                          }
                                          placeholder={t(
                                            "setup.enterParentFirstName"
                                          )}
                                          className="text-sm"
                                        />
                                      </div>
                                      <div>
                                        <Input
                                          value={
                                            editingExistingParent?.lastName ||
                                            ""
                                          }
                                          onChange={(e) =>
                                            handleEditExistingParentInputChange(
                                              "lastName",
                                              e.target.value
                                            )
                                          }
                                          placeholder={t(
                                            "setup.enterParentLastName"
                                          )}
                                          className="text-sm"
                                        />
                                      </div>
                                    </div>
                                    <div>
                                      <Input
                                        value={
                                          editingExistingParent?.email || ""
                                        }
                                        onChange={(e) =>
                                          handleEditExistingParentInputChange(
                                            "email",
                                            e.target.value
                                          )
                                        }
                                        placeholder={t(
                                          "setup.enterParentEmail"
                                        )}
                                        className="text-sm"
                                      />
                                    </div>
                                    <div>
                                      <Input
                                        value={
                                          editingExistingParent?.phone || ""
                                        }
                                        onChange={(e) =>
                                          handleEditExistingParentInputChange(
                                            "phone",
                                            e.target.value
                                          )
                                        }
                                        placeholder={t(
                                          "setup.enterParentPhone"
                                        )}
                                        className="text-sm"
                                      />
                                      {existingParentPhoneValidationError && (
                                        <p className="text-red-500 text-xs mt-1">
                                          {getParentPhoneErrorMessage(
                                            existingParentPhoneValidationError
                                          )}
                                        </p>
                                      )}
                                    </div>
                                    <div className="grid grid-cols-2 gap-2">
                                      <div>
                                        <Input
                                          value={
                                            editingExistingParent?.idCardNumber ||
                                            ""
                                          }
                                          onChange={(e) =>
                                            handleEditExistingParentInputChange(
                                              "idCardNumber",
                                              e.target.value
                                            )
                                          }
                                          placeholder={t(
                                            "setup.enterParentIdCardNumber"
                                          )}
                                          className="text-sm"
                                        />
                                      </div>
                                      <div>
                                        <Select
                                          value={
                                            editingExistingParent?.gender || ""
                                          }
                                          onValueChange={(value) =>
                                            handleEditExistingParentInputChange(
                                              "gender",
                                              value
                                            )
                                          }
                                        >
                                          <SelectTrigger className="text-sm">
                                            <SelectValue
                                              placeholder={t(
                                                "setup.selectGender"
                                              )}
                                            />
                                          </SelectTrigger>
                                          <SelectContent>
                                            <SelectItem value="male">
                                              {t("setup.male")}
                                            </SelectItem>
                                            <SelectItem value="female">
                                              {t("setup.female")}
                                            </SelectItem>
                                          </SelectContent>
                                        </Select>
                                      </div>
                                    </div>
                                  </div>
                                ) : (
                                  // Display mode for existing parent
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                      <Users className="h-4 w-4 text-gray-500" />
                                      <div>
                                        <p className="font-medium text-sm">
                                          {parent.firstName} {parent.lastName}
                                        </p>
                                        <p className="text-xs text-gray-500">
                                          {parent.phone}
                                        </p>
                                      </div>
                                    </div>
                                    <div className="flex gap-1">
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          handleEditExistingParent(parent)
                                        }
                                        className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                                      >
                                        <Edit className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() =>
                                          handleRemoveExistingParent(parent.id)
                                        }
                                        className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        )}

                      {/* Add new parent form for existing student */}
                      {isAddingParentToExisting && (
                        <div className="p-3 border rounded-lg bg-blue-50 mb-4">
                          <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <label className="text-sm font-medium mb-1 block">
                                  {t("setup.parentFirstName")}*
                                </label>
                                <Input
                                  value={newParentForExisting.firstName}
                                  onChange={(e) =>
                                    handleNewParentForExistingInputChange(
                                      "firstName",
                                      e.target.value
                                    )
                                  }
                                  placeholder={t("setup.enterParentFirstName")}
                                />
                              </div>
                              <div>
                                <label className="text-sm font-medium mb-1 block">
                                  {t("setup.parentLastName")}*
                                </label>
                                <Input
                                  value={newParentForExisting.lastName}
                                  onChange={(e) =>
                                    handleNewParentForExistingInputChange(
                                      "lastName",
                                      e.target.value
                                    )
                                  }
                                  placeholder={t("setup.enterParentLastName")}
                                />
                              </div>
                            </div>
                            <div>
                              <label className="text-sm font-medium mb-1 block">
                                {t("setup.parentEmail")}*
                              </label>
                              <Input
                                value={newParentForExisting.email}
                                onChange={(e) =>
                                  handleNewParentForExistingInputChange(
                                    "email",
                                    e.target.value
                                  )
                                }
                                placeholder={t("setup.enterParentEmail")}
                              />
                            </div>
                            <div>
                              <label className="text-sm font-medium mb-1 block">
                                {t("setup.parentPhone")}*
                              </label>
                              <Input
                                value={newParentForExisting.phone}
                                onChange={(e) =>
                                  handleNewParentForExistingInputChange(
                                    "phone",
                                    e.target.value
                                  )
                                }
                                placeholder={t("setup.enterParentPhone")}
                              />
                              {newParentForExistingPhoneValidationError && (
                                <p className="text-red-500 text-xs mt-1">
                                  {getParentPhoneErrorMessage(
                                    newParentForExistingPhoneValidationError
                                  )}
                                </p>
                              )}
                            </div>
                            <div className="grid grid-cols-2 gap-2">
                              <div>
                                <label className="text-sm font-medium mb-1 block">
                                  {t("setup.parentGender")}*
                                </label>
                                <Select
                                  value={newParentForExisting.gender}
                                  onValueChange={(value) =>
                                    handleNewParentForExistingInputChange(
                                      "gender",
                                      value
                                    )
                                  }
                                >
                                  <SelectTrigger>
                                    <SelectValue
                                      placeholder={t("setup.selectGender")}
                                    />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="male">
                                      {t("setup.male")}
                                    </SelectItem>
                                    <SelectItem value="female">
                                      {t("setup.female")}
                                    </SelectItem>
                                  </SelectContent>
                                </Select>
                              </div>
                              <div>
                                <label className="text-sm font-medium mb-1 block">
                                  {t("setup.parentIdCardNumber")}*
                                </label>
                                <Input
                                  value={newParentForExisting.idCardNumber}
                                  onChange={(e) =>
                                    handleNewParentForExistingInputChange(
                                      "idCardNumber",
                                      e.target.value
                                    )
                                  }
                                  placeholder={t(
                                    "setup.enterParentIdCardNumber"
                                  )}
                                />
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                type="button"
                                onClick={handleAddParentToExisting}
                                className="bg-purple-600 hover:bg-purple-700"
                                disabled={
                                  !newParentForExisting.firstName.trim() ||
                                  !newParentForExisting.lastName.trim() ||
                                  !newParentForExisting.email.trim() ||
                                  !newParentForExisting.phone.trim() ||
                                  !newParentForExisting.idCardNumber.trim() ||
                                  !newParentForExisting.gender ||
                                  !!newParentForExistingPhoneValidationError
                                }
                              >
                                {t("setup.saveParent")}
                              </Button>
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                  setIsAddingParentToExisting(false);
                                  setNewParentForExisting({
                                    firstName: "",
                                    lastName: "",
                                    email: "",
                                    phone: "",
                                    idCardNumber: "",
                                    gender: "",
                                  });
                                  setNewParentForExistingPhoneValidationError(
                                    ""
                                  );
                                }}
                              >
                                {t("setup.cancel")}
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setIsAddingParentToExisting(true)}
                        className="flex items-center gap-2"
                      >
                        <Plus className="h-4 w-4" />
                        {t("setup.addParent")}
                      </Button>

                      {validationErrors.editParents && (
                        <p className="text-red-500 text-xs mt-1">
                          {editingStudent && editingStudent.parents.length === 0
                            ? t("validationSetup.parentsRequired")
                            : t("validationSetup.duplicateParentId")}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                // Display mode
                <>
                  <div className="flex justify-end gap-1 mb-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditStudent(student)}
                      className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 p-1 h-auto"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteStudent(student.id)}
                      className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>

                  <h3 className="font-medium text-gray-900">
                    {student.firstName} {student.lastName}
                  </h3>
                  {student.birthday && (
                    <p className="text-sm text-gray-500">
                      {t("setup.birthday")}: {student.birthday}
                    </p>
                  )}
                  {student.idCardNumber && (
                    <p className="text-sm text-gray-500">
                      ID: {student.idCardNumber}
                    </p>
                  )}
                  <p className="text-sm text-gray-500">
                    {t("setup.grade")}: {student.grade} {student.section}
                  </p>
                  {student.parents && student.parents.length > 0 && (
                    <div className="mt-2">
                      <p className="text-xs font-medium text-gray-600 mb-1">
                        {t("setup.parentInformation")}:
                      </p>
                      {student.parents.map((parent, index) => (
                        <div key={parent.id} className="text-sm text-gray-500">
                          <p>
                            {parent.firstName} {parent.lastName}
                          </p>
                          <p className="text-xs">{parent.phone}</p>
                          {index < student.parents.length - 1 && (
                            <div className="mb-1" />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>
      ) : (
        <div className="border rounded-md p-8 text-center text-gray-500">
          {t("setup.noStudentsRegistered")}
        </div>
      )}

      <p className="text-sm text-gray-500 mt-6">
        * {t("setup.requiredFields")}
      </p>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("deleteConfirmation.confirmDeleteStudent")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("deleteConfirmation.confirmDeleteStudentMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDeleteStudent}>
              {t("deleteConfirmation.cancel")}
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteStudent}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("deleteConfirmation.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
