import { useState, useRef, useEffect } from 'react';
import { Message, ChatHistory } from '../pages/AssistantPage/types';

export function useChat() {
  const [chatHistories, setChatHistories] = useState<ChatHistory[]>([]);
  const [activeChat, setActiveChat] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [chatMessages, setChatMessages] = useState<Record<string, Message[]>>({});
  const [isTyping, setIsTyping] = useState(false);
  const timeoutIDRef = useRef<NodeJS.Timeout | null>(null);

  // Load from localStorage
  useEffect(() => {
    const savedHistories = localStorage.getItem('chatHistories');
    const savedMessages = localStorage.getItem('chatMessages');
    
    if (savedHistories) {
      try {
        const parsed = JSON.parse(savedHistories);
        const historiesWithDates = parsed.map((history: any) => ({
          ...history,
          date: new Date(history.date)
        }));
        setChatHistories(historiesWithDates);
      } catch (e) {
        console.error('Failed to parse chat histories:', e);
      }
    }
    
    if (savedMessages) {
      try {
        setChatMessages(JSON.parse(savedMessages));
      } catch (e) {
        console.error('Failed to parse chat messages:', e);
      }
    }
  }, []);

  // Save to localStorage
  useEffect(() => {
    localStorage.setItem('chatHistories', JSON.stringify(chatHistories));
  }, [chatHistories]);

  useEffect(() => {
    localStorage.setItem('chatMessages', JSON.stringify(chatMessages));
  }, [chatMessages]);

  // Chat functions
  const handleNewChat = () => {
    setActiveChat(null);
    setMessages([]);
  };

  const handleChatSelect = (chatId: string) => {
    setActiveChat(chatId);
    setMessages(chatMessages[chatId] || []);
  };

  const handleDeleteChat = (chatId: string) => {
    setChatHistories(chatHistories.filter(chat => chat.id !== chatId));
    if (activeChat === chatId) {
      setActiveChat(null);
      setMessages([]);
    }
  };

  const handleDeleteAllConversations = () => {
    setChatHistories([]);
    setActiveChat(null);
    setMessages([]);
  };

  return {
    chatHistories,
    setChatHistories,
    activeChat,
    setActiveChat,
    messages,
    setMessages,
    chatMessages,
    setChatMessages,
    isTyping,
    setIsTyping,
    timeoutIDRef,
    handleNewChat,
    handleChatSelect,
    handleDeleteChat,
    handleDeleteAllConversations
  };
}