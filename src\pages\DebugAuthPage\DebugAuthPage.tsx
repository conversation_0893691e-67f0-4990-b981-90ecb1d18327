import { useState, useEffect } from "react";
import {
  useTypedAuthUser,
  useIsAuthenticated,
  useSignOut,
  useSignIn,
} from "../../hooks/useAuth";
import {
  studentNavItems,
  teacherNavItems,
  parentNavItems,
  directorNavItems,
} from "../../components/Sidebar/SideNav";
import { Button } from "../../components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Badge } from "../../components/ui/badge";
import {
  Copy,
  RefreshCw,
  LogOut,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  Play,
  Monitor,
} from "lucide-react";
import { toast } from "react-hot-toast";

interface TokenInfo {
  token: string;
  decoded?: any;
  isExpired: boolean;
  expiresAt?: Date;
  timeUntilExpiry?: string;
}

interface RefreshTestResult {
  success: boolean;
  message: string;
  timestamp: Date;
  newTokenReceived?: boolean;
  error?: string;
  responseTime?: number;
}

interface NetworkLog {
  url: string;
  method: string;
  status: number;
  timestamp: Date;
  duration: number;
  success: boolean;
  error?: string;
}

export default function DebugAuthPage() {
  const authUser = useTypedAuthUser();
  const isAuthenticated = useIsAuthenticated();
  const signOut = useSignOut();
  const [showTokens, setShowTokens] = useState(false);
  const [tokenInfo, setTokenInfo] = useState<{
    auth?: TokenInfo;
    refresh?: TokenInfo;
  }>({});
  const [refreshTests, setRefreshTests] = useState<RefreshTestResult[]>([]);
  const [isTestingRefresh, setIsTestingRefresh] = useState(false);
  const [autoRefreshMonitor, setAutoRefreshMonitor] = useState<boolean>(false);
  const [networkLogs, setNetworkLogs] = useState<NetworkLog[]>([]);
  const signIn = useSignIn();

  // Utility functions
  const decodeJWT = (token: string) => {
    try {
      const base64Url = token.split(".")[1];
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/");
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map(function (c) {
            return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join("")
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error("Error decoding JWT:", error);
      return null;
    }
  };

  const isTokenExpired = (decoded: any) => {
    if (!decoded || !decoded.exp) return true;
    return Date.now() >= decoded.exp * 1000;
  };

  const getTimeUntilExpiry = (decoded: any) => {
    if (!decoded || !decoded.exp) return "Unknown";
    const expiryTime = decoded.exp * 1000;
    const now = Date.now();
    const diff = expiryTime - now;

    if (diff <= 0) return "Expired";

    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const getCookieValue = (name: string) => {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(";").shift();
    return null;
  };

  const analyzeTokens = () => {
    const authCookie = getCookieValue("_auth");
    const refreshCookie = getCookieValue("_auth_refresh");

    const info: { auth?: TokenInfo; refresh?: TokenInfo } = {};

    if (authCookie) {
      const decoded = decodeJWT(authCookie);
      info.auth = {
        token: authCookie,
        decoded,
        isExpired: isTokenExpired(decoded),
        expiresAt: decoded?.exp ? new Date(decoded.exp * 1000) : undefined,
        timeUntilExpiry: getTimeUntilExpiry(decoded),
      };
    }

    if (refreshCookie) {
      const decoded = decodeJWT(refreshCookie);
      info.refresh = {
        token: refreshCookie,
        decoded,
        isExpired: isTokenExpired(decoded),
        expiresAt: decoded?.exp ? new Date(decoded.exp * 1000) : undefined,
        timeUntilExpiry: getTimeUntilExpiry(decoded),
      };
    }

    setTokenInfo(info);
  };

  const testRefreshToken = async () => {
    setIsTestingRefresh(true);
    const refreshToken = getCookieValue("_auth_refresh");
    const startTime = Date.now();

    if (!refreshToken) {
      const result: RefreshTestResult = {
        success: false,
        message: "No refresh token found",
        timestamp: new Date(),
        error: "Missing refresh token",
        responseTime: 0,
      };
      setRefreshTests((prev) => [result, ...prev]);
      setIsTestingRefresh(false);
      toast.error("No refresh token found!");
      return;
    }

    try {
      debugger;
      const response = await fetch(
        `${import.meta.env.VITE_EDITAR_BASE_API_URL}/auth/refresh`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            refresh_token: refreshToken,
          }),
        }
      );

      const responseTime = Date.now() - startTime;

      if (response.ok) {
        const data = await response.json();
        const result: RefreshTestResult = {
          success: true,
          message: `Token refreshed successfully. New token expires in ${data.expires_in} seconds`,
          timestamp: new Date(),
          newTokenReceived: !!data.access_token,
          responseTime,
        };
        setRefreshTests((prev) => [result, ...prev]);

        // Save new tokens to auth kit store
        signIn({
          auth: {
            token: data.access_token,
            type: "Bearer",
          },
          refresh: data.refresh_token,
          userState: authUser, // Preserve existing user state
        });

        toast.success("Refresh token test successful!");
        // Re-analyze tokens after refresh
        setTimeout(analyzeTokens, 1000);
      } else {
        const errorData = await response.json().catch(() => ({}));
        const result: RefreshTestResult = {
          success: false,
          message: `Refresh failed: ${response.status} ${response.statusText}`,
          timestamp: new Date(),
          error: errorData.detail || errorData.message || "Unknown error",
          responseTime,
        };
        setRefreshTests((prev) => [result, ...prev]);
        toast.error("Refresh token test failed!");
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      const result: RefreshTestResult = {
        success: false,
        message: "Network error during refresh test",
        timestamp: new Date(),
        error: error instanceof Error ? error.message : "Unknown error",
        responseTime,
      };
      setRefreshTests((prev) => [result, ...prev]);
      toast.error("Refresh token test failed!");
    } finally {
      setIsTestingRefresh(false);
    }
  };

  const handleCopyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  const handleSignOut = () => {
    signOut();
    toast.success("Signed out successfully");
  };

  // Monitor network requests for automatic refresh
  useEffect(() => {
    if (!autoRefreshMonitor) return;

    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const [url, options] = args;
      const startTime = Date.now();

      try {
        const response = await originalFetch(...args);
        const endTime = Date.now();

        if (typeof url === "string" && url.includes("/auth/refresh")) {
          const log: NetworkLog = {
            url,
            method: options?.method || "GET",
            status: response.status,
            timestamp: new Date(),
            duration: endTime - startTime,
            success: response.ok,
          };
          setNetworkLogs((prev) => [log, ...prev.slice(0, 9)]); // Keep last 10 logs
        }

        return response;
      } catch (error) {
        const endTime = Date.now();
        if (typeof url === "string" && url.includes("/auth/refresh")) {
          const log: NetworkLog = {
            url,
            method: options?.method || "GET",
            status: 0,
            timestamp: new Date(),
            duration: endTime - startTime,
            success: false,
            error: error instanceof Error ? error.message : "Unknown error",
          };
          setNetworkLogs((prev) => [log, ...prev.slice(0, 9)]);
        }
        throw error;
      }
    };

    return () => {
      window.fetch = originalFetch;
    };
  }, [autoRefreshMonitor]);

  // Update token info periodically
  useEffect(() => {
    analyzeTokens();
    const interval = setInterval(analyzeTokens, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const allowedPaths = {
    student: [
      ...studentNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
    ],
    teacher: [
      ...teacherNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
    ],
    parent: [
      ...parentNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
    ],
    admin: [
      ...directorNavItems.map((item) => item.path),
      "/notifications",
      "/assistant",
    ],
  };

  const normalizedRole = authUser?.role?.trim().toLowerCase();
  const userAllowedPaths =
    allowedPaths[normalizedRole as keyof typeof allowedPaths] || [];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">
          Authentication Debug & Refresh Token Testing
        </h1>
        <Button
          onClick={handleSignOut}
          variant="destructive"
          className="flex items-center gap-2"
        >
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>
      </div>

      {/* Authentication Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Authentication Status
            <Badge variant={isAuthenticated ? "default" : "destructive"}>
              {isAuthenticated ? "Authenticated" : "Not Authenticated"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>Is Authenticated:</strong>{" "}
              {isAuthenticated ? "Yes" : "No"}
            </p>
            <p>
              <strong>User Object:</strong> {authUser ? "Present" : "Null"}
            </p>
            <p>
              <strong>Timestamp:</strong> {new Date().toISOString()}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* User Information */}
      {authUser && (
        <Card>
          <CardHeader>
            <CardTitle>User Information</CardTitle>
            <CardDescription>
              Current authenticated user details
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p>
                  <strong>ID:</strong> {authUser.id}
                </p>
                <p>
                  <strong>Name:</strong> {authUser.name}
                </p>
                <p>
                  <strong>Email:</strong> {authUser.email}
                </p>
                <p>
                  <strong>Role:</strong> <Badge>{authUser.role}</Badge>
                </p>
              </div>
              <div>
                <p>
                  <strong>Normalized Role:</strong>{" "}
                  <Badge variant="outline">{normalizedRole}</Badge>
                </p>
                <p>
                  <strong>Current Time:</strong> {new Date().toLocaleString()}
                </p>
                <p>
                  <strong>Session Active:</strong>{" "}
                  <Badge variant={isAuthenticated ? "default" : "destructive"}>
                    {isAuthenticated ? "Yes" : "No"}
                  </Badge>
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Token Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Token Information & Testing
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowTokens(!showTokens)}
              className="flex items-center gap-1"
            >
              {showTokens ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              {showTokens ? "Hide" : "Show"} Tokens
            </Button>
          </CardTitle>
          <CardDescription>
            JWT tokens stored in cookies and refresh testing
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Auth Token Info */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <strong>Auth Token:</strong>
                <Badge variant={tokenInfo.auth ? "default" : "destructive"}>
                  {tokenInfo.auth ? "Present" : "Missing"}
                </Badge>
                {tokenInfo.auth && (
                  <>
                    <Badge
                      variant={
                        tokenInfo.auth.isExpired ? "destructive" : "default"
                      }
                    >
                      {tokenInfo.auth.isExpired ? "Expired" : "Valid"}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {tokenInfo.auth.timeUntilExpiry}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleCopyToClipboard(
                          tokenInfo.auth!.token,
                          "Auth token"
                        )
                      }
                      className="flex items-center gap-1"
                    >
                      <Copy className="h-3 w-3" />
                      Copy
                    </Button>
                  </>
                )}
              </div>
              {showTokens && tokenInfo.auth && (
                <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                  {tokenInfo.auth.token}
                </div>
              )}
            </div>

            {/* Refresh Token Info */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <strong>Refresh Token:</strong>
                <Badge variant={tokenInfo.refresh ? "default" : "destructive"}>
                  {tokenInfo.refresh ? "Present" : "Missing"}
                </Badge>
                {tokenInfo.refresh && (
                  <>
                    <Badge
                      variant={
                        tokenInfo.refresh.isExpired ? "destructive" : "default"
                      }
                    >
                      {tokenInfo.refresh.isExpired ? "Expired" : "Valid"}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {tokenInfo.refresh.timeUntilExpiry}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        handleCopyToClipboard(
                          tokenInfo.refresh!.token,
                          "Refresh token"
                        )
                      }
                      className="flex items-center gap-1"
                    >
                      <Copy className="h-3 w-3" />
                      Copy
                    </Button>
                  </>
                )}
              </div>
              {showTokens && tokenInfo.refresh && (
                <div className="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                  {tokenInfo.refresh.token}
                </div>
              )}
            </div>

            {/* Refresh Token Testing */}
            <div className="border-t pt-4">
              <div className="flex items-center gap-2 mb-4">
                <h3 className="text-lg font-semibold">Refresh Token Testing</h3>
                <Button
                  onClick={testRefreshToken}
                  disabled={isTestingRefresh || !tokenInfo.refresh}
                  className="flex items-center gap-2"
                >
                  {isTestingRefresh ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                  {isTestingRefresh ? "Testing..." : "Test Refresh"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setAutoRefreshMonitor(!autoRefreshMonitor)}
                  className="flex items-center gap-2"
                >
                  <Monitor className="h-4 w-4" />
                  {autoRefreshMonitor ? "Stop" : "Start"} Monitor
                </Button>
              </div>

              {/* Test Results */}
              {refreshTests.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Test Results:</h4>
                  <div className="max-h-60 overflow-y-auto space-y-2">
                    {refreshTests.map((test, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded border ${
                          test.success
                            ? "bg-green-50 border-green-200"
                            : "bg-red-50 border-red-200"
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-1">
                          {test.success ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )}
                          <span className="font-medium">
                            {test.success ? "Success" : "Failed"}
                          </span>
                          <span className="text-sm text-gray-500">
                            {test.timestamp.toLocaleTimeString()}
                          </span>
                          {test.responseTime && (
                            <span className="text-sm text-gray-500">
                              ({test.responseTime}ms)
                            </span>
                          )}
                        </div>
                        <p className="text-sm">{test.message}</p>
                        {test.error && (
                          <p className="text-sm text-red-600 mt-1">
                            Error: {test.error}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Network Logs */}
              {autoRefreshMonitor && networkLogs.length > 0 && (
                <div className="mt-4 space-y-2">
                  <h4 className="font-medium">
                    Automatic Refresh Network Logs:
                  </h4>
                  <div className="max-h-40 overflow-y-auto space-y-1">
                    {networkLogs.map((log, index) => (
                      <div
                        key={index}
                        className={`p-2 rounded text-sm ${
                          log.success ? "bg-blue-50" : "bg-red-50"
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={log.success ? "default" : "destructive"}
                          >
                            {log.status}
                          </Badge>
                          <span>{log.method}</span>
                          <span className="text-gray-500">
                            {log.timestamp.toLocaleTimeString()}
                          </span>
                          <span className="text-gray-500">
                            ({log.duration}ms)
                          </span>
                        </div>
                        {log.error && (
                          <p className="text-red-600 mt-1">{log.error}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Role-Based Paths */}
      <Card>
        <CardHeader>
          <CardTitle>Role-Based Allowed Paths</CardTitle>
          <CardDescription>
            Paths accessible to the current user role
          </CardDescription>
        </CardHeader>
        <CardContent>
          {normalizedRole ? (
            <div>
              <p className="mb-2">
                <span className="font-medium">Role:</span> {normalizedRole}
              </p>
              <p className="mb-2">
                <span className="font-medium">
                  Allowed Paths ({userAllowedPaths.length}):
                </span>
              </p>
              <ul className="text-sm space-y-1 max-h-40 overflow-y-auto">
                {userAllowedPaths.map((path, index) => (
                  <li key={index} className="bg-gray-100 px-2 py-1 rounded">
                    {path}
                  </li>
                ))}
              </ul>
            </div>
          ) : (
            <p className="text-gray-500">No role information available</p>
          )}
        </CardContent>
      </Card>

      {/* Current Path Status */}
      <Card>
        <CardHeader>
          <CardTitle>Current Path Status</CardTitle>
          <CardDescription>
            Authorization status for current location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div>
              <span className="font-medium">Current Path:</span>{" "}
              <span className="bg-gray-100 px-2 py-1 rounded">
                {window.location.pathname}
              </span>
            </div>
            <div>
              <span className="font-medium">Is Path Allowed:</span>{" "}
              <Badge
                variant={
                  userAllowedPaths.includes(window.location.pathname)
                    ? "default"
                    : "destructive"
                }
              >
                {userAllowedPaths.includes(window.location.pathname)
                  ? "Yes"
                  : "No"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Dashboard Access:</span>{" "}
              <Badge
                variant={
                  userAllowedPaths.includes("/dashboard")
                    ? "default"
                    : "destructive"
                }
              >
                {userAllowedPaths.includes("/dashboard") ? "Allowed" : "Denied"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Environment Information */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Information</CardTitle>
          <CardDescription>System and configuration details</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>API Base URL:</strong>{" "}
              {import.meta.env.VITE_EDITAR_BASE_API_URL}
            </p>
            <p>
              <strong>Current Domain:</strong> {window.location.hostname}
            </p>
            <p>
              <strong>Protocol:</strong> {window.location.protocol}
            </p>
            <p>
              <strong>Current Path:</strong> {window.location.pathname}
            </p>
            <p>
              <strong>User Agent:</strong> {navigator.userAgent}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Raw Data */}
      <Card>
        <CardHeader>
          <CardTitle>Raw Auth User Data</CardTitle>
          <CardDescription>
            Complete user object from authentication state
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-60">
            {JSON.stringify(authUser, null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
