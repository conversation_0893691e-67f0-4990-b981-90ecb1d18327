import { create } from "zustand";
import { persist } from "zustand/middleware";
import { User, Assignment, Subject } from "../types";

export interface AppState {
  user: User | null;
  token: string | null;
  subjects: Subject[];
  assignments: Assignment[];
  loadingStates: {
    auth: boolean;
    subjects: boolean;
    assignments: boolean;
    notifications: boolean;
  };
  setupCompleted: boolean;
  setUser: (user: User | null, token?: string) => void;
  setSubjects: (subjects: Subject[]) => void;
  setAssignments: (assignments: Assignment[]) => void;
  setLoadingState: (
    key: keyof AppState["loadingStates"],
    isLoading: boolean
  ) => void;
  logout: () => void;
  setSetupCompleted: (completed: boolean) => void;
}

export const useStore = create<AppState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      subjects: [],
      assignments: [],
      loadingStates: {
        auth: false,
        subjects: false,
        assignments: false,
        notifications: false,
      },
      setupCompleted: false,
      setUser: (user, token) => set({ user, token: token || null }),
      setSubjects: (subjects) =>
        set((state) =>
          JSON.stringify(state.subjects) !== JSON.stringify(subjects)
            ? { subjects }
            : state
        ),
      setAssignments: (assignments) =>
        set((state) =>
          JSON.stringify(state.assignments) !== JSON.stringify(assignments)
            ? { assignments }
            : state
        ),
      setLoadingState: (key, isLoading) =>
        set((state) => ({
          loadingStates: {
            ...state.loadingStates,
            [key]: isLoading,
          },
        })),
      // logout: () => set({ user: null, token: null }),
      logout: () => set({ user: null, token: null, setupCompleted: false }),
      setSetupCompleted: (completed) => set({ setupCompleted: completed }),
    }),
    {
      name: "auth-storage",
    }
  )
);
