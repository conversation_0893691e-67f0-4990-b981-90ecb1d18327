import { useState } from "react";
import { useTranslation } from "react-i18next";
import AttendanceCard from "../../components/AttendanceCard";
import { Button } from "@/components/ui/button";
import { Search, Plus } from "lucide-react";
import RecordAbsenceDialog from "../../components/RecordAbsenceDialog";
import RecordAttendanceDialog from "../../components/RecordAttendanceDialog";
import WarningManagement from "../../components/WarningManagement";

export default function TeacherAttendancePage() {
  const { t } = useTranslation();
  const [filterStatus, setFilterStatus] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [showAbsenceDialog, setShowAbsenceDialog] = useState(false);
  const [showAttendanceDialog, setShowAttendanceDialog] = useState(false);

  // Format today's date as Apr 10, 2024
  const today = new Date();
  const formattedDate = today.toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });

  // Add function to handle new absence records
  const handleRecordAbsence = (absenceData: any) => {
    console.log("Recording absence:", absenceData);
    // Here you would typically make an API call to save the absence
    // Then update the attendanceData state with the new record
  };

  // Add function to handle attendance records
  const handleRecordAttendance = (attendanceData: any) => {
    console.log("Recording attendance:", attendanceData);
    // Here you would typically make an API call to save the attendance
    // Then update the attendanceData state with the new records
  };

  const attendanceData = [
    {
      subject: "Mathematics",
      date: "Mon, Mar 10, 2025",
      reason: "Doctor appointment",
      status: "Excused",
    },
    {
      subject: "Physics",
      date: "Tue, Mar 11, 2025",
      reason: "Missed bus",
      status: "Unexcused",
    },
    {
      subject: "Biology",
      date: "Wed, Mar 12, 2025",
      reason: "Family emergency",
      status: "Pending",
    },
  ];

  const statusOptions = ["All", "Excused", "Unexcused", "Pending"];

  const filteredAttendanceData = attendanceData.filter((item) => {
    const matchesStatus =
      filterStatus === "All" || item.status === filterStatus;
    const matchesSearch = item.subject
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-3">
        <h2 className="text-2xl sm:text-3xl font-bold">
          {t("attendance.studentAttendance")}
        </h2>
        <div className="flex gap-2 w-full sm:w-auto">
          <Button
            className="bg-[#7B3AED] hover:bg-[#6D28D9] text-white flex-1 sm:flex-auto"
            onClick={() => setShowAttendanceDialog(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            {t("attendance.recordAttendance")}
          </Button>
          <Button
            variant="outline"
            className="flex-1 sm:flex-auto"
            onClick={() => setShowAbsenceDialog(true)}
          >
            <Plus className="w-4 h-4 mr-2" />
            {t("attendance.recordAbsence")}
          </Button>
        </div>
      </div>

      {/* Add the RecordAbsenceDialog component */}
      <RecordAbsenceDialog
        isOpen={showAbsenceDialog}
        onClose={() => setShowAbsenceDialog(false)}
        onSubmit={handleRecordAbsence}
      />

      {/* Add the RecordAttendanceDialog component */}
      <RecordAttendanceDialog
        isOpen={showAttendanceDialog}
        onClose={() => setShowAttendanceDialog(false)}
        onSubmit={handleRecordAttendance}
        subject="Mathematics"
        date={formattedDate}
      />

      <div className="bg-white p-4 sm:p-5 border border-gray-300 rounded-md">
        <div className="pb-4">
          <p className="text-xl font-bold">{t("attendance.absenceLog")}</p>
          <p className="text-sm sm:text-base">
            {t("attendance.recordabsences")}
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-grow">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder={t("attendance.searchBySubject")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border rounded-md px-4 py-2 w-full pl-10"
            />
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilterStatus("All")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "All"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.all")}
            </button>
            <button
              onClick={() => setFilterStatus("Excused")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "Excused"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.excused")}
              {filterStatus !== "Excused" && (
                <span className="ml-1">
                  (
                  {
                    attendanceData.filter((item) => item.status === "Excused")
                      .length
                  }
                  )
                </span>
              )}
            </button>
            <button
              onClick={() => setFilterStatus("Unexcused")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "Unexcused"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.unexcused")}
              {filterStatus !== "Unexcused" && (
                <span className="ml-1">
                  (
                  {
                    attendanceData.filter((item) => item.status === "Unexcused")
                      .length
                  }
                  )
                </span>
              )}
            </button>
            <button
              onClick={() => setFilterStatus("Pending")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "Pending"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("attendance.status.pending")}
              {filterStatus !== "Pending" && (
                <span className="ml-1">
                  (
                  {
                    attendanceData.filter((item) => item.status === "Pending")
                      .length
                  }
                  )
                </span>
              )}
            </button>
          </div>
        </div>

        {/* Attendance Cards */}
        <div className="space-y-2 sm:space-y-4">
          {filteredAttendanceData.map((item, index) => (
            <AttendanceCard
              key={index}
              subject={item.subject}
              date={item.date}
              reason={item.reason}
              status={item.status}
            />
          ))}
        </div>

        {filteredAttendanceData.length === 0 && (
          <p className="text-center text-gray-500 py-4">
            {t("attendance.noRecordsFound")}
          </p>
        )}
      </div>

      <WarningManagement
        title={t("warnings.classWarnings")}
        description={t("warnings.teacherDescription")}
      />
    </div>
  );
}

// import React, { useEffect, useState } from "react";
// import axios from "axios";
// import AttendanceCard from "../../components/AttendanceCard";

// export default function TeacherAttendancePage() {
//   const [attendanceData, setAttendanceData] = useState([]);

//   useEffect(() => {
//     axios.get("/api/attendance")
//       .then(res => {
//         // Optional: format the date
//         const formattedData = res.data.map(item => ({
//           ...item,
//           date: new Date(item.date).toLocaleDateString("en-US", {
//             weekday: "short",
//             month: "short",
//             day: "numeric",
//             year: "numeric",
//           }),
//         }));
//         setAttendanceData(formattedData);
//       })
//       .catch(err => {
//         console.error("Error fetching attendance data:", err);
//       });
//   }, []);

//   return (
//     <div className="w-full">
//       <div className="flex items-center justify-between mb-4">
//         <h2 className="text-3xl font-bold">Student Attendance</h2>
//       </div>
//       <div className="bg-white p-5 border border-gray-300 rounded-md">
//         <div className="pb-4">
//           <p className="text-xl font-bold">Absence Log</p>
//           <p>Record of student absences</p>
//         </div>
//         {attendanceData.length > 0 ? (
//           attendanceData.map((item, index) => (
//             <AttendanceCard
//               key={index}
//               subject={item.subject}
//               date={item.date}
//               reason={item.reason}
//               status={item.status}
//             />
//           ))
//         ) : (
//           <p>No attendance records available.</p>
//         )}
//       </div>
//     </div>
//   );
// }

// API RESPONSE
// [
//   {
//     "subject": "Mathematics",
//     "date": "2025-03-10T00:00:00Z",
//     "reason": "Doctor appointment",
//     "status": "Excused"
//   },
//   {
//     "subject": "Physics",
//     "date": "2025-03-11T00:00:00Z",
//     "reason": "Missed bus",
//     "status": "Unexcused"
//   },
//   {
//     "subject": "Biology",
//     "date": "2025-03-12T00:00:00Z",
//     "reason": "Family emergency",
//     "status": "Pending"
//   }
// ]
