import { Plus, ClipboardList } from "lucide-react";
import AssignmentsView from "./AssignmentsView";
import { assignments } from "./mockData";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import { validateRequired } from "../../lib/validation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

export default function TeacherAssignmentsPage() {
  const [loading, _] = useState(false);
  const { t } = useTranslation();
  const [filterStatus, setFilterStatus] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAssignment, setSelectedAssignment] = useState<any>(null);
  const [showSubmissions, setShowSubmissions] = useState(false);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<any>(null);
  const [editingAssignment, setEditingAssignment] = useState<any>(null);
  const [newAssignment, setNewAssignment] = useState({
    title: "",
    description: "",
    dueDate: "",
    points: "100",
  });

  // Additional handlers for edit functionality
  const handleEditAssignment = (assignment: any) => {
    setEditingAssignment({
      ...assignment,
      points: assignment.points || "100",
    });
    setShowEditDialog(true);
  };

  const handleSaveEdit = () => {
    setShowEditDialog(false);
    setEditingAssignment(null);
    // Here you would typically make an API call to update the assignment
  };

  // Mock submissions data
  const mockSubmissions = [
    {
      studentName: "John Doe",
      submissionDate: "2023-05-15",
      grade: "A",
      submissionContent: "Lorem ipsum dolor sit amet...",
    },
    {
      studentName: "Jane Smith",
      submissionDate: "2023-05-14",
      grade: "B+",
      submissionContent: "Consectetur adipiscing elit...",
    },
    {
      studentName: "Alex Johnson",
      submissionDate: "2023-05-16",
      grade: null,
      submissionContent: "Sed do eiusmod tempor incididunt...",
    },
  ];

  const handleViewSubmissions = (assignment: any) => {
    setSelectedAssignment(assignment);
    setShowSubmissions(true);
  };

  const closeSubmissionsModal = () => {
    setShowSubmissions(false);
    setSelectedAssignment(null);
  };

  const handleCreateAssignment = () => {
    // Validate assignment data
    if (!validateRequired(newAssignment.title)) {
      toast.error(t("validation.titleRequired"));
      return;
    }

    if (!validateRequired(newAssignment.description)) {
      toast.error(t("validation.descriptionRequired"));
      return;
    }

    if (!validateRequired(newAssignment.dueDate)) {
      toast.error(t("validation.dueDateRequired"));
      return;
    }

    setShowCreateDialog(false);
    setNewAssignment({
      title: "",
      description: "",
      dueDate: "",
      points: "100",
    });

    toast.success(t("assignmentsMessages.assignmentCreated"));
    // Here you would typically make an API call to save the assignment
  };

  const handleReviewSubmission = (submission: any) => {
    setSelectedSubmission(submission);
    setShowReviewDialog(true);
  };

  const handleSubmitReview = () => {
    setShowReviewDialog(false);
    setSelectedSubmission(null);
    toast.success(t("assignmentsMessages.reviewSubmitted"));
  };

  const filteredAssignments = assignments.filter((assignment) => {
    const matchesStatus =
      filterStatus === "All" || assignment.status === filterStatus;
    const matchesSearch = assignment.title
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  const actionButton = (
    <Button
      className="bg-[#7B3AED] hover:bg-[#6D28D9] text-white w-full sm:w-auto"
      onClick={() => setShowCreateDialog(true)}
    >
      <Plus className="w-4 h-4 mr-2" />
      {t("assignments.createAssignment")}
    </Button>
  );

  return (
    <div className="w-full">
      <AssignmentsView
        title={t("assignments.manageAssignments")}
        desc={t("assignments.allAssignments")}
        actionButton={actionButton}
      >
        {/* Filter Controls - Responsive */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-grow">
            <input
              type="text"
              placeholder={t("assignments.searchAssignments")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="border rounded-md px-4 py-2 w-full"
            />
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setFilterStatus("All")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "All"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("assignments.all")}
            </button>
            <button
              onClick={() => setFilterStatus("completed")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "completed"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("assignments.status.completed")}
              {filterStatus !== "completed" && (
                <span className="ml-1">
                  (
                  {
                    assignments.filter((item) => item.status === "completed")
                      .length
                  }
                  )
                </span>
              )}
            </button>
            <button
              onClick={() => setFilterStatus("due soon")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "due soon"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("assignments.status.duesoon")}
              {filterStatus !== "due soon" && (
                <span className="ml-1">
                  (
                  {
                    assignments.filter((item) => item.status === "due soon")
                      .length
                  }
                  )
                </span>
              )}
            </button>
            <button
              onClick={() => setFilterStatus("overdue")}
              className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                filterStatus === "overdue"
                  ? "bg-gray-700 text-white"
                  : "bg-gray-100 text-gray-700"
              }`}
            >
              {t("assignments.status.overdue")}
              {filterStatus !== "overdue" && (
                <span className="ml-1">
                  (
                  {
                    assignments.filter((item) => item.status === "overdue")
                      .length
                  }
                  )
                </span>
              )}
            </button>
          </div>
        </div>

        {loading ? (
          <p>{t("assignments.loadingAssignments")}</p>
        ) : (
          filteredAssignments.map((assignment, index) => (
            <div key={index} className="py-2">
              <div className="border border-gray-300 rounded-md bg-white py-4 px-4 sm:px-6 md:px-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                  <p className="font-semibold">{assignment.title}</p>
                  <p className="before:content-['•'] before:mr-1 before:text-gray-400 text-gray-700 text-sm">
                    {t("assignments.dueDate")}: {assignment.dueDate}
                  </p>
                </div>
                <div className="font-semibold flex flex-wrap items-center gap-2 sm:gap-3 w-full sm:w-auto">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEditAssignment(assignment)}
                    className="w-full sm:w-auto"
                  >
                    {t("edit")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewSubmissions(assignment)}
                    className="flex items-center gap-1 w-full sm:w-auto"
                  >
                    <ClipboardList className="w-4 h-4" />
                    {t("viewSubmissions")}
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </AssignmentsView>

      {/* Submissions Dialog */}
      <Dialog open={showSubmissions} onOpenChange={setShowSubmissions}>
        <DialogContent className="w-[95vw] max-w-[700px] p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>
              {selectedAssignment
                ? `${t("submissions.viewFor")} ${selectedAssignment.title}`
                : t("submissions.viewSubmissions")}
            </DialogTitle>
          </DialogHeader>

          <div className="py-4 overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="whitespace-nowrap">
                    {t("submissions.student")}
                  </TableHead>
                  <TableHead className="whitespace-nowrap">
                    {t("submissions.date")}
                  </TableHead>
                  <TableHead className="whitespace-nowrap">
                    {t("submissions.grade")}
                  </TableHead>
                  <TableHead className="whitespace-nowrap">
                    {t("submissions.actions")}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {mockSubmissions.map((submission, idx) => (
                  <TableRow key={idx}>
                    <TableCell className="whitespace-nowrap">
                      {submission.studentName}
                    </TableCell>
                    <TableCell className="whitespace-nowrap">
                      {submission.submissionDate}
                    </TableCell>
                    <TableCell>
                      {submission.grade || (
                        <span className="text-muted-foreground">
                          {t("submissions.notGraded")}
                        </span>
                      )}
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="link"
                        className="p-0 h-auto"
                        onClick={() => handleReviewSubmission(submission)}
                      >
                        {t("submissions.review")}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button variant="outline" onClick={closeSubmissionsModal}>
              {t("close")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Assignment Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="w-[95vw] max-w-[600px] p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>{t("assignments.createAssignment")}</DialogTitle>
            <DialogDescription>
              {t("assignments.createDescription")}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="title" className="sm:text-right">
                {t("assignments.title")}
              </Label>
              <Input
                id="title"
                value={newAssignment.title}
                onChange={(e) =>
                  setNewAssignment({ ...newAssignment, title: e.target.value })
                }
                className="col-span-1 sm:col-span-3"
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="description" className="sm:text-right">
                {t("assignments.description")}
              </Label>
              <Textarea
                id="description"
                value={newAssignment.description}
                onChange={(e) =>
                  setNewAssignment({
                    ...newAssignment,
                    description: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="dueDate" className="sm:text-right">
                {t("assignments.dueDate")}
              </Label>
              <Input
                id="dueDate"
                type="date"
                value={newAssignment.dueDate}
                onChange={(e) =>
                  setNewAssignment({
                    ...newAssignment,
                    dueDate: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3"
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="points" className="sm:text-right">
                {t("assignments.points")}
              </Label>
              <Input
                id="points"
                type="number"
                value={newAssignment.points}
                onChange={(e) =>
                  setNewAssignment({ ...newAssignment, points: e.target.value })
                }
                className="col-span-1 sm:col-span-3"
              />
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setShowCreateDialog(false)}
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-[#7B3AED] hover:bg-[#6D28D9] text-white"
              onClick={handleCreateAssignment}
            >
              {t("create")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Review Submission Dialog */}
      <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
        <DialogContent className="w-[95vw] max-w-[600px] p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>{t("submissions.reviewSubmission")}</DialogTitle>
            <DialogDescription>
              {selectedSubmission &&
                `${selectedSubmission.studentName} - ${selectedSubmission.submissionDate}`}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="border p-4 rounded-md bg-gray-50">
              <h4 className="font-medium mb-2">
                {t("submissions.submissionContent")}
              </h4>
              <p className="text-sm text-gray-700">
                {selectedSubmission?.submissionContent}
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="grade" className="sm:text-right">
                {t("submissions.grade")}
              </Label>
              <Input
                id="grade"
                defaultValue={selectedSubmission?.grade || ""}
                placeholder="A, B+, C, etc."
                className="col-span-1 sm:col-span-3"
              />
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="feedback" className="sm:text-right">
                {t("submissions.feedback")}
              </Label>
              <Textarea
                id="feedback"
                placeholder={t("submissions.provideFeedback")}
                className="col-span-1 sm:col-span-3"
                rows={4}
              />
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setShowReviewDialog(false)}
            >
              {t("cancel")}
            </Button>
            <Button
              className="bg-[#7B3AED] hover:bg-[#6D28D9] text-white"
              onClick={handleSubmitReview}
            >
              {t("submissions.submitReview")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Assignment Dialog */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent className="w-[95vw] max-w-[600px] p-4 sm:p-6">
          <DialogHeader>
            <DialogTitle>{t("assignments.editAssignment")}</DialogTitle>
            <DialogDescription>
              {t("assignments.editDescription")}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="edit-title" className="sm:text-right">
                {t("assignments.title")}
              </Label>
              <Input
                id="edit-title"
                value={editingAssignment?.title || ""}
                onChange={(e) =>
                  setEditingAssignment({
                    ...editingAssignment,
                    title: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3"
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="edit-description" className="sm:text-right">
                {t("assignments.description")}
              </Label>
              <Textarea
                id="edit-description"
                value={editingAssignment?.description || ""}
                onChange={(e) =>
                  setEditingAssignment({
                    ...editingAssignment,
                    description: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="edit-dueDate" className="sm:text-right">
                {t("assignments.dueDate")}
              </Label>
              <Input
                id="edit-dueDate"
                type="date"
                value={editingAssignment?.dueDate || ""}
                onChange={(e) =>
                  setEditingAssignment({
                    ...editingAssignment,
                    dueDate: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3"
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="edit-points" className="sm:text-right">
                {t("assignments.points")}
              </Label>
              <Input
                id="edit-points"
                type="number"
                value={editingAssignment?.points || ""}
                onChange={(e) =>
                  setEditingAssignment({
                    ...editingAssignment,
                    points: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3"
              />
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-4 items-start sm:items-center gap-2 sm:gap-4">
              <Label htmlFor="edit-status" className="sm:text-right">
                {t("assignments.status.label")}
              </Label>
              <select
                id="edit-status"
                value={editingAssignment?.status || ""}
                onChange={(e) =>
                  setEditingAssignment({
                    ...editingAssignment,
                    status: e.target.value,
                  })
                }
                className="col-span-1 sm:col-span-3 w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="completed">
                  {t("assignments.status.completed")}
                </option>
                <option value="due soon">
                  {t("assignments.status.duesoon")}
                </option>
                <option value="overdue">
                  {t("assignments.status.overdue")}
                </option>
              </select>
            </div>
          </div>

          <DialogFooter className="flex-col sm:flex-row gap-2 sm:gap-0">
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              {t("cancel")}
            </Button>
            <Button
              className="bg-[#7B3AED] hover:bg-[#6D28D9] text-white"
              onClick={handleSaveEdit}
            >
              {t("save")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
