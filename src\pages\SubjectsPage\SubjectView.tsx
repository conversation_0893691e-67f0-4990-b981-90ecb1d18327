import { Book<PERSON><PERSON>, Users, Settings } from "lucide-react";
import TopicList from "../../components/TopicList";
import SubjectCard from "../../components/SubjectCard";
import {
  subjects,
  studentSubjects,
  teacherSubjects,
  directorSubjects,
  parentSubjects,
  latestTopics,
} from "./mockData";
import { useTranslation } from "react-i18next";
import { useTypedAuthUser } from "../../hooks/useAuth";

interface SubjectViewProps {
  headerTitle?: string;
  headerButton?: React.ReactNode;
  showTeacherAssignment?: boolean;
  showSubjectSettings?: boolean;
  customSubjects?: any[]; // Add this prop
}

export default function SubjectView({
  headerTitle,
  headerButton,
  showTeacherAssignment = false,
  showSubjectSettings = false,
  customSubjects, // Add this prop
}: SubjectViewProps) {
  const { t } = useTranslation();
  const authUser = useTypedAuthUser();

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();

  // Select the appropriate subjects based on user role or use custom subjects if provided
  const getSubjectsForRole = () => {
    if (customSubjects) return customSubjects;

    switch (normalizedRole) {
      case "student":
        return studentSubjects;
      case "teacher":
        return teacherSubjects;
      case "parent":
        return parentSubjects;
      case "admin":
        return directorSubjects;
      default:
        console.warn("SubjectView: Unknown role, using default subjects", {
          originalRole: authUser?.role,
          normalizedRole,
        });
        return subjects;
    }
  };

  const roleSubjects = getSubjectsForRole();

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row items-start sm:items-center sm:justify-between gap-4 mb-6">
        <h2 className="text-2xl sm:text-3xl font-bold">{headerTitle}</h2>
        {headerButton}
      </div>

      <p className="font-bold text-lg pb-4">{t("subjects.allSubjects")}</p>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pb-10">
        {roleSubjects.map((subject) => (
          <div key={subject.id} className="relative">
            <SubjectCard
              subject={subject}
              isTeacher={normalizedRole === "teacher"}
            />
            {showTeacherAssignment && (
              <button
                className="absolute top-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-50"
                title={t("subjects.assignTeacher")}
              >
                <Users className="w-4 h-4 text-gray-600" />
              </button>
            )}
            {showSubjectSettings && (
              <button
                className="absolute bottom-2 right-2 p-1 bg-white rounded-full shadow-md hover:bg-gray-50"
                title={t("subjects.subjectSettings")}
              >
                <Settings className="w-4 h-4 text-gray-600" />
              </button>
            )}
          </div>
        ))}
      </div>

      <div className="border border-gray-300 bg-white p-4 sm:p-5 md:p-8 rounded-md">
        <div className="flex items-center justify-between pb-4 sm:pb-6">
          <div className="flex items-center">
            <BookOpen className="w-5 h-5 sm:w-6 sm:h-6 mt-1" />
            <p className="pl-2 font-bold text-base sm:text-lg">
              {t("subjects.latestTopics")}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 sm:gap-5">
          {latestTopics.map((topic, index) => (
            <TopicList key={index} topic={topic} />
          ))}
        </div>
      </div>
    </div>
  );
}
