import Sidebar from "../components/Sidebar";
import Header from "../components/Header";
import ChatAssistant from "../components/ChatAssistant/ChatAssistant";

export default function DirectorLayout({
  children,
  toggleSidebar,
  isSidebarOpen,
  isMobile,
  hideAssistant = false,
}: any) {
  return (
    <div className="flex h-screen bg-gray-50 select-none">
      <Sidebar toggleSidebar={toggleSidebar} isSidebarOpen={isSidebarOpen} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          toggleSidebar={toggleSidebar}
          isSidebarOpen={isSidebarOpen}
          isMobile={isMobile}
        />
        <main className="flex-1 overflow-auto p-6">
          <div className="mx-auto">{children}</div>
        </main>
      </div>
      {!hideAssistant && <ChatAssistant />}
    </div>
  );
}
