import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ist,
  FilePlus,
  Upload,
  FileChe<PERSON>,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import CurriculumView from "./CurriculumView";
import ActivityDialog from "@/components/ActivityDialog/ActivityDialog";
import MaterialUploadDialog from "@/components/MaterialUploadDialog/MaterialUploadDialog";
import HomeworkAssignmentDialog from "@/components/HomeworkAssignmentDialog/HomeworkAssignmentDialog";
import AssessmentPlanDialog from "@/components/AssessmentPlanDialog/AssessmentPlanDialog";
import LessonOutcomeDialog from "@/components/LessonOutcomeDialog/LessonOutcomeDialog";
import { toast } from "react-hot-toast";
import { useState } from "react";
import { LessonOutcomes } from "@/components/LessonOutcomeForm/LessonOutcomeForm";

export default function TeacherCurriculumPage() {
  const { t } = useTranslation();
  const [lessonOutcomes, setLessonOutcomes] = useState<
    Record<string, LessonOutcomes>
  >({});

  const schedule = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].map(
    (day, index) => ({
      day,
      date: `Apr ${21 + index}`,
      lessons: [
        {
          subject: "Physics: Newton's Laws",
          color: "bg-purple-100 text-purple-800",
        },
        {
          subject: "Biology: Cell Structure",
          color: "bg-green-100 text-green-800",
        },
      ],
    })
  );

  const lessonCards = [
    {
      title: "Newton's Laws of Motion",
      description:
        "Understanding the fundamental principles of classical mechanics through Newton's three laws of motion.",
      objectives: [
        "Define and explain Newton's First Law of Motion",
        "Apply Newton's Second Law in mathematical problems",
        "Identify action-reaction pairs using Newton's Third Law",
      ],
      resources: 5,
      exercises: 3,
    },
    {
      title: "Cell Structure and Function",
      description:
        "Exploring the basic unit of life, including cell organelles and their functions.",
      objectives: [
        "Identify major cell organelles",
        "Compare plant and animal cells",
        "Explain cellular transport mechanisms",
      ],
      resources: 4,
      exercises: 2,
    },
  ];

  const handleAddActivity = (activity: any) => {
    console.log("New activity added:", activity);
    // Here you would typically save the activity to your backend
    toast.success("Activity added successfully");
  };

  const handleUploadMaterial = (material: any) => {
    console.log("New material uploaded:", material);
    // Process the uploaded material
    toast.success("Material uploaded successfully");
  };

  const handleAssignHomework = (homework: any) => {
    console.log("New homework assigned:", homework);
    // Process the homework assignment
    toast.success("Homework assigned successfully");
  };

  const handlePlanAssessment = (assessment: any) => {
    console.log("New assessment planned:", assessment);
    // Process the assessment plan
    toast.success("Assessment planned successfully");
  };

  const handleLessonOutcomeSubmit = (outcomes: LessonOutcomes) => {
    setLessonOutcomes({
      ...lessonOutcomes,
      [outcomes.lessonId]: outcomes,
    });
    toast.success(t("lessonOutcomes.savedSuccess"));
  };

  const enhancedLessonCards = lessonCards.map((lesson, index) => ({
    ...lesson,
    id: `lesson-${index}`, // Add an ID for each lesson
    hasOutcomes: !!lessonOutcomes[`lesson-${index}`],
    outcomeButton: (
      <LessonOutcomeDialog
        trigger={
          <button className="text-indigo-600 hover:text-indigo-800 flex items-center gap-1 text-sm">
            <FileCheck size={16} />
            {lessonOutcomes[`lesson-${index}`]
              ? t("lessonOutcomes.editOutcomes")
              : t("lessonOutcomes.addOutcomes")}
          </button>
        }
        lessonId={`lesson-${index}`}
        lessonTitle={lesson.title}
        onSubmit={handleLessonOutcomeSubmit}
        initialData={lessonOutcomes[`lesson-${index}`]}
      />
    ),
  }));

  const actionButtons = (
    <div className="space-x-2 flex flex-wrap gap-2">
      <ActivityDialog
        trigger={
          <button className="bg-indigo-600 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center gap-2 text-sm sm:text-base">
            <FilePlus size={16} /> {t("curriculum.addActivity")}
          </button>
        }
        onSubmit={handleAddActivity}
      />
      <MaterialUploadDialog
        trigger={
          <button className="bg-green-600 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center gap-2 text-sm sm:text-base">
            <Upload size={16} /> {t("curriculum.uploadMaterial")}
          </button>
        }
        onSubmit={handleUploadMaterial}
      />
      <HomeworkAssignmentDialog
        trigger={
          <button className="bg-orange-500 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center gap-2 text-sm sm:text-base">
            <ClipboardList size={16} /> {t("curriculum.assignHomework")}
          </button>
        }
        onSubmit={handleAssignHomework}
      />
      <AssessmentPlanDialog
        trigger={
          <button className="bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg flex items-center gap-2 text-sm sm:text-base">
            <ChartGantt size={16} /> {t("curriculum.planAssessment")}
          </button>
        }
        onSubmit={handlePlanAssessment}
      />
    </div>
  );

  return (
    <CurriculumView
      title={t("curriculum.planning")}
      actionButtons={actionButtons}
      schedule={schedule}
      lessonCards={enhancedLessonCards}
      onLessonEdit={(lesson) => console.log("Edit lesson:", lesson)}
    />
  );
}
