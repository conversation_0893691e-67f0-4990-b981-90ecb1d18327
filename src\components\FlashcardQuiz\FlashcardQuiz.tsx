import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import { Shuffle, CheckCircle2 } from "lucide-react";
import confetti from "canvas-confetti";

interface FlashcardProps {
  question: string;
  answer: string;
}

export default function FlashcardQuiz({
  cards: initialCards,
}: {
  cards: FlashcardProps[];
}) {
  const { t } = useTranslation();
  const [cards, setCards] = useState<FlashcardProps[]>(initialCards);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [flipped, setFlipped] = useState(false);
  const [completed, setCompleted] = useState<number[]>([]);
  const [showCompletion, setShowCompletion] = useState(false);
  const [cardHeight, setCardHeight] = useState(256); // Default height (64 * 4)

  // Adjust card height based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        // Mobile
        setCardHeight(200);
      } else if (window.innerWidth < 1024) {
        // Tablet
        setCardHeight(224);
      } else {
        // Desktop
        setCardHeight(256);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial sizing

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    if (completed.length === cards.length && cards.length > 0) {
      setShowCompletion(true);

      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
      });
    } else {
      setShowCompletion(false);
    }
  }, [completed, cards.length]);

  useEffect(() => {
    setFlipped(false);
  }, [currentIndex]);

  const handleFlip = () => setFlipped(!flipped);

  const handleNext = () => {
    if (currentIndex < cards.length - 1) {
      setFlipped(false);

      setTimeout(() => {
        setCurrentIndex(currentIndex + 1);
      }, 100);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setFlipped(false);

      setTimeout(() => {
        setCurrentIndex(currentIndex - 1);
      }, 100);
    }
  };

  const handleMarkCompleted = () => {
    if (!completed.includes(currentIndex)) {
      setCompleted([...completed, currentIndex]);
    } else {
      setCompleted(completed.filter((idx) => idx !== currentIndex));
    }
  };

  const handleShuffle = () => {
    const shuffled = [...cards].sort(() => Math.random() - 0.5);
    setCards(shuffled);
    setCurrentIndex(0);
    setFlipped(false);

    const newCompleted = completed.map((oldIndex) => {
      const card = initialCards[oldIndex];
      const newIndex = shuffled.findIndex(
        (c) => c.question === card.question && c.answer === card.answer
      );
      return newIndex >= 0 ? newIndex : oldIndex;
    });

    setCompleted(newCompleted);
  };

  return (
    <div className="border border-gray-300 rounded-lg p-4 sm:p-5 md:p-6 bg-white shadow-md w-full max-w-full">
      {showCompletion ? (
        <div className="flex flex-col items-center justify-center py-4 sm:py-6 md:py-8">
          <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-green-100 flex items-center justify-center mb-3 sm:mb-4">
            <CheckCircle2 className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
          </div>
          <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">
            {t("flashcards.completionTitle")}
          </h3>
          <p className="text-gray-600 text-center text-sm sm:text-base mb-4 sm:mb-6 px-2">
            {t("flashcards.completionMessage")}
          </p>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={() => {
                setCompleted([]);
                setCurrentIndex(0);
                setFlipped(false);
              }}
              className="border-purple-200 text-purple-700 hover:bg-purple-50 w-full sm:w-auto"
              size="sm"
            >
              {t("flashcards.restart")}
            </Button>
            <Button
              onClick={handleShuffle}
              className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto"
              size="sm"
            >
              <Shuffle className="w-4 h-4 mr-2" />
              {t("flashcards.shuffleAndRestart")}
            </Button>
          </div>
        </div>
      ) : (
        <>
          <div className="flex justify-between items-center mb-3 sm:mb-4">
            <h2 className="text-base sm:text-lg md:text-xl font-bold text-purple-700">
              {t("flashcards.title")}
            </h2>
            <div className="text-xs sm:text-sm bg-purple-100 text-purple-700 px-2 sm:px-3 py-0.5 sm:py-1 rounded-full font-medium">
              {currentIndex + 1} / {cards.length}
            </div>
          </div>

          <div
            className={`relative perspective-1000 mb-3 sm:mb-4`}
            style={{ height: `${cardHeight}px` }}
          >
            {/* Question side */}
            <div
              className={`absolute w-full h-full transition-all duration-300 transform-style-preserve-3d ${
                flipped ? "rotate-y-180 pointer-events-none" : ""
              } backface-hidden`}
              onClick={() => !flipped && handleFlip()}
            >
              <div className="w-full h-full flex items-center justify-center p-3 sm:p-4 md:p-6 rounded-lg border-2 border-purple-200 bg-white hover:shadow-lg cursor-pointer">
                <div className="text-center">
                  <p className="text-base sm:text-lg font-medium text-gray-800">
                    {cards[currentIndex].question}
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mt-2">
                    {t("flashcards.clickToSeeAnswer")}
                  </p>
                </div>
              </div>
            </div>

            {/* Answer side */}
            <div
              className={`absolute w-full h-full transition-all duration-300 transform-style-preserve-3d ${
                flipped ? "" : "rotate-y-180 pointer-events-none"
              } backface-hidden`}
              onClick={() => flipped && handleFlip()}
            >
              <div className="w-full h-full flex items-center justify-center p-3 sm:p-4 md:p-6 rounded-lg border-2 border-purple-400 bg-purple-50 hover:shadow-lg cursor-pointer">
                <div className="text-center">
                  <p className="text-base sm:text-lg font-medium text-purple-700">
                    {cards[currentIndex].answer}
                  </p>
                  <p className="text-xs sm:text-sm text-gray-500 mt-2">
                    {t("flashcards.clickToSeeQuestion")}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-between gap-2 sm:gap-0">
            <div>
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentIndex === 0}
                className="border-purple-200 text-purple-700 hover:bg-purple-50 text-xs sm:text-sm w-full sm:w-auto"
                size="sm"
              >
                {t("flashcards.previous")}
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 sm:gap-2">
              <Button
                variant="outline"
                className={`text-xs sm:text-sm ${
                  completed.includes(currentIndex)
                    ? "bg-green-100 text-green-700 border-green-200"
                    : "border-purple-200 text-purple-700 hover:bg-purple-50"
                } w-full sm:w-auto`}
                onClick={handleMarkCompleted}
                size="sm"
              >
                {completed.includes(currentIndex)
                  ? t("flashcards.marked")
                  : t("flashcards.markAsKnown")}
              </Button>

              <Button
                className="bg-purple-600 hover:bg-purple-700 text-xs sm:text-sm w-full sm:w-auto"
                onClick={handleNext}
                disabled={currentIndex === cards.length - 1}
                size="sm"
              >
                {t("flashcards.next")}
              </Button>
            </div>
          </div>

          <div className="mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-gray-200">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0">
              <div className="text-xs sm:text-sm text-gray-600 w-full sm:w-auto">
                <span>{t("flashcards.progress")}: </span>
                <span className="font-medium text-purple-700">
                  {completed.length}/{cards.length}
                </span>
                <div className="w-full sm:w-32 md:w-48 bg-gray-200 rounded-full h-1.5 sm:h-2 mt-1">
                  <div
                    className="bg-purple-600 h-1.5 sm:h-2 rounded-full"
                    style={{
                      width: `${(completed.length / cards.length) * 100}%`,
                    }}
                  ></div>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-1 text-purple-700 hover:bg-purple-50 text-xs sm:text-sm p-1 sm:p-2 h-auto w-full sm:w-auto justify-center sm:justify-start"
                onClick={handleShuffle}
              >
                <Shuffle className="h-3 w-3 sm:h-4 sm:w-4" />
                {t("flashcards.shuffle")}
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
