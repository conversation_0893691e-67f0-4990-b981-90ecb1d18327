import { useState, useEffect } from "react";
import { NavLink } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { SlGraduation } from "react-icons/sl";
import { Menu, X } from "lucide-react";
import { useTypedAuthUser } from "../../hooks/useAuth";

export default function PublicHeader() {
  const { t } = useTranslation();
  const user = useTypedAuthUser();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleGetStarted = () => {
    if (user) {
      window.open("/dashboard", "_blank");
    } else {
      window.open("/login", "_blank");
    }
  };

  return (
    <header className="bg-gradient-to-r from-purple-600 to-purple-800 text-white  sticky top-0 z-20 ">
      <div className="container mx-auto px-6 md:px-10 lg:px-20 py-4 flex justify-between items-center">
        <NavLink to="/" className="flex items-center">
          <SlGraduation className="text-white-600 text-2xl mr-2" />
          <span className="font-bold text-xl text-white-700">E-ditar</span>
        </NavLink>

        {/* Desktop Navigation */}
        <nav className={`${isMobile ? "hidden" : "flex"} items-center gap-6`}>
          <NavLink
            to="/"
            className={({ isActive }) =>
              `text-sm font-medium ${
                isActive
                  ? "text-white font-bold"
                  : "text-white hover:text-gray-200"
              }`
            }
          >
            {t("welcomePage.home")}
          </NavLink>
          <NavLink
            to="/features"
            className={({ isActive }) =>
              `text-sm font-medium ${
                isActive
                  ? "text-white font-bold"
                  : "text-white hover:text-gray-200"
              }`
            }
          >
            {t("welcomePage.featuresTitle")}
          </NavLink>
          <NavLink
            to="/pricing"
            className={({ isActive }) =>
              `text-sm font-medium ${
                isActive
                  ? "text-white font-bold"
                  : "text-white hover:text-gray-200"
              }`
            }
          >
            {t("welcomePage.pricing")}
          </NavLink>
          <NavLink
            to="/about"
            className={({ isActive }) =>
              `text-sm font-medium ${
                isActive
                  ? "text-white font-bold"
                  : "text-white hover:text-gray-200"
              }`
            }
          >
            {t("welcomePage.aboutUs")}
          </NavLink>
          <NavLink
            to="/contact"
            className={({ isActive }) =>
              `text-sm font-medium ${
                isActive
                  ? "text-white font-bold"
                  : "text-white hover:text-gray-200"
              }`
            }
          >
            {t("welcomePage.contactUs")}
          </NavLink>
        </nav>

        <div className={`${isMobile ? "hidden" : "flex"} items-center gap-4`}>
          <button
            onClick={handleGetStarted}
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
          >
            {t("welcomePage.login")}
          </button>
        </div>

        {/* Mobile Menu Button */}
        {isMobile && (
          <button onClick={toggleMobileMenu} className="p-2">
            {isMobileMenuOpen ? (
              <X className="w-6 h-6 text-white" />
            ) : (
              <Menu className="w-6 h-6 text-white" />
            )}
          </button>
        )}
      </div>

      {/* Mobile Menu */}
      {isMobile && isMobileMenuOpen && (
        <div className="bg-white  border-t border-gray-200 py-4 px-4">
          <nav className="flex flex-col gap-4">
            <NavLink
              to="/"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive ? "text-purple-700" : "text-gray-600"
                }`
              }
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t("welcomePage.home")}
            </NavLink>
            <NavLink
              to="/features"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive ? "text-purple-700" : "text-gray-600"
                }`
              }
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t("welcomePage.featuresTitle")}
            </NavLink>
            <NavLink
              to="/pricing"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive ? "text-purple-700" : "text-gray-600"
                }`
              }
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t("welcomePage.pricing")}
            </NavLink>
            <NavLink
              to="/about"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive ? "text-purple-700" : "text-gray-600"
                }`
              }
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t("welcomePage.aboutUs")}
            </NavLink>
            <NavLink
              to="/contact"
              className={({ isActive }) =>
                `text-sm font-medium ${
                  isActive ? "text-purple-700" : "text-gray-600"
                }`
              }
              onClick={() => setIsMobileMenuOpen(false)}
            >
              {t("welcomePage.contactUs")}
            </NavLink>
            <div className="pt-2 border-t border-gray-200 mt-2">
              {user ? (
                <button
                  onClick={handleGetStarted}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium w-full"
                >
                  {t("welcomePage.dashboard")}
                </button>
              ) : (
                <div className="flex flex-col gap-3">
                  <button
                    onClick={handleGetStarted}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium"
                  >
                    {t("welcomePage.login")}
                  </button>
                </div>
              )}
            </div>
          </nav>
        </div>
      )}
    </header>
  );
}
