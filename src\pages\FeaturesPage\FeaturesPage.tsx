import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import Footer from "@/components/Footer";
import { CheckCircle, ChartNoAxesColumnIncreasing } from "lucide-react";
import KeyFeatures from "@/components/KeyFeaturesCard";
import { useState, useEffect } from "react";
import PublicHeader from "@/components/PublicHeader";

export default function FeaturesPage() {
  const { t } = useTranslation();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [isTablet, setIsTablet] = useState(
    window.innerWidth >= 640 && window.innerWidth < 1024
  );

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 1024);
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial sizing
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const roleFeatures = {
    students: t("featuresPage.roleFeatures.students", { returnObjects: true }),
    teachers: t("featuresPage.roleFeatures.teachers", { returnObjects: true }),
    parents: t("featuresPage.roleFeatures.parents", { returnObjects: true }),
    directors: t("featuresPage.roleFeatures.directors", {
      returnObjects: true,
    }),
  };

  return (
    <>
      <PublicHeader />

      <section className="bg-purple-600 text-white py-16 md:py-24 cursor-default">
        <div className="container mx-auto px-4 md:px-8 text-center">
          <h1
            className={`${
              isMobile ? "text-2xl" : isTablet ? "text-3xl" : "text-5xl"
            } font-bold mb-4`}
          >
            {t("featuresPage.title")}
          </h1>
          <p
            className={`max-w-2xl mx-auto ${
              isMobile ? "text-base" : "text-lg md:text-xl"
            }`}
          >
            {t("featuresPage.subtitle")}
          </p>
        </div>
      </section>

      <KeyFeatures />

      {/* Feature Spotlight */}
      <section className={`py-${isMobile ? "10" : "16"} cursor-default`}>
        <div className="container mx-auto px-4 md:px-8 lg:px-20">
          <h2
            className={`${
              isMobile ? "text-xl" : isTablet ? "text-2xl" : "text-3xl"
            } font-bold text-center mb-${isMobile ? "8" : "12"}`}
          >
            {t("featuresPage.featureSpotlight")}
          </h2>

          <div className="bg-white rounded-lg shadow-md  overflow-hidden">
            <div className="flex flex-col  md:flex-row">
              <div
                className={`${isMobile ? "p-4" : "p-8"} ${
                  !isMobile && !isTablet ? "md:w-2/5" : ""
                } bg-purple-50 flex items-center justify-center`}
              >
                <div className="w-full max-w-xs flex items-center justify-center">
                  <ChartNoAxesColumnIncreasing
                    className={`${
                      isMobile ? "h-16 w-16" : "h-24 w-24"
                    } text-purple-600`}
                  />
                </div>
              </div>
              <div
                className={`${!isMobile && !isTablet ? "md:w-3/5" : ""} p-${
                  isMobile ? "4" : "8"
                }`}
              >
                <h3
                  className={`${
                    isMobile ? "text-lg" : isTablet ? "text-xl" : "text-2xl"
                  } font-bold mb-${isMobile ? "2" : "4"}`}
                >
                  {t("featuresPage.analyticsDashboard.title")}
                </h3>
                <p className="text-gray-600 mb-6">
                  {t("featuresPage.analyticsDashboard.description")}
                </p>
                <ul className="space-y-3">
                  {[
                    "Interactive data visualizations",
                    "Customizable reports and exports",
                    "Performance trend analysis",
                    "Actionable insights and recommendations",
                  ].map((item, i) => (
                    <li key={i} className="flex items-start">
                      <CheckCircle className="text-purple-500 mr-2 h-5 w-5 mt-0.5" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                <button className="mt-6 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition">
                  {t("featuresPage.learnMore")} →
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Role-based Features */}
      <section
        className={`py-${isMobile ? "10" : "16"} bg-gray-50 cursor-default`}
      >
        <div className="container mx-auto px-4 md:px-8">
          <h2
            className={`${
              isMobile ? "text-xl" : isTablet ? "text-2xl" : "text-3xl"
            } font-bold text-center mb-4`}
          >
            {t("featuresPage.roleFeatures.title")}
          </h2>
          <p
            className={`text-center text-gray-600 mb-${
              isMobile ? "8" : "12"
            } max-w-2xl mx-auto ${isMobile ? "text-sm" : ""}`}
          >
            {t("featuresPage.roleFeatures.subtitle")}
          </p>

          <div className={`space-y-${isMobile ? "8" : isTablet ? "12" : "16"}`}>
            {Object.entries({
              [t("featuresPage.roleFeatures.forStudents")]:
                roleFeatures.students,
              [t("featuresPage.roleFeatures.forTeachers")]:
                roleFeatures.teachers,
              [t("featuresPage.roleFeatures.forParents")]: roleFeatures.parents,
              [t("featuresPage.roleFeatures.forSchoolDirectors")]:
                roleFeatures.directors,
            }).map(([role, features], index) => (
              <motion.div
                key={role}
                className={`bg-white rounded-lg shadow-md mb-10 p-${
                  isMobile ? "4" : isTablet ? "6" : "8"
                }`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="flex flex-col md:flex-row items-start">
                  <div className="md:w-1/3 mb-6 md:mb-0 pr-0 md:pr-8">
                    <h3 className="text-2xl font-bold mb-3 text-purple-600">
                      {role}
                    </h3>
                    <p className="text-gray-600">
                      Tailored features designed specifically for to enhance
                      their educational experience.
                    </p>
                  </div>
                  <div className="md:w-2/3 grid grid-cols-1 md:grid-cols-2 gap-6">
                    {(features as string[]).map(
                      (feature: string, i: number) => (
                        <div
                          key={i}
                          className="flex items-start bg-gray-50 p-4 rounded-lg"
                        >
                          <CheckCircle className="text-purple-500 mr-3 h-5 w-5 mt-0.5 flex-shrink-0" />
                          <span className="font-medium">{feature}</span>
                        </div>
                      )
                    )}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-purple-600 text-white py-12 cursor-default">
        <div className="container mx-auto px-4 md:px-8 lg:px-20 text-center">
          <h2
            className={`${
              isMobile ? "text-xl" : isTablet ? "text-2xl" : "text-3xl"
            } font-bold mb-4`}
          >
            {t("featuresPage.ctaTitle")}
          </h2>
          <div
            className={`flex ${
              isMobile ? "flex-col" : "justify-center"
            } gap-4 mt-6`}
          >
            <button className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-md transition">
              {t("featuresPage.startToday")}
            </button>
            <button className="bg-transparent border border-white hover:bg-white hover:text-purple-600 text-white px-6 py-3 rounded-md transition">
              {t("featuresPage.contactSales")}
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </>
  );
}
