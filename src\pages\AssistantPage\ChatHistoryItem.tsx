import React from "react";
import { Trash2 } from "lucide-react";
import { motion } from "framer-motion";
import { ChatHistory } from "./types";

interface ChatHistoryItemProps {
  chat: ChatHistory;
  isActive: boolean;
  onClick: () => void;
  onDelete: () => void;
}

export const ChatHistoryItem: React.FC<ChatHistoryItemProps> = ({
  chat,
  isActive,
  onClick,
  onDelete,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className={`flex items-center px-3 py-2 rounded-lg cursor-pointer group ${
        isActive
          ? "bg-purple-100 dark:bg-purple-900/20"
          : "hover:bg-gray-100 dark:hover:bg-gray-800/50"
      }`}
      onClick={onClick}
    >
      <div className="flex-1 truncate">
        <p className="text-sm font-medium truncate">{chat.title}</p>
      </div>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDelete();
        }}
        className="opacity-0 group-hover:opacity-100 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-opacity"
      >
        <Trash2 size={16} className="text-gray-500" />
      </button>
    </motion.div>
  );
};
