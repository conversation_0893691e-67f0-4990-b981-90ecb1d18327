import { useState } from "react";
import { IoMailOutline } from "react-icons/io5";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import { validateEmail } from "../../lib/validation";

export default function ForgotPassword() {
  const { t } = useTranslation();

  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleChange = (e: any) => {
    setEmail(e.target.value);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();

    if (!validateEmail(email)) {
      setError(t("validation.invalidEmail"));
      toast.error(t("validation.invalidEmail"));
      return;
    }

    setError(null);
    // Handle the password reset logic here (e.g., API call)
    console.log("Sending reset link to:", email);
    toast.success(t("resetLinkSent"));
    setEmail("");
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center px-4 relative overflow-hidden cursor-default"
      style={{
        background:
          "linear-gradient(-45deg, #7B3AED, #9B87F5, #D8B4FE, #F3E8FF)",
        backgroundSize: "400% 400%",
        animation: "gradient 15s ease infinite",
      }}
    >
      <div className="bg-white rounded-2xl shadow-lg p-6 sm:p-8 md:p-10 max-w-md w-full transition-all">
        <h3 className="text-xl sm:text-2xl font-semibold text-purple-700 mb-1 sm:mb-2">
          {t("resetPassword")}
        </h3>
        <p className="text-xs sm:text-sm mb-4 sm:mb-6 text-gray-500">
          {t("resetDesc")}
        </p>
        <form onSubmit={handleSubmit} className="space-y-3 sm:space-y-4">
          <div>
            <div>
              <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                {t("email")}
              </label>
              <div className="flex items-center  w-full px-3 py-1.5 sm:px-4 sm:py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500 outline-none text-xs sm:text-sm">
                <IoMailOutline className=" text-gray-400 w-4 sm:w-5 h-4 sm:h-5 mr-2" />
                <input
                  id="email"
                  type="email"
                  name="email"
                  value={email}
                  onChange={handleChange}
                  className="w-full focus:outline-none "
                  placeholder="<EMAIL>"
                  required
                />
              </div>
            </div>
            {error && (
              <p className="text-red-500 text-xs sm:text-sm mt-1 sm:mt-2">
                {error}
              </p>
            )}
          </div>

          <button
            type="submit"
            className="w-full bg-purple-600 text-white py-1.5 sm:py-2 rounded-lg hover:bg-purple-700 transition font-medium text-xs sm:text-sm"
          >
            {t("sendResetLink")}
          </button>
        </form>

        <div className="flex justify-center text-xs sm:text-sm mt-3 sm:mt-4">
          <p className="text-gray-500">
            {t("rememberpassword")}{" "}
            <a href="/login" className="text-purple-600 hover:underline">
              {t("backToLogin")}
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
