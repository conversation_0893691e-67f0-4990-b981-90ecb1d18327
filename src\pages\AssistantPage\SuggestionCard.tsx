import React from "react";

interface SuggestionCardProps {
  icon: React.ReactNode;
  title: string;
  onClick: () => void;
}

export const SuggestionCard: React.FC<SuggestionCardProps> = ({
  icon,
  title,
  onClick,
}) => {
  return (
    <button
      className="flex items-start gap-3 p-4 text-left border rounded-lg hover:bg-gray-50 transition-colors"
      onClick={onClick}
    >
      <div className="text-purple-600 mt-0.5">{icon}</div>
      <span className="text-sm text-gray-700">{title}</span>
    </button>
  );
};
