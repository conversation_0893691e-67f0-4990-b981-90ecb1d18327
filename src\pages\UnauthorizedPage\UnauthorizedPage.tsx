import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import useSignOut from "react-auth-kit/hooks/useSignOut";

export default function UnauthorizedPage() {
  const navigate = useNavigate();
  const signOut = useSignOut();
  const { t } = useTranslation();

  const handleGoToLogin = () => {
    signOut();
    navigate("/login");
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50">
      <div className="flex flex-col justify-center items-center max-w-md mx-auto p-8">
        <div className="text-6xl mb-6">🚫</div>
        <h1 className="font-bold text-3xl text-center text-gray-800 mb-4">
          {t("unauthorized") || "Unauthorized Access"}
        </h1>
        <p className="text-gray-600 text-center mb-6 leading-relaxed">
          {t("unauthorizedMessage") ||
            "You don't have permission to access this page. Please contact your administrator if you believe this is an error."}
        </p>
        <div className="flex flex-col sm:flex-row gap-3 w-full">
          <button
            className="flex-1 bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition font-medium"
            onClick={handleGoToLogin}
          >
            {t("goToLogin") || "Go to Login"}
          </button>
          <button
            className="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition font-medium"
            onClick={handleGoBack}
          >
            {t("goBack") || "Go Back"}
          </button>
        </div>
      </div>
    </div>
  );
}
