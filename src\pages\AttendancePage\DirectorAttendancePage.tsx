import React from "react";
import { Search } from "lucide-react";
import WarningManagement from "../../components/WarningManagement";
import { useTranslation } from "react-i18next";

export default function DirectorAttendancePage() {
  const [searchTerm, setSearchTerm] = React.useState("");
  const { t } = useTranslation();

  return (
    <div className="p-4">
      <h1 className="text-2xl font-bold mb-4">Director Attendance Page</h1>

      {/* Filter Controls */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search by subject..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="border rounded-md px-4 py-2 w-full pl-10"
          />
        </div>
        <div className="flex flex-wrap gap-2">
          <button className="bg-blue-500 text-white px-4 py-2 rounded mr-2">
            Today
          </button>
          <button className="bg-blue-500 text-white px-4 py-2 rounded mr-2">
            This Week
          </button>
          <button className="bg-blue-500 text-white px-4 py-2 rounded mr-2">
            This Month
          </button>
          <button className="bg-blue-500 text-white px-4 py-2 rounded mr-2">
            This Year
          </button>
        </div>
      </div>

      {/* Attendance Table */}
      <table className="w-full border-collapse">
        <thead className="bg-gray-100">
          <tr>
            <th className="p-4 border-b">Student Name</th>
            <th className="p-4 border-b">Subject</th>
            <th className="p-4 border-b">Date</th>
            <th className="p-4 border-b">Status</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="p-4 border-b">John Doe</td>
            <td className="p-4 border-b">Mathematics</td>
            <td className="p-4 border-b">2023-10-01</td>
            <td className="p-4 border-b">Present</td>
          </tr>
          <tr>
            <td className="p-4 border-b">Jane Smith</td>
            <td className="p-4 border-b">Science</td>
            <td className="p-4 border-b">2023-10-02</td>
            <td className="p-4 border-b">Absent</td>
          </tr>
          <tr>
            <td className="p-4 border-b">Emily Johnson</td>
            <td className="p-4 border-b">History</td>
            <td className="p-4 border-b">2023-10-03</td>
            <td className="p-4 border-b">Present</td>
          </tr>
        </tbody>
      </table>

      <WarningManagement
        readOnly={false}
        title={t("warnings.schoolWarnings") || "School Warnings"}
      />
    </div>
  );
}
