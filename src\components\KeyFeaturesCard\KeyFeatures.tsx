import {
  LayoutDashboard,
  Calendar,
  Award,
  CircleCheckBig,
  Users,
  Lightbulb,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";

export default function KeyFeatures({ title, description }: any) {
  const { t } = useTranslation();

  const keyFeatures = [
    {
      icon: <LayoutDashboard />,
      title: t("welcomePage.features.dashboards.title"),
      description: t("welcomePage.features.dashboards.description"),
      link: "#",
      iconColor: "text-blue-600",
      iconBg: "bg-blue-100",
    },
    {
      icon: <Calendar />,
      title: t("welcomePage.features.timetable.title"),
      description: t("welcomePage.features.timetable.description"),
      link: "#",
      iconColor: "text-green-600",
      iconBg: "bg-green-100",
    },
    {
      icon: <Award />,
      title: t("welcomePage.features.grades.title"),
      description: t("welcomePage.features.grades.description"),
      link: "#",
      iconColor: "text-yellow-600",
      iconBg: "bg-yellow-100",
    },
    {
      icon: <CircleCheckBig />,
      title: t("welcomePage.features.attendance.title"),
      description: t("welcomePage.features.attendance.description"),
      link: "#",
      iconColor: "text-purple-600",
      iconBg: "bg-purple-100",
    },
    {
      icon: <Users />,
      title: t("welcomePage.features.communication.title"),
      description: t("welcomePage.features.communication.description"),
      link: "#",
      iconColor: "text-pink-600",
      iconBg: "bg-pink-100",
    },
    {
      icon: <Lightbulb />,
      title: t("welcomePage.features.ai.title"),
      description: t("welcomePage.features.ai.description"),
      link: "#",
      iconColor: "text-red-600",
      iconBg: "bg-red-100",
    },
  ];

  return (
    <section className="bg-gray-50 py-12 md:py-16 lg:py-20 cursor-default">
      <div className="container mx-auto px-4 md:px-8 lg:px-20">
        <div className="text-center max-w-2xl mx-auto pb-8 lg:pb-10">
          <h2 className="text-2xl md:text-3xl font-bold pb-4">{title}</h2>
          <p className="text-base md:text-lg mb-6 text-gray-500">
            {description}
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {keyFeatures.map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white p-5 md:p-6 rounded-lg shadow-md hover:shadow-lg transition"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{
                duration: 0.5,
                ease: "easeOut",
                delay: index * 0.2,
              }}
            >
              <div
                className={`text-2xl md:text-3xl mb-4 p-3 rounded-md w-fit ${feature.iconColor} ${feature.iconBg}`}
              >
                {feature.icon}
              </div>
              <h3 className="text-lg md:text-xl font-semibold mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm md:text-base">
                {feature.description}
              </p>
              <a
                href={feature.link}
                className={`font-medium mt-3 inline-block text-sm md:text-base ${feature.iconColor}`}
              >
                Learn more →
              </a>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
