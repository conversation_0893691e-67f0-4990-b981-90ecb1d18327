import useAuthUser from "react-auth-kit/hooks/useAuthUser";
import useIsAuthenticated from "react-auth-kit/hooks/useIsAuthenticated";
import useSignIn from "react-auth-kit/hooks/useSignIn";
import useSignOut from "react-auth-kit/hooks/useSignOut";
import { User } from "../types";

// Typed version of useAuthUser hook
export const useTypedAuthUser = (): User | null => {
  return useAuthUser<User>();
};

// Re-export other hooks for consistency
export { useIsAuthenticated, useSignIn, useSignOut };
