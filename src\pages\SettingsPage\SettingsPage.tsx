import { useTranslation } from "react-i18next";
import { useTypedAuthUser } from "../../hooks/useAuth";
import { Bell, Globe, Lock, Moon } from "lucide-react";
import { useState, useEffect } from "react";
import toast from "react-hot-toast";
import {
  validateEmail,
  validatePhone,
  validateRequired,
  validatePassword,
} from "../../lib/validation";

export default function SettingsPage() {
  const user = useTypedAuthUser();
  const { t } = useTranslation();
  const [darkMode, setDarkMode] = useState(false);
  const [language, setLanguage] = useState("en");
  const [notifications, setNotifications] = useState({
    email: true,
    app: true,
    updates: false,
  });

  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [isTablet, setIsTablet] = useState(
    window.innerWidth >= 640 && window.innerWidth < 1024
  );

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 1024);
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial sizing
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const [formData, setFormData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    phone: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [validationErrors, setValidationErrors] = useState({
    name: false,
    email: false,
    phone: false,
    currentPassword: false,
    newPassword: false,
    confirmPassword: false,
    passwordMatch: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    if (value.trim() !== "") {
      setValidationErrors({
        ...validationErrors,
        [name]: false,
      });

      if (name === "newPassword" || name === "confirmPassword") {
        setValidationErrors({
          ...validationErrors,
          passwordMatch: false,
        });
      }
    }
  };

  const validateProfileForm = () => {
    let isValid = true;
    const errors = { ...validationErrors };

    if (!validateRequired(formData.name)) {
      errors.name = true;
      isValid = false;
    }

    if (!validateEmail(formData.email)) {
      errors.email = true;
      isValid = false;
    }

    if (formData.phone && !validatePhone(formData.phone)) {
      errors.phone = true;
      isValid = false;
    }

    setValidationErrors(errors);
    return isValid;
  };

  const validatePasswordForm = () => {
    let isValid = true;
    const errors = { ...validationErrors };

    if (!validateRequired(formData.currentPassword)) {
      errors.currentPassword = true;
      isValid = false;
    }

    if (!validatePassword(formData.newPassword)) {
      errors.newPassword = true;
      isValid = false;
    }

    if (!validateRequired(formData.confirmPassword)) {
      errors.confirmPassword = true;
      isValid = false;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      errors.passwordMatch = true;
      isValid = false;
    }

    setValidationErrors(errors);
    return isValid;
  };

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateProfileForm()) {
      toast.success(t("settings.profileUpdated"));
    } else {
      toast.error(t("settings.validationError"));
    }
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validatePasswordForm()) {
      toast.success(t("settings.passwordUpdated"));

      setFormData({
        ...formData,
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } else {
      toast.error(t("settings.validationError"));
    }
  };

  return (
    <div
      className={`p-3 sm:p-4 md:p-1 max-w-full ${
        isMobile ? "space-y-4" : "space-y-6"
      }`}
    >
      <h1
        className={`${
          isMobile ? "text-2xl" : isTablet ? "text-2xl" : "text-3xl"
        } font-bold mb-2 sm:mb-4 md:mb-6`}
      >
        {t("sidebar.settings")}
      </h1>

      <form
        onSubmit={handleProfileSubmit}
        className={`bg-white ${
          isMobile ? "p-4" : isTablet ? "p-5" : "p-6"
        } rounded-lg shadow-md border border-gray-300`}
      >
        <h2
          className={`${isMobile ? "text-xl" : "text-2xl"} font-semibold mb-1`}
        >
          {t("settings.profileInformation")}
        </h2>
        <p className={`text-gray-500 ${isMobile ? "mb-4 text-sm" : "mb-6"}`}>
          {t("settings.UpdateInformation")}
        </p>

        <div
          className={`grid grid-cols-1 ${
            isTablet || !isMobile ? "md:grid-cols-2" : ""
          } gap-4 mb-4 sm:mb-6`}
        >
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("fullName")}*
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`w-full px-3 sm:px-4 py-2 border ${
                validationErrors.name
                  ? "border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:ring-purple-400"
              } rounded-lg focus:ring-2 outline-none transition`}
            />
            {validationErrors.name && (
              <p className="text-red-500 text-xs mt-1">
                {t("settings.nameRequired")}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("email")}*
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-3 sm:px-4 py-2 border ${
                validationErrors.email
                  ? "border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:ring-purple-400"
              } rounded-lg focus:ring-2 outline-none transition`}
            />
            {validationErrors.email && (
              <p className="text-red-500 text-xs mt-1">
                {t("validation.invalidEmail")}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("Role")}
            </label>
            <input
              className="w-full px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 outline-none transition bg-gray-100"
              type="text"
              value={user?.role}
              disabled
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("settings.phoneNumber")}
            </label>
            <input
              type="tel"
              name="phone"
              placeholder="+****************"
              value={formData.phone}
              onChange={handleInputChange}
              className={`w-full px-3 sm:px-4 py-2 border ${
                validationErrors.phone
                  ? "border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:ring-purple-400"
              } rounded-lg focus:ring-2 outline-none transition`}
            />
            {validationErrors.phone && (
              <p className="text-red-500 text-xs mt-1">
                {t("validation.invalidPhone")}
              </p>
            )}
          </div>
        </div>

        <button
          type="submit"
          className={`px-3 sm:px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition ${
            isMobile ? "text-sm" : ""
          }`}
        >
          {t("saveChanges")}
        </button>
      </form>

      {/* Security Settings */}
      <form
        onSubmit={handlePasswordSubmit}
        className={`bg-white ${
          isMobile ? "p-4" : isTablet ? "p-5" : "p-6"
        } rounded-lg shadow-md border border-gray-300`}
      >
        <div className="flex items-center mb-3 sm:mb-4">
          <Lock
            className={`${
              isMobile ? "w-4 h-4" : "w-5 h-5"
            } text-purple-600 mr-2`}
          />
          <h2 className={`${isMobile ? "text-xl" : "text-2xl"} font-semibold`}>
            {t("settings.security")}
          </h2>
        </div>
        <p className={`text-gray-500 ${isMobile ? "mb-4 text-sm" : "mb-6"}`}>
          {t("settings.securityDesc")}
        </p>

        <div className="space-y-3 sm:space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("settings.currentPassword")}*
            </label>
            <input
              type="password"
              name="currentPassword"
              value={formData.currentPassword}
              onChange={handleInputChange}
              className={`w-full px-3 sm:px-4 py-2 border ${
                validationErrors.currentPassword
                  ? "border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:ring-purple-400"
              } rounded-lg focus:ring-2 outline-none transition`}
            />
            {validationErrors.currentPassword && (
              <p className="text-red-500 text-xs mt-1">
                {t("settings.currentPasswordRequired")}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("settings.newPassword")}*
            </label>
            <input
              type="password"
              name="newPassword"
              value={formData.newPassword}
              onChange={handleInputChange}
              className={`w-full px-3 sm:px-4 py-2 border ${
                validationErrors.newPassword
                  ? "border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:ring-purple-400"
              } rounded-lg focus:ring-2 outline-none transition`}
            />
            {validationErrors.newPassword && (
              <p className="text-red-500 text-xs mt-1">
                {t("settings.passwordRequirements")}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t("settings.confirmPassword")}*
            </label>
            <input
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className={`w-full px-3 sm:px-4 py-2 border ${
                validationErrors.confirmPassword ||
                validationErrors.passwordMatch
                  ? "border-red-500 focus:ring-red-500"
                  : "border-gray-300 focus:ring-purple-400"
              } rounded-lg focus:ring-2 outline-none transition`}
            />
            {validationErrors.confirmPassword && (
              <p className="text-red-500 text-xs mt-1">
                {t("settings.confirmPasswordRequired")}
              </p>
            )}
            {validationErrors.passwordMatch && (
              <p className="text-red-500 text-xs mt-1">
                {t("settings.passwordsDoNotMatch")}
              </p>
            )}
          </div>
        </div>

        <button
          type="submit"
          className={`mt-3 sm:mt-4 px-3 sm:px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition ${
            isMobile ? "text-sm" : ""
          }`}
        >
          {t("settings.updatePassword")}
        </button>
      </form>

      {/* Preferences */}
      <div
        className={`bg-white ${
          isMobile ? "p-4" : isTablet ? "p-5" : "p-6"
        } rounded-lg shadow-md border border-gray-300`}
      >
        <div className="flex items-center mb-3 sm:mb-4">
          <Moon
            className={`${
              isMobile ? "w-4 h-4" : "w-5 h-5"
            } text-purple-600 mr-2`}
          />
          <h2 className={`${isMobile ? "text-xl" : "text-2xl"} font-semibold`}>
            {t("settings.preferences")}
          </h2>
        </div>

        <div className="space-y-4 sm:space-y-6">
          {/* Theme Toggle */}
          <div className="flex items-center justify-between">
            <div className={isMobile ? "max-w-[70%]" : ""}>
              <h3 className="font-medium">{t("settings.darkMode")}</h3>
              <p
                className={`${isMobile ? "text-xs" : "text-sm"} text-gray-500`}
              >
                {t("settings.darkModeDesc")}
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={darkMode}
                onChange={() => setDarkMode(!darkMode)}
                className="sr-only peer"
              />
              <div
                className={`${
                  isMobile ? "w-9 h-5" : "w-11 h-6"
                } bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 sm:after:h-5 after:w-4 sm:after:w-5 after:transition-all peer-checked:bg-purple-600`}
              ></div>
            </label>
          </div>

          {/* Language Selector */}
          <div>
            <div className="flex items-center mb-2">
              <Globe
                className={`${
                  isMobile ? "w-4 h-4" : "w-5 h-5"
                } text-purple-600 mr-2`}
              />
              <h3 className="font-medium">{t("settings.language")}</h3>
            </div>
            <select
              value={language}
              onChange={(e) => setLanguage(e.target.value)}
              className={`${
                isMobile ? "w-full" : isTablet ? "w-1/2" : "w-1/3"
              } px-3 sm:px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 outline-none transition`}
            >
              <option value="en">English</option>
              <option value="sq">Albanian</option>
              <option value="fr">Français</option>
              <option value="de">Deutsch</option>
            </select>
          </div>
        </div>
      </div>

      {/* Notifications */}
      <div
        className={`bg-white ${
          isMobile ? "p-4" : isTablet ? "p-5" : "p-6"
        } rounded-lg shadow-md border border-gray-300`}
      >
        <div className="flex items-center mb-3 sm:mb-4">
          <Bell
            className={`${
              isMobile ? "w-4 h-4" : "w-5 h-5"
            } text-purple-600 mr-2`}
          />
          <h2 className={`${isMobile ? "text-xl" : "text-2xl"} font-semibold`}>
            {t("settings.notifications")}
          </h2>
        </div>
        <p className={`text-gray-500 ${isMobile ? "mb-4 text-sm" : "mb-6"}`}>
          {t("settings.notificationsDesc")}
        </p>

        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center justify-between">
            <div className={isMobile ? "max-w-[70%]" : ""}>
              <h3 className="font-medium">
                {t("settings.emailNotifications")}
              </h3>
              <p
                className={`${isMobile ? "text-xs" : "text-sm"} text-gray-500`}
              >
                {t("settings.emailNotificationsDesc")}
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.email}
                onChange={() =>
                  setNotifications({
                    ...notifications,
                    email: !notifications.email,
                  })
                }
                className="sr-only peer"
              />
              <div
                className={`${
                  isMobile ? "w-9 h-5" : "w-11 h-6"
                } bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 sm:after:h-5 after:w-4 sm:after:w-5 after:transition-all peer-checked:bg-purple-600`}
              ></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div className={isMobile ? "max-w-[70%]" : ""}>
              <h3 className="font-medium">{t("settings.appNotifications")}</h3>
              <p
                className={`${isMobile ? "text-xs" : "text-sm"} text-gray-500`}
              >
                {t("settings.appNotificationsDesc")}
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.app}
                onChange={() =>
                  setNotifications({
                    ...notifications,
                    app: !notifications.app,
                  })
                }
                className="sr-only peer"
              />
              <div
                className={`${
                  isMobile ? "w-9 h-5" : "w-11 h-6"
                } bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 sm:after:h-5 after:w-4 sm:after:w-5 after:transition-all peer-checked:bg-purple-600`}
              ></div>
            </label>
          </div>

          <div className="flex items-center justify-between">
            <div className={isMobile ? "max-w-[70%]" : ""}>
              <h3 className="font-medium">{t("settings.systemUpdates")}</h3>
              <p
                className={`${isMobile ? "text-xs" : "text-sm"} text-gray-500`}
              >
                {t("settings.systemUpdatesDesc")}
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={notifications.updates}
                onChange={() =>
                  setNotifications({
                    ...notifications,
                    updates: !notifications.updates,
                  })
                }
                className="sr-only peer"
              />
              <div
                className={`${
                  isMobile ? "w-9 h-5" : "w-11 h-6"
                } bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 sm:after:h-5 after:w-4 sm:after:w-5 after:transition-all peer-checked:bg-purple-600`}
              ></div>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
