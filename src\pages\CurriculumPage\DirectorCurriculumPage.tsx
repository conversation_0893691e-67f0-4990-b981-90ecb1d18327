import { useTranslation } from "react-i18next";
import CurriculumView from "./CurriculumView";
import { Plus, Settings } from "lucide-react";

export default function DirectorCurriculumPage() {
  const { t } = useTranslation();

  const schedule = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"].map(
    (day, index) => ({
      day,
      date: `Apr ${21 + index}`,
      lessons: [
        {
          subject: "Physics: Newton's Laws",
          color: "bg-purple-100 text-purple-800",
        },
        {
          subject: "Biology: Cell Structure",
          color: "bg-green-100 text-green-800",
        },
      ],
    })
  );

  const lessonCards = [
    {
      title: "Physics Fundamentals",
      description: "Introduction to basic physics concepts and principles",
      objectives: [
        "Understand Newton's Laws of Motion",
        "Apply physics principles to real-world scenarios",
        "Complete laboratory experiments",
      ],
      resources: 12,
      exercises: 8,
      progress: 75,
      status: "in-progress" as const,
      dueDate: "Apr 30, 2024",
    },
    {
      title: "Biology Essentials",
      description: "Comprehensive study of cellular biology and organisms",
      objectives: [
        "Explore cell structure and function",
        "Study cellular processes",
        "Conduct microscope observations",
      ],
      resources: 15,
      exercises: 10,
      progress: 60,
      status: "in-progress" as const,
      dueDate: "May 5, 2024",
    },
  ];

  const actionButtons = (
    <div className="flex flex-wrap items-center gap-2 sm:gap-4">
      <button className="flex items-center gap-2 px-3 sm:px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm sm:text-base">
        <Plus className="w-4 h-4" />
        {t("curriculum.addCurriculum")}
      </button>
      <button className="flex items-center gap-2 px-3 sm:px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-sm sm:text-base">
        <Settings className="w-4 h-4" />
        {t("curriculum.settings")}
      </button>
    </div>
  );

  return (
    <div className="space-y-6">
      <CurriculumView
        title={t("curriculum.curriculumManagement")}
        actionButtons={actionButtons}
        schedule={schedule}
        lessonCards={lessonCards}
        onLessonEdit={(lesson) => console.log("Edit lesson:", lesson)}
        gradeLevel={["Grade 7", "Grade 8", "Grade 9", "Grade 10"]}
        subjects={["Physics", "Biology", "Chemistry", "Mathematics"]}
      />
    </div>
  );
}
