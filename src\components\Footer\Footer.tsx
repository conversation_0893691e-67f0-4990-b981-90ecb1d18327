import { useTranslation } from "react-i18next";
import { SlGraduation } from "react-icons/sl";
import { NavLink } from "react-router-dom";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function Footer() {
  const { t, i18n } = useTranslation();

  const changeLanguage = (lng: any) => {
    i18n.changeLanguage(lng);
    localStorage.setItem("language", lng);
  };

  return (
    <footer className="bg-gray-900 text-white py-8">
      <div className="container mx-auto px-4 md:px-8">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <NavLink to="/">
            <div className="flex items-center mb-6 md:mb-0">
              <SlGraduation className="text-white text-xl md:text-2xl mr-2" />
              <span className="font-bold text-lg md:text-xl">E-ditar</span>
            </div>
          </NavLink>

          <div className="flex flex-wrap justify-center gap-4 md:gap-8 mb-6 md:mb-0">
            <NavLink
              to="/about"
              className="hover:text-gray-300 text-sm md:text-base"
            >
              {t("welcomePage.aboutUs")}
            </NavLink>
            <NavLink
              to="/features"
              className="hover:text-gray-300 text-sm md:text-base"
            >
              {t("welcomePage.featuresTitle")}
            </NavLink>
            <NavLink
              to="/pricing"
              className="hover:text-gray-300 text-sm md:text-base"
            >
              {t("welcomePage.pricing")}
            </NavLink>
            <NavLink
              to="/contact"
              className="hover:text-gray-300 text-sm md:text-base"
            >
              {t("welcomePage.contactUs")}
            </NavLink>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-6 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm md:text-base mb-4 md:mb-0 text-center md:text-left">
            © {new Date().getFullYear()} {t("welcomePage.copyright")}
          </p>
          <div className="flex flex-wrap justify-center gap-4 md:gap-4">
            <AlertDialog>
              <AlertDialogTrigger className="hover:text-gray-300 text-xs md:text-sm">
                {t("welcomePage.privacyPolicy")}
              </AlertDialogTrigger>
              <AlertDialogContent className="max-h-[80vh] overflow-y-auto">
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {t("welcomePage.privacyPolicy")}
                  </AlertDialogTitle>
                  <AlertDialogDescription className="text-left space-y-4">
                    <h3 className="font-semibold text-base">
                      {t("privacy.section1Title")}
                    </h3>
                    <p>{t("privacy.section1Desc")}</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>{t("privacy.section1List.0")}</li>
                      <li>{t("privacy.section1List.1")}</li>
                      <li>{t("privacy.section1List.2")}</li>
                    </ul>

                    <h3 className="font-semibold text-base">
                      {t("privacy.section2Title")}
                    </h3>
                    <p>{t("privacy.section2Desc")}</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>{t("privacy.section2List.0")}</li>
                      <li>{t("privacy.section2List.1")}</li>
                      <li>{t("privacy.section2List.2")}</li>
                      <li>{t("privacy.section2List.3")}</li>
                    </ul>

                    <h3 className="font-semibold text-base">
                      {t("privacy.section3Title")}
                    </h3>
                    <p>{t("privacy.section3Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("privacy.section4Title")}
                    </h3>
                    <p>{t("privacy.section4Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("privacy.section5Title")}
                    </h3>
                    <p>{t("privacy.section5Desc")}</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>{t("privacy.section5List.0")}</li>
                      <li>{t("privacy.section5List.1")}</li>
                      <li>{t("privacy.section5List.2")}</li>
                      <li>{t("privacy.section5List.3")}</li>
                    </ul>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogAction>
                    {t("privacy.understand")}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <AlertDialog>
              <AlertDialogTrigger className="hover:text-gray-300 text-xs md:text-sm">
                {t("welcomePage.termsOfService")}
              </AlertDialogTrigger>
              <AlertDialogContent className="max-h-[80vh] overflow-y-auto">
                <AlertDialogHeader>
                  <AlertDialogTitle>
                    {t("welcomePage.termsOfService")}
                  </AlertDialogTitle>
                  <AlertDialogDescription className="text-left space-y-4">
                    <h3 className="font-semibold text-base">
                      {t("terms.section1Title")}
                    </h3>
                    <p>{t("terms.section1Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("terms.section2Title")}
                    </h3>
                    <p>{t("terms.section2Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("terms.section3Title")}
                    </h3>
                    <p>{t("terms.section3Desc")}</p>
                    <ul className="list-disc pl-5 space-y-1">
                      <li>{t("terms.section3List.0")}</li>
                      <li>{t("terms.section3List.1")}</li>
                      <li>{t("terms.section3List.2")}</li>
                      <li>{t("terms.section3List.3")}</li>
                      <li>{t("terms.section3List.4")}</li>
                    </ul>

                    <h3 className="font-semibold text-base">
                      {t("terms.section4Title")}
                    </h3>
                    <p>{t("terms.section4Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("terms.section5Title")}
                    </h3>
                    <p>{t("terms.section5Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("terms.section6Title")}
                    </h3>
                    <p>{t("terms.section6Desc")}</p>

                    <h3 className="font-semibold text-base">
                      {t("terms.section7Title")}
                    </h3>
                    <p>{t("terms.section7Desc")}</p>
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogAction>
                    {t("terms.acceptButton")}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>

            <div className="flex ml-4">
              <button
                className={`text-center px-3 py-1 border-r border-gray-700 ${
                  i18n.language === "sq"
                    ? "text-purple-400 font-bold"
                    : "text-gray-300 hover:text-white"
                }`}
                onClick={() => changeLanguage("sq")}
              >
                SQ
              </button>
              <button
                className={`text-center px-3 py-1 ${
                  i18n.language === "en"
                    ? "text-purple-400 font-bold"
                    : "text-gray-300 hover:text-white"
                }`}
                onClick={() => changeLanguage("en")}
              >
                EN
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
