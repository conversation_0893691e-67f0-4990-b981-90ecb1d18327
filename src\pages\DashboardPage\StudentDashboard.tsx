import { useState } from "react";
import StatCard from "../../components/StatCard";
import {
  GraduationCap,
  ClipboardList,
  Calendar,
  BarChart3,
  Clock,
  BookOpen,
} from "lucide-react";
import ScheduleDashCard from "../../components/ScheduleDashCard";
import SubjectCard from "../../components/SubjectCard";
import { useTranslation } from "react-i18next";
import AcademicPerformance from "../../components/AcademicPerformance";
import UpcomingEvents from "../../components/UpcomingEvents/UpcomingEvents";
import FormattedDate from "@/components/FormattedDate";
import { studentSubjects, getPerformanceData } from "../SubjectsPage/mockData";

export default function StudentDashboard() {
  const { t } = useTranslation();
  const [showAllSubjects, setShowAllSubjects] = useState(false);

  const visibleSubjects = showAllSubjects
    ? studentSubjects
    : studentSubjects.slice(0, 4);
  const studentPerformanceData = getPerformanceData(studentSubjects);

  // Update performance data to use consistent colors
  const updatedPerformanceData = studentPerformanceData.map((item) => ({
    ...item,
    color: "#7b3aed",
  }));

  const ProgressBar = ({ value }: { value: number }) => {
    return (
      <div className="w-full h-2 bg-gray-200 rounded">
        <div
          className="h-full rounded bg-purple-700"
          style={{ width: `${value}%` }}
        />
      </div>
    );
  };

  const currentDay = new Date()
    .toLocaleDateString("en-US", { weekday: "long" })
    .toLowerCase();

  return (
    <main>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 mb-6">
        <h1 className="text-2xl sm:text-3xl font-bold">
          {t("dashboardStudent.title")}
        </h1>
        <p className="text-gray-500">
          <FormattedDate />
        </p>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <StatCard
          title={t("dashboardStudent.averageGrade")}
          value="87.0"
          progressBar={<ProgressBar value={87} />}
          description={`${t("dashboardStudent.basedOn")} 8 grades`}
          icon={<BarChart3 className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboardStudent.totalSubjects")}
          value="6"
          description={t("dashboardStudent.AllenrolledSubjects")}
          icon={<GraduationCap className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboardStudent.upcomingAssignments")}
          value="3"
          description={t("dashboardStudent.dueThisWeek")}
          icon={<ClipboardList className="h-5 w-5" />}
        />
        <StatCard
          title={t("dashboardStudent.absences")}
          value="3"
          description={t("dashboardStudent.thisSemester")}
          icon={<Clock className="h-5 w-5" />}
        />
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-7 gap-4 sm:gap-6">
        <div className="col-span-1 lg:col-span-4 p-6 border border-gray-300 bg-white rounded-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              <h2 className="text-xl font-bold">
                {t("dashboardStudent.latestTopic")}: Newton's Laws of Motion
              </h2>
            </div>
          </div>
          <p className="text-gray-500 pb-4">
            {t("dashboardStudent.understandingPrinciples")}
          </p>
          <p className="font-bold pb-4">
            {t("dashboardStudent.recommendedResources")}:
          </p>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div className="border border-gray-300 p-4 rounded-md bg-white shadow-sm">
              <div className="flex flex-col justify-between h-48 items-center text-center">
                <div className="bg-purple-100 p-2 rounded-full mb-2">
                  <BookOpen className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-base">
                  Newton's Three Laws Explained
                </h3>
                <p className="text-sm text-gray-500 mb-3">
                  {t("dashboardStudent.article")}
                </p>
                <button className="border border-gray-300 rounded-md px-4 py-1 text-sm font-medium hover:bg-gray-100 cursor-pointer">
                  {t("dashboardStudent.openArticle")}
                </button>
              </div>
            </div>
            <div className="border border-gray-300 p-4 rounded-md bg-white shadow-sm">
              <div className="flex flex-col justify-between h-48  items-center text-center">
                <div className="bg-purple-100 p-2 rounded-full mb-2">
                  <GraduationCap className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-base">
                  Physics Lab: Demonstrating Newton's Laws
                </h3>
                <p className="text-sm text-gray-500 mb-3">
                  {t("dashboardStudent.video")}
                </p>
                <button className="border border-gray-300 rounded-md px-4 py-1 text-sm font-medium hover:bg-gray-100 cursor-pointer">
                  {t("dashboardStudent.openVideo")}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="col-span-1 lg:col-span-3 p-6 border border-gray-300 bg-white rounded-md">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <h2 className="text-xl font-bold">
                {" "}
                {t("dashboard.todaysSchedule")}
              </h2>
            </div>
            <span className="text-gray-500">{t(`days.${currentDay}`)}</span>
          </div>

          <div className="space-y-4">
            <ScheduleDashCard
              subject="Mathematics"
              time="8:00 - 9:30"
              room="Room 101"
              color="blue"
            />
            <ScheduleDashCard
              subject="Physics"
              time="9:45 - 11:15"
              room="Room 205"
              color="green"
            />
            <ScheduleDashCard
              subject="Biology"
              time="12:00 - 13:30"
              room="Room 302"
              color="yellow"
            />
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-6 pt-4 sm:pt-6 ">
        <div className="col-span-1 lg:col-span-9 p-6 border border-gray-300 bg-white rounded-md ">
          <AcademicPerformance
            data={updatedPerformanceData}
            title={t("academicPerformance")}
            subtitle={t("yourSubjectPerformance")}
          />
        </div>

        <div className="col-span-1 lg:col-span-3 p-6 border border-gray-300 bg-white rounded-md">
          <UpcomingEvents />
        </div>
      </div>

      <div className="py-10">
        <p className="pb-5 font-semibold text-lg">
          {t("dashboard.yourSubjects")}
        </p>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 pb-10">
          {visibleSubjects.map((subject) => (
            <SubjectCard key={subject.id} subject={subject} />
          ))}
        </div>
        {studentSubjects.length > 4 && (
          <div className="flex justify-center">
            <button
              onClick={() => setShowAllSubjects(!showAllSubjects)}
              className="text-sm font-medium px-4 py-2 rounded-md border border-gray-300 hover:bg-slate-100 bg-white cursor-pointer"
            >
              {showAllSubjects
                ? t("dashboard.showLess")
                : t("dashboard.viewAllSubjects")}
            </button>
          </div>
        )}
      </div>
    </main>
  );
}
