import { FaRegBell } from "react-icons/fa";
import { LogOut, User, Menu, X } from "lucide-react";
import { NavLink, useNavigate, useLocation } from "react-router-dom";
import { useTypedAuthUser, useSignOut } from "../../hooks/useAuth";
import { MdWavingHand } from "react-icons/md";
import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

export default function Header({
  toggleSidebar,
  isSidebarOpen,
  isMobile,
}: {
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  isMobile: boolean;
}) {
  const authUser = useTypedAuthUser();
  const signOut = useSignOut();
  const [unreadCount, setUnreadCount] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);
  const navigate = useNavigate();
  const { t } = useTranslation();
  const location = useLocation();
  const isDashboardPage = location.pathname === "/dashboard";

  const isAnyLoading = false;

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Get unread notifications count based on user role
  useEffect(() => {
    if (authUser?.role) {
      // Normalize role to lowercase for consistent comparison
      const normalizedRole = authUser.role.trim().toLowerCase();

      let notifications: { id: number; read: boolean }[];
      switch (normalizedRole) {
        case "student":
          notifications = studentNotifications;
          break;
        case "teacher":
          notifications = teacherNotifications;
          break;
        case "parent":
          notifications = parentNotifications;
          break;
        case "admin":
          notifications = directorNotifications;
          break;
        default:
          console.warn("Header: Unknown role for notifications", {
            originalRole: authUser.role,
            normalizedRole,
          });
          notifications = [];
      }

      const count = notifications.filter((n) => !n.read).length;
      setUnreadCount(count);
    }
  }, [authUser?.role]);

  const handleLogout = () => {
    signOut();
    navigate("/login");
  };

  const handleClickProfile = () => {
    navigate("/settings");
  };

  return (
    <header
      className={`bg-white border-b border-gray-300 px-3 sm:px-4 md:px-6 py-3 md:py-4 sticky top-0 z-10 transition-all duration-200 ${
        isScrolled ? "shadow-md" : ""
      } relative`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center flex-1">
          {isMobile && (
            <button onClick={toggleSidebar} className=" bg-purple-50 p-1">
              {isSidebarOpen ? (
                <X className="w-6 h-6 text-[#6e59a5]" />
              ) : (
                <Menu className="w-6 h-6 text-[#6e59a5]" />
              )}
            </button>
          )}
          <div className="flex items-center">
            <h2 className="font-semibold text-xs sm:text-sm md:text-base truncate max-w-[140px] sm:max-w-[200px] md:max-w-none ml-2 sm:ml-3">
              {isDashboardPage ? `${t("welcome")}, ` : ""}
              {authUser?.name}
            </h2>
            <MdWavingHand className="text-orange-400 text-sm sm:text-base md:text-lg ml-1" />
          </div>
        </div>

        <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
          <NavLink to={"/notifications"}>
            <button className="relative p-1 sm:p-1.5 md:p-2 rounded-full hover:bg-gray-100 transition-colors">
              <FaRegBell className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6 text-gray-600" />
              {unreadCount > 0 && (
                <span className="absolute top-0 right-0 h-3 w-3 sm:h-4 sm:w-4 rounded-full bg-red-500 flex items-center justify-center text-white text-[8px] sm:text-xs">
                  {unreadCount > 9 ? "9+" : unreadCount}
                </span>
              )}
            </button>
          </NavLink>

          <Popover>
            <PopoverTrigger asChild>
              <button className="h-6 w-6 sm:h-7 sm:w-7 md:h-8 md:w-8 rounded-full bg-gray-200 flex items-center justify-center hover:bg-gray-300 transition-colors">
                <User className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5 text-gray-600" />
              </button>
            </PopoverTrigger>
            <PopoverContent
              className="w-40 sm:w-48 md:w-56 p-0 rounded-md border border-gray-300 shadow-md"
              align="end"
              side="bottom"
              sideOffset={5}
            >
              <div className="p-2 sm:p-3">
                <p className="font-semibold text-sm sm:text-base truncate">
                  {authUser?.name}
                </p>
                <p className="text-xs md:text-sm truncate text-gray-500">
                  {authUser?.email}
                </p>
              </div>
              <div
                className="border-t border-gray-300 flex items-center p-2 sm:p-3 hover:bg-gray-100 cursor-pointer transition-colors"
                onClick={handleClickProfile}
              >
                <User className="w-3 h-3 sm:w-4 sm:h-4" />
                <p className="pl-1 sm:pl-2 font-medium text-xs sm:text-sm">
                  {t("profile")}
                </p>
              </div>
              <div
                className="border-t border-gray-300 flex items-center p-2 sm:p-3 hover:bg-gray-100 cursor-pointer transition-colors"
                onClick={handleLogout}
              >
                <LogOut className="w-3 h-3 sm:w-4 sm:h-4" />
                <p className="pl-1 sm:pl-2 font-medium text-xs sm:text-sm">
                  {t("logout")}
                </p>
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {isAnyLoading && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-purple-200">
          <div className="h-full bg-purple-600 w-1/3 animate-[loading_1.5s_ease-in-out_infinite]"></div>
        </div>
      )}
    </header>
  );
}

const studentNotifications = [
  { id: 1, read: false },
  { id: 2, read: false },
  { id: 3, read: true },
  { id: 4, read: true },
];

const teacherNotifications = [
  { id: 1, read: false },
  { id: 2, read: false },
  { id: 3, read: true },
  { id: 4, read: true },
];

const parentNotifications = [
  { id: 1, read: false },
  { id: 2, read: false },
  { id: 3, read: true },
  { id: 4, read: true },
];

const directorNotifications = [
  { id: 1, read: false },
  { id: 2, read: false },
  { id: 3, read: true },
  { id: 4, read: true },
];
