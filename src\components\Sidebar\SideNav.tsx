import {
  BookOpen,
  Calendar,
  GraduationCap,
  LayoutDashboard,
  BarChart3,
  ClipboardList,
  Clock,
  UserCheck,
  MessageSquare,
  NotebookText,
  Settings,
  FileText,
  Bot,
  ClipboardListIcon,
  Users,
} from "lucide-react";

export const studentNavItems = [
  {
    label: "sidebar.dashboard",
    path: "/dashboard",
    icon: <LayoutDashboard size={18} />,
  },
  {
    label: "sidebar.curriculum",
    path: "/curriculum",
    icon: <NotebookText size={18} />,
  },
  {
    label: "sidebar.schedule",
    path: "/schedule",
    icon: <Calendar size={18} />,
  },
  {
    label: "sidebar.subjects",
    path: "/subjects",
    icon: <BookOpen size={18} />,
  },
  {
    label: "sidebar.assignments",
    path: "/assignments",
    icon: <ClipboardList size={18} />,
  },
  { label: "sidebar.grades", path: "/grades", icon: <BarChart3 size={18} /> },
  {
    label: "sidebar.attendance",
    path: "/attendance",
    icon: <Clock size={18} />,
  },
  {
    label: "sidebar.assistant",
    path: "/assistant",
    icon: <Bot size={18} />,
  },
  {
    label: "sidebar.settings",
    path: "/settings",
    icon: <Settings size={18} />,
  },
];

export const teacherNavItems = [
  {
    label: "sidebar.dashboard",
    path: "/dashboard",
    icon: <LayoutDashboard size={18} />,
  },
  {
    label: "sidebar.curriculum",
    path: "/curriculum",
    icon: <NotebookText size={18} />,
  },
  {
    label: "sidebar.classSchedule",
    path: "/schedule",
    icon: <Calendar size={18} />,
  },
  {
    label: "sidebar.manageTopics",
    path: "/subjects",
    icon: <BookOpen size={18} />,
  },
  {
    label: "sidebar.assignments",
    path: "/assignments",
    icon: <ClipboardList size={18} />,
  },
  {
    label: "sidebar.gradeStudents",
    path: "/grades",
    icon: <BarChart3 size={18} />,
  },
  {
    label: "sidebar.attendance",
    path: "/attendance",
    icon: <UserCheck size={18} />,
  },
  {
    label: "sidebar.studentmanagement",
    path: "/students",
    icon: <GraduationCap size={18} />,
  },
  {
    label: "sidebar.assistant",
    path: "/assistant",
    icon: <Bot size={18} />,
  },
  {
    label: "sidebar.settings",
    path: "/settings",
    icon: <Settings size={18} />,
  },
];

export const parentNavItems = [
  {
    label: "sidebar.dashboard",
    path: "/dashboard",
    icon: <LayoutDashboard size={18} />,
  },
  {
    label: "sidebar.curriculum",
    path: "/curriculum",
    icon: <NotebookText size={18} />,
  },
  {
    label: "sidebar.childSchedule",
    path: "/schedule",
    icon: <Calendar size={18} />,
  },
  {
    label: "sidebar.subjects",
    path: "/subjects",
    icon: <BookOpen size={18} />,
  },
  { label: "sidebar.grades", path: "/grades", icon: <BarChart3 size={18} /> },
  {
    label: "sidebar.attendance",
    path: "/attendance",
    icon: <Clock size={18} />,
  },
  {
    label: "sidebar.messages",
    path: "/messages",
    icon: <MessageSquare size={18} />,
  },
  {
    label: "sidebar.assistant",
    path: "/assistant",
    icon: <Bot size={18} />,
  },
  {
    label: "sidebar.settings",
    path: "/settings",
    icon: <Settings size={18} />,
  },
];

export const directorNavItems = [
  {
    label: "sidebar.dashboard",
    path: "/dashboard",
    icon: <LayoutDashboard size={18} />,
  },
  {
    label: "sidebar.curriculum",
    path: "/curriculum",
    icon: <NotebookText size={18} />,
  },
  {
    label: "sidebar.subjects",
    path: "/subjects",
    icon: <BookOpen size={18} />,
  },
  {
    label: "sidebar.staffMembers",
    path: "/staff-members",
    icon: <Users size={18} />,
  },
  {
    label: "sidebar.studentmanagement",
    path: "/students",
    icon: <GraduationCap size={18} />,
  },
  {
    label: "sidebar.lessonObservation",
    path: "/lesson-observation",
    icon: <ClipboardListIcon size={18} />,
  },
  {
    label: "sidebar.analytics",
    path: "/analytics",
    icon: <BarChart3 size={18} />,
  },
  {
    label: "sidebar.reports",
    path: "/reports",
    icon: <FileText size={18} />,
  },
  {
    label: "sidebar.assistant",
    path: "/assistant",
    icon: <Bot size={18} />,
  },
  {
    label: "sidebar.settings",
    path: "/settings",
    icon: <Settings size={18} />,
  },
];
