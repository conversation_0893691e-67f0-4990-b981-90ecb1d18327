import { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Search } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Student {
  id: string;
  name: string;
}

interface RecordAttendanceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  subject: string;
  date: string;
}

export default function RecordAttendanceDialog({
  isOpen,
  onClose,
  onSubmit,
  subject,
  date,
}: RecordAttendanceDialogProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");

  // Mock student data - replace with actual data from your API
  const students: Student[] = [
    { id: "1", name: "<PERSON>" },
    { id: "2", name: "<PERSON>" },
    { id: "3", name: "<PERSON>" },
    { id: "4", name: "<PERSON>" },
    { id: "5", name: "<PERSON>" },
    { id: "6", name: "<PERSON>" },
    { id: "7", name: "James Anderson" },
  ];

  const [attendanceData, setAttendanceData] = useState(
    students.map((student) => ({
      studentId: student.id,
      status: "present", // default to present
      reason: "",
    }))
  );

  // Filter students based on search term
  const filteredStudents = students
    .filter((student) =>
      student.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const handleStatusChange = (studentId: string, status: string) => {
    setAttendanceData(
      attendanceData.map((record) =>
        record.studentId === studentId
          ? {
              ...record,
              status,
              reason: status === "absent" ? record.reason : "",
            }
          : record
      )
    );
  };

  const handleReasonChange = (studentId: string, reason: string) => {
    setAttendanceData(
      attendanceData.map((record) =>
        record.studentId === studentId ? { ...record, reason } : record
      )
    );
  };

  const handleMarkAllPresent = () => {
    setAttendanceData(
      attendanceData.map((record) => ({
        ...record,
        status: "present",
        reason: "",
      }))
    );
  };

  const handleSubmit = () => {
    onSubmit({
      subject,
      date,
      records: attendanceData,
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t("attendance.recordAttendance")}</DialogTitle>
        </DialogHeader>

        <div className="mb-4">
          <p className="font-medium">{subject}</p>
          <p className="text-sm text-gray-500">{date}</p>
        </div>

        {/* Add search input */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder={t("attendance.searchStudents")}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="border rounded-md px-4 py-2 w-full pl-10"
          />
        </div>

        <div className="space-y-4">
          <div className="flex justify-end mb-2">
            <Button
              variant="outline"
              onClick={handleMarkAllPresent}
              className="text-xs sm:text-sm"
              size="sm"
            >
              {t("attendance.markAllPresent")}
            </Button>
          </div>

          <div className="border rounded-md overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="text-left py-2 sm:py-3 px-2 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm">
                    {t("attendance.student")}
                  </th>
                  <th className="text-center py-2 sm:py-3 px-2 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm">
                    {t("attendance.present")}
                  </th>
                  <th className="text-center py-2 sm:py-3 px-2 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm">
                    {t("attendance.absent")}
                  </th>
                  <th className="text-center py-2 sm:py-3 px-2 sm:px-4 font-medium text-gray-500 text-xs sm:text-sm">
                    {t("attendance.late")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {filteredStudents.map((student) => {
                  const record = attendanceData.find(
                    (r) => r.studentId === student.id
                  )!;
                  return (
                    <tr key={student.id} className="hover:bg-gray-50">
                      <td className="py-2 sm:py-3 px-2 sm:px-4 text-xs sm:text-sm">
                        {student.name}
                      </td>
                      <td className="text-center py-2 sm:py-3 px-2 sm:px-4">
                        <button
                          className={`w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center ${
                            record.status === "present"
                              ? "bg-purple-500 text-white"
                              : "border border-gray-300"
                          }`}
                          onClick={() =>
                            handleStatusChange(student.id, "present")
                          }
                        >
                          {record.status === "present" && (
                            <Check className="w-3 h-3 sm:w-4 sm:h-4" />
                          )}
                        </button>
                      </td>
                      <td className="text-center py-2 sm:py-3 px-2 sm:px-4">
                        <div className="flex items-center justify-center">
                          <button
                            className={`w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center ${
                              record.status === "absent"
                                ? "bg-purple-500 text-white"
                                : "border border-gray-300"
                            }`}
                            onClick={() =>
                              handleStatusChange(student.id, "absent")
                            }
                          >
                            {record.status === "absent" && (
                              <Check className="w-3 h-3 sm:w-4 sm:h-4" />
                            )}
                          </button>
                          {record.status === "absent" && (
                            <div className="ml-2 w-16 sm:w-24">
                              <Select
                                value={record.reason}
                                onValueChange={(value) =>
                                  handleReasonChange(student.id, value)
                                }
                              >
                                <SelectTrigger className="h-6 sm:h-8 text-xs">
                                  <SelectValue
                                    placeholder={t("attendance.reason")}
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="sick">
                                    {t("attendance.reasons.sick")}
                                  </SelectItem>
                                  <SelectItem value="family">
                                    {t("attendance.reasons.family")}
                                  </SelectItem>
                                  <SelectItem value="leaveHour">
                                    {t("attendance.reasons.leaveHour")}
                                  </SelectItem>
                                  <SelectItem value="other">
                                    {t("attendance.reasons.other")}
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="text-center py-2 sm:py-3 px-2 sm:px-4">
                        <button
                          className={`w-5 h-5 sm:w-6 sm:h-6 rounded-full flex items-center justify-center ${
                            record.status === "late"
                              ? "bg-purple-500 text-white"
                              : "border border-gray-300"
                          }`}
                          onClick={() => handleStatusChange(student.id, "late")}
                        >
                          {record.status === "late" && (
                            <Check className="w-3 h-3 sm:w-4 sm:h-4" />
                          )}
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        <div className="mt-6">
          <Button
            onClick={handleSubmit}
            className="w-full bg-purple-600 hover:bg-purple-700 text-white"
          >
            {t("submit")}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
