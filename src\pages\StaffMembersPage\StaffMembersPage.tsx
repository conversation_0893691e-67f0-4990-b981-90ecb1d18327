import { useState } from "react";
import { Search, UserPlus, Eye, Pencil } from "lucide-react";
import { useTranslation } from "react-i18next";
import TeacherDialog from "../../components/TeacherDialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import toast from "react-hot-toast";

type StaffStatus = "All" | "Active" | "Leave";
type StaffRole = "All" | "Teachers" | "Professors" | "Inspectors" | "Directors";

const statusOptions: StaffStatus[] = ["All", "Active", "Leave"];
const roleOptions: StaffRole[] = [
  "All",
  "Teachers",
  "Professors",
  "Inspectors",
  "Directors",
];

const staffMembers = [
  {
    id: 1,
    name: "<PERSON>",
    subject: "Mathematics",
    email: "<EMAIL>",
    phone: "(*************",
    status: "Active",
    role: "Teachers",
    classes: 5,
    experience: "8 years",
  },
  {
    id: 6,
    name: "<PERSON>",
    subject: "Mathematics",
    email: "<EMAIL>",
    phone: "(*************",
    status: "Leave",
    role: "Teachers",
    classes: 5,
    experience: "8 years",
  },
  {
    id: 2,
    name: "<PERSON>",
    subject: "Science",
    email: "<EMAIL>",
    phone: "(*************",
    status: "Active",
    role: "Professors",
    classes: 4,
    experience: "12 years",
  },
  {
    id: 3,
    name: "Sarah Johnson",
    subject: "English Literature",
    email: "<EMAIL>",
    phone: "(*************",
    status: "Leave",
    role: "Inspectors",
    classes: 0,
    experience: "15 years",
  },
  {
    id: 4,
    name: "Robert King",
    subject: "School Management",
    email: "<EMAIL>",
    phone: "(*************",
    status: "Active",
    role: "Directors",
    classes: 0,
    experience: "10 years",
  },
];

export default function StaffMembersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<StaffStatus>("All");
  const [roleFilter, setRoleFilter] = useState<StaffRole>("All");
  const { t } = useTranslation();

  const filteredStaff = searchTerm
    ? staffMembers.filter((member) =>
        member.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : staffMembers.filter((member) => {
        const matchesRole = roleFilter === "All" || member.role === roleFilter;
        const matchesStatus =
          statusFilter === "All" || member.status === statusFilter;
        return matchesRole && matchesStatus;
      });

  const handleAddStaff = (data: any) => {
    console.log("Add staff member:", data);
    toast.success(t("teachersMessages.teacherAdded"));
  };

  const handleEditStaff = (data: any) => {
    console.log("Edit staff member:", data);
    toast.success(t("teachersMessages.teacherUpdated"));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center flex-wrap gap-4">
        <div>
          <h1 className="text-3xl font-bold">{t("staffmembers.title")}</h1>
          <p className="text-gray-500 text-sm">{t("staffmembers.subtitle")}</p>
        </div>
        <TeacherDialog
          mode="add"
          trigger={
            <Button className="bg-purple-600 hover:bg-purple-500 text-white">
              <UserPlus className="h-4 w-4 mr-2" />
              {t("staffmembers.addTeacher")}
            </Button>
          }
          onSubmit={handleAddStaff}
        />
      </div>

      {/* Filter Section */}
      <div className="bg-white rounded-lg border border-gray-200 mt-8">
        <div className="px-10 pt-10 flex flex-col lg:flex-row justify-between gap-6">
          {/* Filters */}
          <div className="flex flex-col gap-4">
            {/* Role Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("staffmembers.filterByRole")}:
              </label>
              <div className="flex flex-wrap gap-2">
                {roleOptions.map((role) => {
                  const count =
                    role === "All"
                      ? staffMembers.filter(
                          (m) =>
                            statusFilter === "All" || m.status === statusFilter
                        ).length
                      : staffMembers.filter(
                          (m) =>
                            m.role === role &&
                            (statusFilter === "All" ||
                              m.status === statusFilter)
                        ).length;

                  return (
                    <Button
                      key={role}
                      size="sm"
                      onClick={() => setRoleFilter(role)}
                      className={`rounded-full px-4 py-1 text-sm ${
                        roleFilter === role
                          ? "bg-gray-900 text-white"
                          : "bg-white border text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      {role === "All"
                        ? t("staffmembers.roles.all")
                        : t(`staffmembers.roles.${role.toLowerCase()}`)}
                      {"  "}({count})
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("staffmembers.filterByStatus")}:
              </label>
              <div className="flex gap-2 flex-wrap">
                {statusOptions.map((status) => {
                  status === "All"
                    ? staffMembers.filter(
                        (m) => roleFilter === "All" || m.role === roleFilter
                      ).length
                    : staffMembers.filter(
                        (m) =>
                          m.status === status &&
                          (roleFilter === "All" || m.role === roleFilter)
                      ).length;

                  return (
                    <Button
                      key={status}
                      size="sm"
                      onClick={() => setStatusFilter(status)}
                      className={`rounded-full px-4 py-1 text-sm ${
                        statusFilter === status
                          ? "bg-gray-700 text-white"
                          : "bg-white border text-gray-700 hover:bg-gray-100"
                      }`}
                    >
                      {status === "All"
                        ? t("staffmembers.status.all")
                        : t(`teachers.status.${status.toLowerCase()}`)}{" "}
                    </Button>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Search */}
          <div className="lg:w-100 w-full self-start">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t("staffmembers.searchTeachers")}
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder={t("teachers.searchTeachers")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        {/* Staff Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 p-10">
          {filteredStaff.map((staff) => (
            <div
              key={staff.id}
              className="border rounded-lg p-4 shadow-sm hover:shadow-md transition duration-300 bg-white flex flex-col justify-between"
            >
              <div>
                <div className="flex justify-between pb-4 ">
                  <h3 className="text-lg font-semibold ">{staff.name}</h3>
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      staff.status === "Active"
                        ? "bg-green-100 text-green-800"
                        : "bg-orange-100 text-orange-800"
                    }`}
                  >
                    {t(`teachers.status.${staff.status.toLowerCase()}`)}
                  </span>
                </div>
                <p className="text-purple-600 text-sm mb-1">{staff.subject}</p>
                <div className="mb-4">
                  <p className="text-blue-600 text-sm">{staff.email}</p>
                  <p className="text-gray-500 text-xs">{staff.phone}</p>
                </div>

                <div className="flex flex-col text-sm text-gray-700 gap-1 mb-4">
                  <p>
                    {t("teachers.details.classes")}: {staff.classes}
                  </p>
                  <p>
                    {t("teachers.details.experience")}: {staff.experience}
                  </p>
                </div>
              </div>
              <div className="flex justify-between gap-2 mt-4">
                <TeacherDialog
                  mode="view"
                  trigger={
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      {t("teachers.details.view")}
                    </Button>
                  }
                  teacher={staff}
                />
                <TeacherDialog
                  mode="edit"
                  trigger={
                    <Button variant="outline" size="sm">
                      <Pencil className="h-4 w-4 mr-1" />
                      {t("teachers.details.edit")}
                    </Button>
                  }
                  teacher={staff}
                  onSubmit={handleEditStaff}
                />
              </div>
            </div>
          ))}
          {filteredStaff.length === 0 && (
            <div className="col-span-5 flex items-center justify-center h-[300px] text-gray-500 text-center">
              {t("staffmembers.noResults")}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
