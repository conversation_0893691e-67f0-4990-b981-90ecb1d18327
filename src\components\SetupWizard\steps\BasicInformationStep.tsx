import { useTranslation } from "react-i18next";
import { School } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import { SearchableSelect } from "@/components/ui/searchable-select";
import { Combobox } from "@/components/ui/combobox";
import { useSetupWizard } from "../SetupWizardContext";
import { mockCities, mockVillages } from "@/data/locationData";

export default function BasicInformationStep() {
  const { t } = useTranslation();
  const { setupData, updateBasicInfo, validationErrors, setValidationError } =
    useSetupWizard();
  const [locationType, setLocationType] = useState<string>(
    setupData.basicInfo.locationType || ""
  );

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    updateBasicInfo({
      [name]: value,
    });

    // Clear validation error if field is not empty
    if (value.trim() !== "") {
      setValidationError(name, false);
    }
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    if (name === "locationType") {
      setLocationType(value);
    }
    updateBasicInfo({
      [name]: value,
    });

    // Clear validation error if field is not empty
    if (value.trim() !== "") {
      setValidationError(name, false);
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6 flex items-center">
        <span className="text-purple-600 mr-2">
          <School size={20} />
        </span>
        {t("setup.basicSchoolInfo")}
      </h2>

      <div className="mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("setup.schoolName")}*
            </label>
            <Input
              name="schoolName"
              value={setupData.basicInfo.schoolName || ""}
              onChange={handleInputChange}
              placeholder={t("setup.enterSchoolName")}
              className={
                validationErrors.schoolName
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
            />
            {validationErrors.schoolName && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.schoolNameRequired")}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("setup.schoolCategory")}*
            </label>
            <Select
              value={setupData.basicInfo.schoolCategory || ""}
              onValueChange={(value) =>
                handleSelectChange("schoolCategory", value)
              }
            >
              <SelectTrigger
                className={`w-full ${
                  validationErrors.schoolCategory
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }`}
              >
                <SelectValue placeholder={t("setup.schoolCategory")} />
              </SelectTrigger>
              <SelectContent className="w-full">
                <SelectItem value="primary">{t("setup.primary")}</SelectItem>
                <SelectItem value="secondary">
                  {t("setup.secondary")}
                </SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.schoolCategory && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.schoolCategoryRequired")}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Location Section */}
      <div className="mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("setup.city")}*
            </label>
            <SearchableSelect
              value={setupData.basicInfo.city || ""}
              onValueChange={(value) => handleSelectChange("city", value)}
              placeholder={t("setup.enterCity")}
              searchPlaceholder={t("setup.searchCity")}
              options={mockCities}
              className={
                validationErrors.city
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
            />
            {validationErrors.city && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.cityRequired")}
              </p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("setup.schoolLocation")}*
            </label>
            <Select
              value={locationType}
              onValueChange={(value) =>
                handleSelectChange("locationType", value)
              }
            >
              <SelectTrigger
                className={`w-full ${
                  validationErrors.locationType
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }`}
              >
                <SelectValue placeholder={t("setup.selectLocation")} />
              </SelectTrigger>
              <SelectContent className="w-full">
                <SelectItem value="urban">{t("setup.urban")}</SelectItem>
                <SelectItem value="rural">{t("setup.rural")}</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.locationType && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.locationTypeRequired")}
              </p>
            )}
          </div>

          {locationType === "rural" && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t("setup.villageName")}*
              </label>
              <Combobox
                value={setupData.basicInfo.villageName || ""}
                onValueChange={(value) =>
                  handleSelectChange("villageName", value)
                }
                placeholder={t("setup.enterVillageName")}
                searchPlaceholder={t("setup.searchVillage")}
                options={mockVillages}
                allowCustom={true}
                className={
                  validationErrors.villageName
                    ? "border-red-500 focus-visible:ring-red-500"
                    : ""
                }
              />
              {validationErrors.villageName && (
                <p className="text-red-500 text-xs mt-1">
                  {t("validationSetup.villageNameRequired")}
                </p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Mission and Vision Section */}
      <div className="mb-4">
        <h3 className="text-md font-medium mb-4 text-gray-700 border-b pb-2">
          {t("setup.missionAndVision")}
        </h3>
        <div className="grid grid-cols-1 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("setup.mission")}*
            </label>
            <Textarea
              name="mission"
              value={setupData.basicInfo.mission || ""}
              onChange={(e) => handleInputChange(e)}
              placeholder={t("setup.enterMission")}
              className={
                validationErrors.mission
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
              rows={3}
            />
            {validationErrors.mission && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.missionRequired")}
              </p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              {t("setup.missionDescription")}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("setup.vision")}*
            </label>
            <Textarea
              name="vision"
              value={setupData.basicInfo.vision || ""}
              onChange={(e) => handleInputChange(e)}
              placeholder={t("setup.enterVision")}
              className={
                validationErrors.vision
                  ? "border-red-500 focus-visible:ring-red-500"
                  : ""
              }
              rows={3}
            />
            {validationErrors.vision && (
              <p className="text-red-500 text-xs mt-1">
                {t("validationSetup.visionRequired")}
              </p>
            )}
            <p className="text-xs text-gray-500 mt-1">
              {t("setup.visionDescription")}
            </p>
          </div>
        </div>
      </div>

      <p className="text-sm text-gray-500 mt-4">
        * {t("setup.requiredFields")}
      </p>
    </div>
  );
}
