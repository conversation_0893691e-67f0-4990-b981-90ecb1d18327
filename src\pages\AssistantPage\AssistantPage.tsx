import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Bot,
  Plus,
  Search,
  Trash2,
  BookOpen,
  Calendar,
  FileText,
  Users,
} from "lucide-react";
import { useTypedAuthUser } from "../../hooks/useAuth";
import toast from "react-hot-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../components/ui/alert-dialog";

// Import components
import { SearchInput } from "./SearchInput";
import { MainSearchBar } from "./MainSearchBar";
import { ChatMessage } from "./ChatMessage";
import { ChatHistoryItem } from "./ChatHistoryItem";
import { SuggestionCard } from "./SuggestionCard";

// Import types
import { ChatHistory, Message } from "./types";

const GlobalStyle = () => {
  return (
    <style>{`
      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }

      .scrollbar-hide {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
      }
    `}</style>
  );
};

// Add this utility function
const useDebounce = <T,>(value: T, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const AssistantPage: React.FC = () => {
  const { t } = useTranslation();
  const user = useTypedAuthUser();
  const [chatHistories, setChatHistories] = useState<ChatHistory[]>([
    {
      id: "1",
      title: "How do I create a new assignment?",
      date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    },
    {
      id: "2",
      title: "Can you explain how grading works?",
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    },
    {
      id: "3",
      title: "Setting up the academic year",
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    },
  ]);

  const [activeChat, setActiveChat] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const [searchValue, setSearchValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const timeoutIDRef = useRef<NodeJS.Timeout | null>(null);
  const [isAtBottom, setIsAtBottom] = useState(true);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Add a state to track the latest message ID
  const [latestMessageId, setLatestMessageId] = useState<string | null>(null);

  // Add these state variables near your other state declarations
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteAllDialogOpen, setDeleteAllDialogOpen] = useState(false);
  const [chatToDelete, setChatToDelete] = useState<string | null>(null);

  // Track scroll position
  useEffect(() => {
    const handleScroll = () => {
      if (chatContainerRef.current) {
        const { scrollTop, scrollHeight, clientHeight } =
          chatContainerRef.current;
        const isBottom = scrollHeight - scrollTop - clientHeight < 100; // Within 100px of bottom
        setIsAtBottom(isBottom);
      }
    };

    const chatContainer = chatContainerRef.current;
    if (chatContainer) {
      chatContainer.addEventListener("scroll", handleScroll);
      return () => chatContainer.removeEventListener("scroll", handleScroll);
    }
  }, []);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current && isAtBottom) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages, isTyping, isAtBottom]);

  // Auto-resize textarea as user types
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.style.height = "56px";
      inputRef.current.style.height = `${Math.min(
        inputRef.current.scrollHeight,
        200,
      )}px`;
    }
  }, [inputValue]);

  const filteredChatHistories = chatHistories.filter((chat) =>
    chat.title.toLowerCase().includes(searchValue.toLowerCase()),
  );

  const handleNewChat = () => {
    setActiveChat(null);
    setMessages([]);
    inputRef.current?.focus();
  };

  const [chatMessages, setChatMessages] = useState<Record<string, Message[]>>({
    "1": [
      { id: "1-1", content: "How do I create a new assignment?", role: "user" },
      {
        id: "1-2",
        content:
          "To create a new assignment, navigate to the Assignments page...",
        role: "assistant",
      },
    ],
    "2": [
      {
        id: "2-1",
        content: "Can you explain how grading works?",
        role: "user",
      },
      {
        id: "2-2",
        content: "Grading in our system works by...",
        role: "assistant",
      },
    ],
    "3": [
      { id: "3-1", content: "Setting up the academic year", role: "user" },
      {
        id: "3-2",
        content: "To set up a new academic year...",
        role: "assistant",
      },
    ],
  });

  const handleChatSelect = (chatId: string) => {
    setActiveChat(chatId);
    setMessages(chatMessages[chatId] || []);
  };

  // Modify handleDeleteChat to open the confirmation dialog
  const handleDeleteChatWithConfirm = (chatId: string) => {
    setChatToDelete(chatId);
    setDeleteDialogOpen(true);
  };

  // Add this function to execute the actual deletion
  const confirmDeleteChat = () => {
    if (chatToDelete) {
      setChatHistories(
        chatHistories.filter((chat) => chat.id !== chatToDelete),
      );
      if (activeChat === chatToDelete) {
        setActiveChat(null);
        setMessages([]);
      }
      setChatToDelete(null);
    }
    setDeleteDialogOpen(false);
  };

  const handleDeleteAllConversations = () => {
    setChatHistories([]);
    setActiveChat(null);
    setMessages([]);
    toast.success(t("assistant.deletedAllConversations"));
  };

  const handleDeleteAllWithConfirm = () => {
    setDeleteAllDialogOpen(true);
  };

  const handleSendMessage = () => {
    if (!inputValue.trim() || isTyping) return;

    try {
      const newUserMessage: Message = {
        id: Date.now().toString(),
        content: inputValue,
        role: "user",
      };

      const updatedMessages = [...messages, newUserMessage];
      setMessages(updatedMessages);
      setInputValue("");

      if (!activeChat) {
        const newChatId = Date.now().toString();
        const newChat: ChatHistory = {
          id: newChatId,
          title:
            inputValue.length > 30
              ? inputValue.substring(0, 30) + "..."
              : inputValue,
          date: new Date(),
        };
        setChatHistories([newChat, ...chatHistories]);
        setActiveChat(newChatId);

        setChatMessages({
          ...chatMessages,
          [newChatId]: updatedMessages,
        });
      } else {
        setChatMessages({
          ...chatMessages,
          [activeChat]: updatedMessages,
        });
      }

      setIsTyping(true);
      timeoutIDRef.current = setTimeout(() => {
        try {
          // Your response generation logic
          // ...

          const assistantResponse: Message = {
            id: (Date.now() + 1).toString(),
            content:
              "I'm your E-ditar AI assistant. This is a simulated response. In a real implementation, this would be connected to an AI service.",
            role: "assistant",
          };

          setLatestMessageId(assistantResponse.id);

          setMessages((prev) => [...prev, assistantResponse]);

          if (activeChat) {
            setChatMessages((prev) => ({
              ...prev,
              [activeChat]: [...(prev[activeChat] || []), assistantResponse],
            }));
          }
        } catch (error) {
          console.error("Error generating response:", error);
          toast.error("Failed to generate response. Please try again.");
        } finally {
          setIsTyping(false);
        }
      }, 1000);
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("Failed to send message. Please try again.");
      setIsTyping(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Only process Enter key if not shift and not typing
    if (e.key === "Enter" && !e.shiftKey && !isTyping) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Group chat histories by date
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);

  const todayChats = filteredChatHistories.filter((chat) => {
    const chatDate = new Date(chat.date);
    chatDate.setHours(0, 0, 0, 0);
    return chatDate.getTime() === today.getTime();
  });

  const yesterdayChats = filteredChatHistories.filter((chat) => {
    const chatDate = new Date(chat.date);
    chatDate.setHours(0, 0, 0, 0);
    return chatDate.getTime() === yesterday.getTime();
  });

  const olderChats = filteredChatHistories.filter((chat) => {
    const chatDate = new Date(chat.date);
    chatDate.setHours(0, 0, 0, 0);
    return chatDate.getTime() < yesterday.getTime();
  });

  // Get suggested questions based on user role
  const getSuggestedQuestions = (role?: string) => {
    switch (role) {
      case "student":
        return [
          "How do I view my grades?",
          "Where can I find my class schedule?",
          "How do I submit an assignment?",
          "How do I contact my teacher?",
        ];
      case "teacher":
        return [
          "How do I create a new assignment?",
          "How do I record student attendance?",
          "How do I enter grades?",
          "How do I schedule a parent meeting?",
        ];
      case "parent":
        return [
          "How do I view my child's grades?",
          "How do I check attendance records?",
          "How do I message a teacher?",
          "How do I update contact information?",
        ];
      case "admin":
        return [
          "How do I add new users?",
          "How do I manage school calendar?",
          "How do I generate reports?",
          "How do I configure system settings?",
        ];
      default:
        return [
          "How do I get started?",
          "What features are available?",
          "How do I navigate the platform?",
          "Where can I find help resources?",
        ];
    }
  };

  const suggestedQuestions = getSuggestedQuestions(user?.role);

  // Helper function to get icons for suggestion cards
  const getSuggestionIcon = (index: number) => {
    const icons = [
      <BookOpen size={18} />,
      <Calendar size={18} />,
      <FileText size={18} />,
      <Users size={18} />,
    ];
    return icons[index % icons.length];
  };

  // Load chat histories from localStorage on component mount
  useEffect(() => {
    const savedHistories = localStorage.getItem("chatHistories");
    const savedMessages = localStorage.getItem("chatMessages");

    if (savedHistories) {
      try {
        const parsed = JSON.parse(savedHistories);
        // Convert date strings back to Date objects
        const historiesWithDates = parsed.map((history: Omit<ChatHistory, 'date'> & { date: string }) => ({
          ...history,
          date: new Date(history.date),
        }));
        setChatHistories(historiesWithDates);
      } catch (e) {
        console.error("Failed to parse chat histories:", e);
      }
    }

    if (savedMessages) {
      try {
        setChatMessages(JSON.parse(savedMessages));
      } catch (e) {
        console.error("Failed to parse chat messages:", e);
      }
    }
  }, []);

  // Save to localStorage when chat histories or messages change
  useEffect(() => {
    localStorage.setItem("chatHistories", JSON.stringify(chatHistories));
  }, [chatHistories]);

  useEffect(() => {
    localStorage.setItem("chatMessages", JSON.stringify(chatMessages));
  }, [chatMessages]);

  // Use this for real-time typing indicators
  const debouncedInputValue = useDebounce(inputValue, 300);

  useEffect(() => {
    if (debouncedInputValue && debouncedInputValue.length > 0) {
      // You could notify the backend that the user is typing
      // This is useful for real chat applications
    }
  }, [debouncedInputValue]);

  return (
    <>
      <GlobalStyle />
      <div className="flex h-[calc(94vh-80px)] bg-gray-50 overflow-hidden">
        {/* Sidebar with chat history - fixed height with independent scrolling */}
        <div className="w-72 bg-white border-r border-gray-200 flex flex-col h-full shadow-sm overflow-hidden">
          <div className="p-3 border-b border-gray-200 flex-shrink-0 flex justify-between items-center">
            <button
              onClick={handleNewChat}
              className="flex items-center justify-center gap-2 bg-purple-600 hover:bg-purple-700 text-white py-2 px-3 rounded-md transition-colors shadow-sm w-full"
            >
              <Plus size={16} />
              <span className="font-medium">{t("assistant.chatAddNew")}</span>
            </button>
          </div>

          <div className="flex-shrink-0">
            <div className="flex items-center gap-1 justify-between px-3 py-3">
              <SearchInput value={searchValue} onChange={setSearchValue} />

              {filteredChatHistories.length >= 2 && (
                <button
                  onClick={handleDeleteAllWithConfirm}
                  className="ml-2 flex items-center justify-center p-3 text-red-500 hover:text-red-600 bg-red-50 hover:bg-red-100 rounded-md transition-colors"
                  title={t("assistant.deleteAllChats")}
                  aria-label={t("assistant.deleteAllChats")}
                >
                  <Trash2 size={16} />
                </button>
              )}
            </div>
          </div>

          <div className="flex-1 overflow-y-auto px-2 ">
            {filteredChatHistories.length === 0 && searchValue && (
              <div className="text-center py-8 text-gray-500">
                <Search className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                <p className="text-sm">{t("assistant.noSearchResults")}</p>
                <p className="text-xs mt-1">
                  {" "}
                  {t("assistant.tryDifferentSearchTerm")}
                </p>
              </div>
            )}

            {filteredChatHistories.length === 0 && !searchValue && (
              <div className="text-center py-8 text-gray-500">
                <Bot className="h-12 w-12 mx-auto text-gray-300 mb-2" />
                <p className="text-sm">{t("assistant.noConversations")}</p>
                <p className="text-xs mt-1">{t("assistant.startNewChat")}</p>
              </div>
            )}

            {todayChats.length > 0 && (
              <div className="mt-3">
                <div className="px-3 mb-1">
                  <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t("Today")}
                  </h3>
                </div>
                {todayChats.map((chat) => (
                  <ChatHistoryItem
                    key={chat.id}
                    chat={chat}
                    isActive={activeChat === chat.id}
                    onClick={() => handleChatSelect(chat.id)}
                    onDelete={() => handleDeleteChatWithConfirm(chat.id)}
                  />
                ))}
              </div>
            )}

            {yesterdayChats.length > 0 && (
              <div className="mt-4">
                <div className="px-3 mb-1">
                  <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t("Yesterday")}
                  </h3>
                </div>
                {yesterdayChats.map((chat) => (
                  <ChatHistoryItem
                    key={chat.id}
                    chat={chat}
                    isActive={activeChat === chat.id}
                    onClick={() => handleChatSelect(chat.id)}
                    onDelete={() => handleDeleteChatWithConfirm(chat.id)}
                  />
                ))}
              </div>
            )}

            {olderChats.length > 0 && (
              <div className="mt-4">
                <div className="px-3 mb-1">
                  <h3 className="text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t("Previous 7 Days")}
                  </h3>
                </div>
                {olderChats.map((chat) => (
                  <ChatHistoryItem
                    key={chat.id}
                    chat={chat}
                    isActive={activeChat === chat.id}
                    onClick={() => handleChatSelect(chat.id)}
                    onDelete={() => handleDeleteChatWithConfirm(chat.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Main chat area - fixed layout with independent scrolling */}
        <div className="flex-1 flex flex-col h-full relative overflow-hidden">
          <div
            ref={chatContainerRef}
            className="flex-1 overflow-y-auto bg-gradient-to-b from-purple-50 to-white [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]"
          >
            {messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-6 h-full">
                <div className="w-16 h-16 rounded-full bg-purple-100 flex items-center justify-center mb-6 shadow-sm">
                  <Bot size={32} className="text-purple-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  {t("assistant.welcomeTitle")}
                </h2>
                <p className="text-gray-600 text-center max-w-md mb-10">
                  {t("assistant.welcomeMessage")}
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-3xl mb-10">
                  {/* Map all suggestion cards */}
                  {suggestedQuestions.map((question, index) => (
                    <SuggestionCard
                      key={index}
                      icon={getSuggestionIcon(index)}
                      title={question}
                      onClick={() => {
                        setInputValue(question);
                        setTimeout(() => {
                          inputRef.current?.focus();
                        }, 0);
                      }}
                    />
                  ))}
                </div>
              </div>
            ) : (
              <div className="p-6 pb-32">
                {" "}
                {/* Increased bottom padding from pb-20 to pb-32 */}
                <div className="w-full max-w-3xl mx-auto">
                  {messages.map((message) => (
                    <ChatMessage
                      key={message.id}
                      message={message}
                      user={user}
                      isNewMessage={message.id === latestMessageId}
                    />
                  ))}
                  {isTyping && (
                    <div className="flex justify-start mb-10">
                      {" "}
                      {/* Added margin bottom */}
                      <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm max-w-3xl">
                        <div className="flex space-x-2">
                          <div
                            className="w-2 h-2 bg-gray-300 rounded-full animate-bounce"
                            style={{ animationDelay: "0ms" }}
                          ></div>
                          <div
                            className="w-2 h-2 bg-gray-300 rounded-full animate-bounce"
                            style={{ animationDelay: "150ms" }}
                          ></div>
                          <div
                            className="w-2 h-2 bg-gray-300 rounded-full animate-bounce"
                            style={{ animationDelay: "300ms" }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} className="h-20" />{" "}
                </div>
              </div>
            )}
          </div>

          <div
            className={`absolute bottom-10 w-full max-w-4xl mx-auto left-0 right-0 p-4 bg-gradient-to-t from-white via-white to-transparent transition-opacity duration-300 ${
              isAtBottom ? "opacity-100" : "opacity-0 pointer-events-none"
            }`}
          >
            <MainSearchBar
              value={inputValue}
              onChange={setInputValue}
              onSend={handleSendMessage}
              onKeyDown={handleKeyDown}
              isTyping={isTyping}
            />
          </div>
        </div>
      </div>

      {/* Delete Single Chat Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("assistant.confirmDelete")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("assistant.confirmDeleteMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("assistant.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteChat}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("assistant.confirmDelete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete All Conversations Confirmation Dialog */}
      <AlertDialog
        open={deleteAllDialogOpen}
        onOpenChange={setDeleteAllDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {t("assistant.confirmDeleteAll")}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {t("assistant.confirmDeleteAllMessage")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("assistant.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                handleDeleteAllConversations();
                setDeleteAllDialogOpen(false);
              }}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {t("assistant.confirmDeleteAll")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
