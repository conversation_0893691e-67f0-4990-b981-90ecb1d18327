import { useTranslation } from "react-i18next";
import SubjectView from "./SubjectView";
import { Plus } from "lucide-react";
import { useState } from "react";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type GradeLevel = "All" | "6" | "7" | "8" | "9" | "10" | "11" | "12";
type SubjectStatus = "All" | "Active" | "Archived";

export default function DirectorSubjects() {
  const { t } = useTranslation();
  const [gradeFilter, setGradeFilter] = useState<GradeLevel>("All");
  const [statusFilter, setStatusFilter] = useState<SubjectStatus>("All");

  const addButton = (
    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 w-full sm:w-auto">
        {/* Grade Level Filter */}
        <Select
          value={gradeFilter}
          onValueChange={(value: GradeLevel) => setGradeFilter(value)}
        >
          <SelectTrigger className="w-full sm:w-[180px] bg-white mb-2 sm:mb-0">
            <SelectValue placeholder={t("subjects.allGrades")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">{t("subjects.allGrades")}</SelectItem>
            {["6", "7", "8", "9", "10", "11", "12"].map((grade) => (
              <SelectItem key={grade} value={grade}>
                {t("subjects.grade")} {grade}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Status Filter */}
        <Select
          value={statusFilter}
          onValueChange={(value: SubjectStatus) => setStatusFilter(value)}
        >
          <SelectTrigger className="w-full sm:w-[180px] bg-white">
            <SelectValue placeholder={t("subjects.allStatus")} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="All">{t("subjects.allStatus")}</SelectItem>
            <SelectItem value="Active">{t("subjects.active")}</SelectItem>
            <SelectItem value="Archived">{t("subjects.archived")}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Add Subject Button */}
      <div className="flex items-center bg-[#7B3AED] text-white px-4 py-2 rounded-md cursor-pointer hover:bg-[#9B87F5] w-full sm:w-auto justify-center sm:justify-start mt-2 sm:mt-0">
        <Plus className="w-4 h-4" />
        <p className="pl-3">{t("subjects.addNewSubject")}</p>
      </div>
    </div>
  );

  return (
    <div className="p-4">
      <SubjectView
        headerTitle={t("sidebar.subjects")}
        headerButton={addButton}
      />
    </div>
  );
}
