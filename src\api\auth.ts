import api from "./index";

interface LoginCredentials {
  username: string;
  password: string;
}

export const login = async (credentials: LoginCredentials) => {
  try {
    // Create URL-encoded form data for OAuth2PasswordRequestForm
    const formData = new URLSearchParams();
    formData.append("username", credentials.username);
    formData.append("password", credentials.password);

    const response = await api.post("/auth/login", formData, {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    });

    if (response.status === 200) {
      // Get user information using the access token
      const { access_token, refresh_token, expires_in } = response.data;

      try {
        const userResponse = await api.get("/auth/me", {
          headers: {
            Authorization: `Bearer ${access_token}`,
          },
        });

        if (userResponse.status === 200) {
          // Return combined data
          return {
            status: 200,
            data: {
              access_token,
              refresh_token,
              expires_in,
              user: userResponse.data,
            },
          };
        }
      } catch (userError) {
        console.error("Error fetching user data:", userError);
        // Return token data without user info if user fetch fails
        return {
          status: 200,
          data: {
            access_token,
            refresh_token,
            expires_in,
            user: null,
          },
        };
      }
    }

    return response;
  } catch (err) {
    console.error("Auth API error:", err);
    return {
      status: 401,
      data: {
        message: "Invalid credentials",
      },
    };
  }
};
