import TeacherAssignmentsPage from "./TeacherAssignmentsPage";
import StudentAssignmentsPage from "./StudentAssignmentsPage";
import DirectorAssignmentsPage from "./DirectorAssignmentsPage";
import { useTypedAuthUser } from "../../hooks/useAuth";

export default function AssignmentsPage() {
  const authUser = useTypedAuthUser();

  const normalizedRole = authUser?.role?.trim().toLowerCase();

  switch (normalizedRole) {
    case "student":
      return <StudentAssignmentsPage />;
    case "teacher":
      return <TeacherAssignmentsPage />;
    case "admin":
      return <DirectorAssignmentsPage />;
    default:
      console.warn("AssignmentsPage: Invalid or unknown role", {
        originalRole: authUser?.role,
        normalizedRole,
      });
      return (
        <div className="p-6 text-center">
          <h2 className="text-xl font-semibold text-red-600 mb-2">
            Invalid Role
          </h2>
          <p className="text-gray-600">
            Your account role "{authUser?.role}" is not recognized. Please
            contact support.
          </p>
        </div>
      );
  }
}
