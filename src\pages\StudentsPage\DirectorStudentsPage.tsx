// import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
// import { Badge } from "@/components/ui/badge";
// import { Button } from "@/components/ui/button";
// import {
//   Search,
//   Filter,
//   Plus,
//   EyeIcon,
//   Pencil,
//   Phone,
//   Mail,
//   User,
//   Calendar,
// } from "lucide-react";
// import { useTranslation } from "react-i18next";
// import { useMemo, useState, useEffect } from "react";

// import { studentsData } from "./StudentsMockData";

// export default function DirectorStudentsPage() {
//   const { t } = useTranslation();
//   const [query, setQuery] = useState("");
//   const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
//   const [isTablet, setIsTablet] = useState(
//     window.innerWidth >= 768 && window.innerWidth < 1024
//   );

//   // Handle responsive breakpoints
//   useEffect(() => {
//     const handleResize = () => {
//       setIsMobile(window.innerWidth < 768);
//       setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
//     };

//     window.addEventListener("resize", handleResize);
//     handleResize();
//     return () => window.removeEventListener("resize", handleResize);
//   }, []);

//   // Flatten studentsData, adding grade and section info to each student
//   const allStudents = useMemo(() => {
//     const students = [];
//     for (const grade in studentsData) {
//       const sections = studentsData[grade];
//       for (const section in sections) {
//         const studentsInSection = sections[section].map((student) => ({
//           ...student,
//           grade,
//           section,
//         }));
//         students.push(...studentsInSection);
//       }
//     }
//     return students;
//   }, []);

//   // Filter students by query
//   const filteredStudents = useMemo(() => {
//     if (!query) return allStudents;
//     const lowerQuery = query.toLowerCase();
//     return allStudents.filter(
//       ({ name, id, parent }) =>
//         name.toLowerCase().includes(lowerQuery) ||
//         id.toLowerCase().includes(lowerQuery) ||
//         parent.toLowerCase().includes(lowerQuery)
//     );
//   }, [query, allStudents]);

//   // StudentCard component for mobile/tablet view
//   const StudentCard = ({
//     student,
//     showGradeSection = false,
//   }: {
//     student: any;
//     showGradeSection?: boolean;
//   }) => (
//     <div className="bg-white border border-gray-300 rounded-lg p-4 mb-4 shadow-sm">
//       <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
//         <div className="flex-1">
//           <div className="flex items-center gap-2 mb-2">
//             <User className="w-4 h-4 text-gray-500" />
//             <h3 className="font-semibold text-lg">{student.name}</h3>
//             <Badge
//               variant={student.status === "active" ? "success" : "destructive"}
//               className="ml-auto sm:ml-0"
//             >
//               {student.status}
//             </Badge>
//           </div>

//           <div className="space-y-2 text-sm">
//             <div className="flex items-center gap-2">
//               <span className="font-medium text-gray-600">ID:</span>
//               <span>{student.id}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <Calendar className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.dateOfBirth")}:
//               </span>
//               <span>{student.dob}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <User className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.parent/guardian")}:
//               </span>
//               <span>{student.parent}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <Phone className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.phone")}:
//               </span>
//               <span>{student.phone}</span>
//             </div>

//             <div className="flex items-center gap-2">
//               <Mail className="w-4 h-4 text-gray-500" />
//               <span className="font-medium text-gray-600">
//                 {t("manageStudents.email")}:
//               </span>
//               <span className="truncate">{student.email}</span>
//             </div>

//             {showGradeSection && (
//               <>
//                 <div className="flex items-center gap-2">
//                   <span className="font-medium text-gray-600">
//                     {t("setup.grade")}:
//                   </span>
//                   <span>{student.grade.replace("grade", "")}</span>
//                 </div>
//                 <div className="flex items-center gap-2">
//                   <span className="font-medium text-gray-600">
//                     {t("manageStudents.section")}:
//                   </span>
//                   <span>{student.section}</span>
//                 </div>
//               </>
//             )}
//           </div>
//         </div>

//         <div className="flex gap-2 sm:flex-col sm:gap-1">
//           <Button variant="ghost" size="sm" className="flex-1 sm:flex-none">
//             <EyeIcon className="w-4 h-4 mr-2" />
//             <span className="sm:hidden">View</span>
//           </Button>
//           <Button variant="ghost" size="sm" className="flex-1 sm:flex-none">
//             <Pencil className="w-4 h-4 mr-2" />
//             <span className="sm:hidden">Edit</span>
//           </Button>
//         </div>
//       </div>
//     </div>
//   );

//   // Render students function - responsive table/cards
//   const renderStudents = (
//     students: any[],
//     sectionName: string,
//     showGradeSection = false
//   ) => (
//     <div className="border border-gray-300 rounded-md p-4 mb-6">
//       <div className="text-lg font-semibold mb-4">{sectionName}</div>

//       {/* Mobile/Tablet Card View */}
//       {isMobile || isTablet ? (
//         <div className="space-y-4">
//           {students.map((student, idx) => (
//             <StudentCard
//               key={idx}
//               student={student}
//               showGradeSection={showGradeSection}
//             />
//           ))}
//         </div>
//       ) : (
//         /* Desktop Table View */
//         <div className="overflow-x-auto">
//           <table className="min-w-full text-sm text-left">
//             <thead className="bg-gray-100">
//               <tr className="text-gray-400 ">
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.studentId")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {`${t("manageStudents.firstName")} ${t(
//                     "manageStudents.lastName"
//                   )}`}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.dateOfBirth")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.parent/guardian")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.phone")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.email")}
//                 </th>
//                 {showGradeSection && (
//                   <>
//                     <th className="px-4 py-2 font-normal">
//                       {t("setup.grade")}
//                     </th>
//                     <th className="px-4 py-2 font-normal">
//                       {t("manageStudents.section")}
//                     </th>
//                   </>
//                 )}
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.status")}
//                 </th>
//                 <th className="px-4 py-2 font-normal">
//                   {t("manageStudents.actions")}
//                 </th>
//               </tr>
//             </thead>
//             <tbody>
//               {students.map((student, idx) => (
//                 <tr key={idx} className="border-t">
//                   <td className="px-4 py-2 ">{student.id}</td>
//                   <td className="px-4 py-2 ">{student.name}</td>
//                   <td className="px-4 py-2 ">{student.dob}</td>
//                   <td className="px-4 py-2 ">{student.parent}</td>
//                   <td className="px-4 py-2 ">{student.phone}</td>
//                   <td className="px-4 py-2 ">{student.email}</td>
//                   {showGradeSection && (
//                     <>
//                       <td className="px-4 py-2 ">
//                         {student.grade.replace("grade", "")}
//                       </td>
//                       <td className="px-4 py-2 ">{student.section}</td>
//                     </>
//                   )}
//                   <td className="px-4 py-2 ">
//                     <Badge
//                       variant={
//                         student.status === "active" ? "success" : "destructive"
//                       }
//                     >
//                       {student.status}
//                     </Badge>
//                   </td>
//                   <td className="px-4 py-2 space-x-2">
//                     <Button variant="ghost" size="icon">
//                       <EyeIcon size={16} />
//                     </Button>
//                     <Button variant="ghost" size="icon">
//                       <Pencil size={16} />
//                     </Button>
//                   </td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>
//       )}
//     </div>
//   );

//   return (
//     <div className="w-full">
//       <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6">
//         <div className="flex-1">
//           <h2 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold leading-tight">
//             {t("manageStudents.title")}
//           </h2>
//           <p className="text-sm sm:text-base lg:text-lg text-gray-500 mt-1">
//             {t("manageStudents.description")}
//           </p>
//         </div>
//         <div className="flex flex-col xs:flex-row sm:flex-row items-stretch xs:items-center gap-2 sm:gap-3 flex-shrink-0">
//           <Button
//             variant="outline"
//             size="sm"
//             className="cursor-pointer text-xs sm:text-sm px-3 sm:px-4 py-2 h-8 sm:h-9"
//           >
//             <Filter className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
//             <span className="hidden xs:inline">
//               {t("manageStudents.filter")}
//             </span>
//             <span className="xs:hidden">Filter</span>
//           </Button>
//           <Button
//             size="sm"
//             className="bg-purple-600 hover:bg-purple-700 cursor-pointer text-xs sm:text-sm px-3 sm:px-4 py-2 h-8 sm:h-9"
//           >
//             <Plus className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2" />
//             <span className="hidden xs:inline">
//               {t("manageStudents.addStudent")}
//             </span>
//             <span className="xs:hidden">Add</span>
//           </Button>
//         </div>
//       </div>

//       <div className="mb-4 sm:mb-6 p-1.5 sm:p-2 rounded-md bg-white border border-gray-300">
//         <div className="w-full bg-white border rounded-md flex items-center gap-2 sm:gap-3 px-3 sm:px-4 py-2 sm:py-2.5">
//           <Search className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 flex-shrink-0" />
//           <input
//             type="text"
//             placeholder={t("manageStudents.searchPlaceholder")}
//             className="w-full bg-transparent outline-none text-sm sm:text-base placeholder:text-xs sm:placeholder:text-sm"
//             value={query}
//             onChange={(e) => setQuery(e.target.value)}
//           />
//         </div>
//       </div>

//       <div className="mb-6 grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-2 sm:gap-3 lg:gap-4">
//         <div className="p-3 sm:p-4 lg:p-5 rounded-md bg-white border border-gray-300 flex flex-col justify-between min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]">
//           <div className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-blue-500">
//             13
//           </div>
//           <div className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-tight">
//             {t("manageStudents.totalStudents")}
//           </div>
//         </div>
//         <div className="p-3 sm:p-4 lg:p-5 rounded-md bg-white border border-gray-300 flex flex-col justify-between min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]">
//           <div className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-green-500">
//             12
//           </div>
//           <div className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-tight">
//             {t("manageStudents.activeStudents")}
//           </div>
//         </div>
//         <div className="p-3 sm:p-4 lg:p-5 rounded-md bg-white border border-gray-300 flex flex-col justify-between min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]">
//           <div className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-purple-500">
//             3
//           </div>
//           <div className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-tight">
//             {t("manageStudents.totalClasses")}
//           </div>
//         </div>
//         <div className="p-3 sm:p-4 lg:p-5 rounded-md bg-white border border-gray-300 flex flex-col justify-between min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]">
//           <div className="text-lg sm:text-xl lg:text-2xl xl:text-3xl font-bold text-red-500">
//             5
//           </div>
//           <div className="text-xs sm:text-sm lg:text-base text-muted-foreground leading-tight">
//             {t("manageStudents.totalSections")}
//           </div>
//         </div>
//       </div>

//       {query ? (
//         filteredStudents.length > 0 ? (
//           renderStudents(
//             filteredStudents,
//             t("manageStudents.searchResults"),
//             true
//           )
//         ) : (
//           <div className="flex items-center justify-center h-64">
//             {" "}
//             <p className="text-center text-muted-foreground">
//               {t("manageStudents.noStudentsFound")}
//             </p>
//           </div>
//         )
//       ) : (
//         <Tabs defaultValue="grade1" className="w-full">
//           <div className="block sm:hidden">
//             <div className="relative mb-4">
//               <div className="overflow-x-auto scrollbar-hide pb-1">
//                 <TabsList className="bg-muted inline-flex h-9 items-center justify-start rounded-lg p-1 w-max min-w-full gap-0.5">
//                   {Object.keys(studentsData).map((gradeKey) => (
//                     <TabsTrigger
//                       key={gradeKey}
//                       value={gradeKey}
//                       className="flex-shrink-0 px-2.5 py-1.5 text-xs font-medium whitespace-nowrap min-w-[70px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
//                     >
//                       {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
//                     </TabsTrigger>
//                   ))}
//                 </TabsList>
//               </div>
//               {/* Scroll indicator shadows */}
//               <div className="absolute left-0 top-0 bottom-0 w-3 bg-gradient-to-r from-gray-50 to-transparent pointer-events-none z-10"></div>
//               <div className="absolute right-0 top-0 bottom-0 w-3 bg-gradient-to-l from-gray-50 to-transparent pointer-events-none z-10"></div>
//             </div>
//           </div>

//           {/* Small screens (640px - 768px): 2 rows with better spacing */}
//           <div className="hidden sm:block md:hidden">
//             <div className="mb-4">
//               <TabsList className="bg-muted w-full justify-center flex-wrap gap-2 p-2 min-h-[44px]">
//                 {Object.keys(studentsData).map((gradeKey) => (
//                   <TabsTrigger
//                     key={gradeKey}
//                     value={gradeKey}
//                     className="flex-shrink-0 px-4 py-2 text-sm font-medium whitespace-nowrap min-w-[90px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
//                   >
//                     {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
//                   </TabsTrigger>
//                 ))}
//               </TabsList>
//             </div>
//           </div>

//           {/* Medium screens (768px - 1024px): Optimized 2 rows */}
//           <div className="hidden md:block lg:hidden">
//             <div className="mb-4">
//               <TabsList className="bg-muted w-full justify-center flex-wrap gap-2.5 p-2.5 min-h-[46px]">
//                 {Object.keys(studentsData).map((gradeKey) => (
//                   <TabsTrigger
//                     key={gradeKey}
//                     value={gradeKey}
//                     className="flex-shrink-0 px-5 py-2.5 text-sm font-medium whitespace-nowrap min-w-[100px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
//                   >
//                     {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
//                   </TabsTrigger>
//                 ))}
//               </TabsList>
//             </div>
//           </div>

//           {/* Large screens (1024px - 1280px): Single row with flex distribution */}
//           <div className="hidden lg:block xl:hidden">
//             <div className="mb-4">
//               <TabsList className="bg-muted w-full justify-center gap-3 p-3 min-h-[48px]">
//                 {Object.keys(studentsData).map((gradeKey) => (
//                   <TabsTrigger
//                     key={gradeKey}
//                     value={gradeKey}
//                     className="flex-1 px-4 py-2.5 text-sm font-medium whitespace-nowrap min-w-[100px] max-w-[130px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
//                   >
//                     {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
//                   </TabsTrigger>
//                 ))}
//               </TabsList>
//             </div>
//           </div>

//           {/* Extra large screens (1280px - 1400px): Spacious single row */}
//           <div className="hidden xl:block 2xl:hidden">
//             <div className="mb-4">
//               <TabsList className="bg-muted w-full justify-center gap-4 p-3 min-h-[50px]">
//                 {Object.keys(studentsData).map((gradeKey) => (
//                   <TabsTrigger
//                     key={gradeKey}
//                     value={gradeKey}
//                     className="flex-1 px-5 py-3 text-sm font-medium whitespace-nowrap min-w-[110px] max-w-[140px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
//                   >
//                     {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
//                   </TabsTrigger>
//                 ))}
//               </TabsList>
//             </div>
//           </div>

//           {/* Ultra wide screens (1400px+): Premium layout */}
//           <div className="hidden 2xl:block">
//             <div className="mb-4">
//               <TabsList className="bg-muted w-full justify-center gap-5 p-4 min-h-[52px]">
//                 {Object.keys(studentsData).map((gradeKey) => (
//                   <TabsTrigger
//                     key={gradeKey}
//                     value={gradeKey}
//                     className="flex-1 px-6 py-3 text-base font-medium whitespace-nowrap min-w-[120px] max-w-[150px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
//                   >
//                     {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
//                   </TabsTrigger>
//                 ))}
//               </TabsList>
//             </div>
//           </div>

//           {Object.entries(studentsData).map(([gradeKey, sections]) => (
//             <TabsContent key={gradeKey} value={gradeKey}>
//               {Object.entries(sections).map(([sectionName, students]) =>
//                 students.length > 0
//                   ? renderStudents(
//                       students,
//                       `${t("setup.grade")} ${gradeKey.replace(
//                         "grade",
//                         ""
//                       )} - ${sectionName}`
//                     )
//                   : null
//               )}

//               {Object.values(sections).every(
//                 (students) => students.length === 0
//               ) && (
//                 <p className="text-center mt-6 text-muted-foreground">
//                   {t("manageStudents.noStudentsFound")}{" "}
//                 </p>
//               )}
//             </TabsContent>
//           ))}
//         </Tabs>
//       )}
//     </div>
//   );
// }
