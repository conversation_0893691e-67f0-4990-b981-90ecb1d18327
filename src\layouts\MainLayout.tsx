import { useTypedAuthUser } from "../hooks/useAuth";
import DirectorLayout from "./DirectorLayout";
import ParentLayout from "./ParentLayout";
import StudentLayout from "./StudentLayout";
import TeacherLayout from "./TeacherLayout";
import { Outlet, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";

export default function MainLayout() {
  const authUser = useTypedAuthUser();
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const location = useLocation();
  const isAssistantPage = location.pathname === "/assistant";

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth >= 768) {
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const getLayout = (children: any) => {
    const props = {
      toggleSidebar,
      isSidebarOpen: isOpen,
      isMobile,
      hideAssistant: isAssistantPage, // Pass this prop to child layouts
    };

    // Normalize role to lowercase for consistent comparison
    const normalizedRole = authUser?.role?.trim().toLowerCase();

    switch (normalizedRole) {
      case "student":
        return <StudentLayout {...props}>{children}</StudentLayout>;
      case "teacher":
        return <TeacherLayout {...props}>{children}</TeacherLayout>;
      case "parent":
        return <ParentLayout {...props}>{children}</ParentLayout>;
      case "admin":
        return <DirectorLayout {...props}>{children}</DirectorLayout>;
      default:
        console.warn("MainLayout: Invalid or unknown role", {
          originalRole: authUser?.role,
          normalizedRole,
        });
        return (
          <div className="p-6 text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-2">
              Invalid Role
            </h2>
            <p className="text-gray-600">
              Your account role "{authUser?.role}" is not recognized for layout
              selection. Please contact support.
            </p>
          </div>
        );
    }
  };

  return getLayout(<Outlet />);
}
