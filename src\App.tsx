import { Routes, Route } from "react-router-dom";
import ProtectedRoute from "./components/ProtectedRoute";
import LoginPage from "./pages/LoginPage";
import DashboardPage from "./pages/DashboardPage";
import WelcomePage from "./pages/WelcomePage";
import SchedulePage from "./pages/SchedulePage";
import SubjectsPage from "./pages/SubjectsPage";
import AchievementsPage from "./pages/AchievementsPage";
import AssignmentsPage from "./pages/AssignmentsPage";
import NotificationsPage from "./pages/NotificationsPage";
import ProgressPage from "./pages/ProgressPage";
import ForgotPassword from "./pages/ForgotPasswordPage";
import NotFoundPage from "./pages/NotFoundPage";
import MessagesPage from "./pages/MessagesPage";
import MainLayout from "./layouts/MainLayout";
import AttendancePage from "./pages/AttendancePage/AttendancePage";
import GradesPage from "./pages/GradesPage";
import StudentsPage from "./pages/StudentsPage";
import SettingsPage from "./pages/SettingsPage";
import CurriculumPage from "./pages/CurriculumPage";
import StaffMembersPage from "./pages/StaffMembersPage";
import ReportsPage from "./pages/ReportsPage";
import AnalyticsPage from "./pages/AnalyticsPage";
import { ToastProvider } from "./components/ui/toast-provider";
import SetupWizardPage from "./components/SetupWizard";
import { AssistantPage } from "./pages/AssistantPage/AssistantPage";
import LessonObservationPage from "./pages/LessonObservationPage";
import AboutPage from "./pages/AboutPage";
import FeaturesPage from "./pages/FeaturesPage";
import ContactPage from "./pages/ContactPage";
import PricingPage from "./pages/PricingPage";
import UnauthorizedPage from "./pages/UnauthorizedPage";
import DebugAuthPage from "./pages/DebugAuthPage/DebugAuthPage";

function App() {
  return (
    <>
      <ToastProvider />
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route path="/forgot-password" element={<ForgotPassword />} />
        <Route path="/" element={<WelcomePage />} />
        <Route path="/about" element={<AboutPage />} />
        <Route path="/features" element={<FeaturesPage />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/pricing" element={<PricingPage />} />
        <Route path="/unauthorized" element={<UnauthorizedPage />} />
        <Route path="*" element={<NotFoundPage />} />

        <Route element={<ProtectedRoute />}>
          <Route path="/setup" element={<SetupWizardPage />} />
          <Route element={<MainLayout />}>
            <Route path="/dashboard" element={<DashboardPage />} />
            <Route path="/schedule" element={<SchedulePage />} />
            <Route path="/subjects" element={<SubjectsPage />} />
            <Route path="/students" element={<StudentsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="/assignments" element={<AssignmentsPage />} />
            <Route path="/achievements" element={<AchievementsPage />} />
            <Route path="/notifications" element={<NotificationsPage />} />
            <Route path="/messages" element={<MessagesPage />} />
            <Route path="/progress" element={<ProgressPage />} />
            <Route path="/grades" element={<GradesPage />} />
            <Route path="/attendance" element={<AttendancePage />} />
            <Route path="/curriculum" element={<CurriculumPage />} />
            <Route path="/staff-members" element={<StaffMembersPage />} />
            <Route path="/reports" element={<ReportsPage />} />
            <Route path="/analytics" element={<AnalyticsPage />} />
            <Route path="/assistant" element={<AssistantPage />} />
            <Route
              path="/lesson-observation"
              element={<LessonObservationPage />}
            />
            <Route path="/debug-auth" element={<DebugAuthPage />} />
          </Route>
        </Route>
      </Routes>
    </>
  );
}

export default App;
