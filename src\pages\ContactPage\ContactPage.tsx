import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Mail, Phone, MapPin, Clock } from "lucide-react";
import Footer from "@/components/Footer/Footer";
import toast from "react-hot-toast";
import AccordionCard from "@/components/AccordionCard";
import PublicHeader from "@/components/PublicHeader";

export default function ContactPage() {
  const { t } = useTranslation();
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [isTablet, setIsTablet] = useState(
    window.innerWidth >= 640 && window.innerWidth < 1024
  );
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    institution: "",
    message: "",
  });

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 1024);
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial sizing
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success(t("contactPage.messageSent"));
    setFormData({
      fullName: "",
      email: "",
      phone: "",
      institution: "",
      message: "",
    });
  };

  return (
    <div className="bg-purple-50 min-h-screen cursor-default">
      <PublicHeader />
      <div
        className={`container mx-auto px-4 py-${
          isMobile ? "8" : "12"
        } max-w-6xl`}
      >
        <div className="text-center mb-12">
          <h1
            className={`${
              isMobile ? "text-3xl" : isTablet ? "text-3xl" : "text-4xl"
            } font-bold text-gray-900 mb-2`}
          >
            {t("contactPage.title")}
          </h1>
          <p className={`text-gray-600 ${isMobile ? "text-sm" : ""}`}>
            {t("contactPage.subtitle")}
          </p>
        </div>

        {/* Contact Form and Info */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="grid grid-cols-1 md:grid-cols-5">
            {/* Contact Form - 3 columns on desktop */}
            <div
              className={`p-${
                isMobile ? "4" : isTablet ? "6" : "8"
              } md:col-span-3`}
            >
              <h2
                className={`${isMobile ? "text-lg" : "text-xl"} font-bold mb-${
                  isMobile ? "4" : "6"
                }`}
              >
                {t("contactPage.formTitle")}
              </h2>
              <form onSubmit={handleSubmit}>
                <div
                  className={`grid grid-cols-1 ${
                    !isMobile ? "md:grid-cols-2" : ""
                  } gap-4 mb-4`}
                >
                  <div>
                    <label
                      className={`block ${
                        isMobile ? "text-xs" : "text-sm"
                      } font-medium text-gray-700 mb-1`}
                    >
                      {t("contactPage.fullName")}
                    </label>
                    <input
                      type="text"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleChange}
                      placeholder="John Doe"
                      className={`w-full px-3 py-${
                        isMobile ? "1.5" : "2"
                      } border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                        isMobile ? "text-sm" : ""
                      }`}
                      required
                    />
                  </div>
                  <div>
                    <label
                      className={`block ${
                        isMobile ? "text-xs" : "text-sm"
                      } font-medium text-gray-700 mb-1`}
                    >
                      {t("contactPage.email")}
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className={`w-full px-3 py-${
                        isMobile ? "1.5" : "2"
                      } border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                        isMobile ? "text-sm" : ""
                      }`}
                      required
                    />
                  </div>
                </div>

                <div
                  className={`grid grid-cols-1 ${
                    !isMobile ? "md:grid-cols-2" : ""
                  } gap-4 mb-4`}
                >
                  <div>
                    <label
                      className={`block ${
                        isMobile ? "text-xs" : "text-sm"
                      } font-medium text-gray-700 mb-1`}
                    >
                      {t("contactPage.phone")}
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="+****************"
                      className={`w-full px-3 py-${
                        isMobile ? "1.5" : "2"
                      } border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                        isMobile ? "text-sm" : ""
                      }`}
                    />
                  </div>
                  <div>
                    <label
                      className={`block ${
                        isMobile ? "text-xs" : "text-sm"
                      } font-medium text-gray-700 mb-1`}
                    >
                      {t("contactPage.institution")}
                    </label>
                    <input
                      type="text"
                      name="institution"
                      value={formData.institution}
                      onChange={handleChange}
                      placeholder="School or Organization"
                      className={`w-full px-3 py-${
                        isMobile ? "1.5" : "2"
                      } border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                        isMobile ? "text-sm" : ""
                      }`}
                    />
                  </div>
                </div>

                <div className="mb-4">
                  <label
                    className={`block ${
                      isMobile ? "text-xs" : "text-sm"
                    } font-medium text-gray-700 mb-1`}
                  >
                    {t("contactPage.message")}
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="How can we help you?"
                    rows={isMobile ? 4 : 5}
                    className={`w-full px-3 py-${
                      isMobile ? "1.5" : "2"
                    } border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                      isMobile ? "text-sm" : ""
                    }`}
                    required
                  ></textarea>
                </div>

                <button
                  type="submit"
                  className={`bg-purple-500 hover:bg-purple-600 text-white font-medium py-${
                    isMobile ? "1.5" : "2"
                  } px-${isMobile ? "4" : "6"} rounded-md transition ${
                    isMobile ? "text-sm" : ""
                  }`}
                >
                  {t("contactPage.sendMessage")}
                </button>
              </form>
            </div>

            {/* Contact Information - 2 columns on desktop */}
            <div
              className={`bg-purple-500 text-white p-${
                isMobile ? "4" : isTablet ? "6" : "8"
              } md:col-span-2`}
            >
              <h2
                className={`${isMobile ? "text-lg" : "text-xl"} font-bold mb-${
                  isMobile ? "4" : "6"
                }`}
              >
                {t("contactPage.infoTitle")}
              </h2>

              <div className={`space-y-${isMobile ? "4" : "6"}`}>
                <div className="flex items-start">
                  <Mail
                    className={`w-${isMobile ? "4" : "5"} h-${
                      isMobile ? "4" : "5"
                    } mr-3 mt-1`}
                  />
                  <div>
                    <h3 className={`font-medium ${isMobile ? "text-sm" : ""}`}>
                      {t("contactPage.emailLabel")}
                    </h3>
                    <p className={isMobile ? "text-sm" : ""}><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Phone
                    className={`w-${isMobile ? "4" : "5"} h-${
                      isMobile ? "4" : "5"
                    } mr-3 mt-1`}
                  />
                  <div>
                    <h3 className={`font-medium ${isMobile ? "text-sm" : ""}`}>
                      {t("contactPage.phoneLabel")}
                    </h3>
                    <p className={isMobile ? "text-sm" : ""}>
                      +****************
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <MapPin
                    className={`w-${isMobile ? "4" : "5"} h-${
                      isMobile ? "4" : "5"
                    } mr-3 mt-1`}
                  />
                  <div>
                    <h3 className={`font-medium ${isMobile ? "text-sm" : ""}`}>
                      {t("contactPage.addressLabel")}
                    </h3>
                    <p className={isMobile ? "text-sm" : ""}>
                      123 Education Lane
                    </p>
                    <p className={isMobile ? "text-sm" : ""}>
                      San Francisco, CA 94103
                    </p>
                    <p className={isMobile ? "text-sm" : ""}>United States</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock
                    className={`w-${isMobile ? "4" : "5"} h-${
                      isMobile ? "4" : "5"
                    } mr-3 mt-1`}
                  />
                  <div>
                    <h3 className={`font-medium ${isMobile ? "text-sm" : ""}`}>
                      {t("contactPage.officeHoursLabel")}
                    </h3>
                    <p className={isMobile ? "text-sm" : ""}>
                      {t("contactPage.weekdays")}
                    </p>
                    <p className={isMobile ? "text-sm" : ""}>
                      {t("contactPage.officeHours")}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <AccordionCard />

      <Footer />
    </div>
  );
}
