import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";

interface LessonOutcomeFormProps {
  lessonId: string;
  lessonTitle: string;
  onSubmit: (outcomes: LessonOutcomes) => void;
  onCancel: () => void;
  initialData?: LessonOutcomes;
}

export interface LessonOutcomes {
  lessonId: string;
  objective: string;
  knowledge: string[];
  skills: string[];
  engagement: string[];
  assessment: string[];
  techniques: string[];
  comprehensionRate: number;
}

export default function LessonOutcomeForm({
  lessonId,
  lessonTitle,
  onSubmit,
  onCancel,
  initialData,
}: LessonOutcomeFormProps) {
  const { t } = useTranslation();

  const [outcomes, setOutcomes] = useState<LessonOutcomes>(
    initialData || {
      lessonId,
      objective: "",
      knowledge: [""],
      skills: [""],
      engagement: [""],
      assessment: [""],
      techniques: [""],
      comprehensionRate: 70,
    }
  );

  const handleTextChange = (
    category: keyof LessonOutcomes,
    value: string | string[] | number
  ) => {
    setOutcomes({ ...outcomes, [category]: value });
  };

  const handleArrayAsTextChange = (
    category:
      | "knowledge"
      | "skills"
      | "engagement"
      | "assessment"
      | "techniques",
    value: string
  ) => {
    const items = value.split("\n").filter((item) => item.trim() !== "");
    setOutcomes({ ...outcomes, [category]: items });
  };

  const getArrayAsText = (
    category:
      | "knowledge"
      | "skills"
      | "engagement"
      | "assessment"
      | "techniques"
  ): string => {
    return outcomes[category].join("\n");
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(outcomes);
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-4 sm:space-y-5 lg:space-y-6 max-h-[90vh] sm:max-h-none overflow-y-auto sm:overflow-visible"
    >
      <div>
        <h2 className="font-bold text-lg sm:text-xl mb-2 sm:mb-3 lg:mb-4">
          {t("lessonOutcomes.formTitle")}: {lessonTitle}
        </h2>
        <p className="text-gray-500 text-xs sm:text-sm mb-3 sm:mb-4 lg:mb-5">
          {t("lessonOutcomes.formDescription")}
        </p>
      </div>

      <div className="space-y-3 sm:space-y-4 lg:space-y-5">
        <div>
          <Label htmlFor="objective" className="text-sm sm:text-base">
            {t("lessonOutcomes.objective")}
          </Label>
          <Textarea
            id="objective"
            placeholder={t("lessonOutcomes.objectivePlaceholder")}
            value={outcomes.objective}
            onChange={(e) => handleTextChange("objective", e.target.value)}
            className="mt-1 sm:mt-2"
            rows={2}
            style={{ minHeight: "60px" }}
          />
        </div>

        {/* Knowledge section */}
        <div className="border rounded-md p-2.5 sm:p-3 lg:p-4">
          <Label
            className="font-medium text-sm sm:text-base"
            htmlFor="knowledge"
          >
            {t("lessonOutcomes.knowledge")}
          </Label>
          <p className="text-xs sm:text-sm text-gray-500 mb-1.5 sm:mb-2">
            {t("lessonOutcomes.knowledgeDescription")}
          </p>
          <Textarea
            id="knowledge"
            placeholder={t("lessonOutcomes.knowledgeItemPlaceholder")}
            value={getArrayAsText("knowledge")}
            onChange={(e) =>
              handleArrayAsTextChange("knowledge", e.target.value)
            }
            className="mt-1 sm:mt-2"
            rows={2}
            style={{ minHeight: "60px" }}
          />
        </div>

        {/* Skills section */}
        <div className="border rounded-md p-2.5 sm:p-3 lg:p-4">
          <Label className="font-medium text-sm sm:text-base" htmlFor="skills">
            {t("lessonOutcomes.skills")}
          </Label>
          <p className="text-xs sm:text-sm text-gray-500 mb-1.5 sm:mb-2">
            {t("lessonOutcomes.skillsDescription")}
          </p>
          <Textarea
            id="skills"
            placeholder={t("lessonOutcomes.skillsItemPlaceholder")}
            value={getArrayAsText("skills")}
            onChange={(e) => handleArrayAsTextChange("skills", e.target.value)}
            className="mt-1 sm:mt-2"
            rows={2}
            style={{ minHeight: "60px" }}
          />
        </div>

        {/* Engagement section */}
        <div className="border rounded-md p-2.5 sm:p-3 lg:p-4">
          <Label
            className="font-medium text-sm sm:text-base"
            htmlFor="engagement"
          >
            {t("lessonOutcomes.engagement")}
          </Label>
          <p className="text-xs sm:text-sm text-gray-500 mb-1.5 sm:mb-2">
            {t("lessonOutcomes.engagementDescription")}
          </p>
          <Textarea
            id="engagement"
            placeholder={t("lessonOutcomes.engagementItemPlaceholder")}
            value={getArrayAsText("engagement")}
            onChange={(e) =>
              handleArrayAsTextChange("engagement", e.target.value)
            }
            className="mt-1 sm:mt-2"
            rows={2}
            style={{ minHeight: "60px" }}
          />
        </div>

        {/* Assessment section */}
        <div className="border rounded-md p-2.5 sm:p-3 lg:p-4">
          <Label
            className="font-medium text-sm sm:text-base"
            htmlFor="assessment"
          >
            {t("lessonOutcomes.assessment")}
          </Label>
          <p className="text-xs sm:text-sm text-gray-500 mb-1.5 sm:mb-2">
            {t("lessonOutcomes.assessmentDescription")}
          </p>
          <Textarea
            id="assessment"
            placeholder={t("lessonOutcomes.assessmentItemPlaceholder")}
            value={getArrayAsText("assessment")}
            onChange={(e) =>
              handleArrayAsTextChange("assessment", e.target.value)
            }
            className="mt-1 sm:mt-2"
            rows={2}
            style={{ minHeight: "60px" }}
          />
        </div>

        {/* Common Techniques section */}
        <div className="border rounded-md p-2.5 sm:p-3 lg:p-4">
          <Label
            className="font-medium text-sm sm:text-base"
            htmlFor="techniques"
          >
            {t("lessonOutcomes.commonTechniques")}
          </Label>
          <p className="text-xs sm:text-sm text-gray-500 mb-1.5 sm:mb-2">
            {t("lessonOutcomes.techniquesDescription")}
          </p>
          <Textarea
            id="techniques"
            placeholder={t("lessonOutcomes.techniquesPlaceholder")}
            value={getArrayAsText("techniques")}
            onChange={(e) =>
              handleArrayAsTextChange("techniques", e.target.value)
            }
            className="mt-1 sm:mt-2"
            rows={2}
            style={{ minHeight: "60px" }}
          />
        </div>

        <div className="mt-2 sm:mt-3">
          <Label
            htmlFor="comprehension"
            className="font-medium text-sm sm:text-base"
          >
            {t("lessonOutcomes.comprehensionRate")}:{" "}
            <span className="text-purple-600 font-semibold">
              {outcomes.comprehensionRate}%
            </span>
          </Label>
          <Slider
            id="comprehension"
            min={0}
            max={100}
            step={5}
            value={[outcomes.comprehensionRate]}
            onValueChange={(value) =>
              handleTextChange("comprehensionRate", value[0])
            }
            className="mt-2"
            style={
              {
                "--slider-track": "#e9d5ff",
                "--slider-fill": "#9333ea",
                "--slider-thumb": "#9333ea",
              } as React.CSSProperties
            }
          />
        </div>
      </div>

      <div className="flex justify-end gap-2 sm:gap-3 mt-3 sm:mt-4 lg:mt-5">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          className="h-8 sm:h-9 text-xs sm:text-sm px-3 py-1.5 sm:px-4 sm:py-2"
        >
          {t("cancel")}
        </Button>
        <Button
          className="bg-purple-600 text-white hover:bg-purple-700 h-8 sm:h-9 text-xs sm:text-sm px-3 py-1.5 sm:px-4 sm:py-2"
          type="submit"
        >
          {t("save")}
        </Button>
      </div>
    </form>
  );
}
