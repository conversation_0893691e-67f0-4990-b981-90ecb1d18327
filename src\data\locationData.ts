// Mock data for cities and villages
// This data represents common cities and villages in Kosovo/Albania region

export interface LocationOption {
  value: string;
  label: string;
}

export const mockCities: LocationOption[] = [
  { value: "pristina", label: "Pristina" },
  { value: "prizren", label: "Prizren" },
  { value: "peja", label: "Pej<PERSON>" },
  { value: "ferizaj", label: "Ferizaj" },
  { value: "gjakova", label: "Gjakova" },
  { value: "gjilan", label: "Gjilan" },
  { value: "mitrovica", label: "<PERSON><PERSON><PERSON><PERSON>" },
  { value: "vushtrri", label: "Vushtr<PERSON>" },
  { value: "podujeva", label: "Podujeva" },
  { value: "suhareka", label: "<PERSON><PERSON><PERSON>" },
  { value: "rahovec", label: "Rahov<PERSON>" },
  { value: "malisheva", label: "Malisheva" },
  { value: "lipjan", label: "Lipjan" },
  { value: "kamenica", label: "Kamen<PERSON>" },
  { value: "viti", label: "Viti" },
  { value: "klina", label: "Klina" },
  { value: "istog", label: "Istog" },
  { value: "skenderaj", label: "Skenderaj" },
  { value: "drenas", label: "Drenas" },
  { value: "kacanik", label: "Kaçanik" },
  { value: "shtime", label: "Shtime" },
  { value: "fushe-kosova", label: "Fushë Kosova" },
  { value: "obiliq", label: "Obiliq" },
  { value: "novoberde", label: "Novobërdë" },
  { value: "gracanica", label: "Graçanica" },
  { value: "hani-elezit", label: "Hani i Elezit" },
  { value: "mamusa", label: "Mamushë" },
  { value: "junik", label: "Junik" },
  { value: "decan", label: "Deçan" },
  { value: "dragash", label: "Dragash" },
].sort((a, b) => a.label.localeCompare(b.label));

export const mockVillages: LocationOption[] = [
  { value: "batlava", label: "Batlava" },
  { value: "bresje", label: "Bresje" },
  { value: "carraleva", label: "Carraleva" },
  { value: "dardania", label: "Dardania" },
  { value: "elez-han", label: "Elez Han" },
  { value: "fushë-kosova", label: "Fushë Kosova" },
  { value: "gërmia", label: "Gërmia" },
  { value: "hajvalia", label: "Hajvalia" },
  { value: "isniqi", label: "Isniqi" },
  { value: "janjeva", label: "Janjeva" },
  { value: "kllokot", label: "Kllokot" },
  { value: "llapi", label: "Llapi" },
  { value: "mramor", label: "Mramor" },
  { value: "novo-brdo", label: "Novo Brdo" },
  { value: "obiliq", label: "Obiliq" },
  { value: "partesh", label: "Partesh" },
  { value: "qikatova", label: "Qikatova" },
  { value: "ranillug", label: "Ranillug" },
  { value: "strpce", label: "Strpce" },
  { value: "tërpeza", label: "Tërpeza" },
  { value: "ujmani", label: "Ujmani" },
  { value: "vranidoll", label: "Vranidoll" },
  { value: "xerxe", label: "Xerxe" },
  { value: "yzeiri", label: "Yzeiri" },
  { value: "zllakuqan", label: "Zllakuqan" },
  { value: "babush", label: "Babush" },
  { value: "çagllavica", label: "Çagllavica" },
  { value: "dobërdol", label: "Dobërdol" },
  { value: "gadime", label: "Gadime" },
  { value: "janjevë", label: "Janjevë" },
  { value: "kuzmin", label: "Kuzmin" },
  { value: "lipljan", label: "Lipljan" },
  { value: "mazgit", label: "Mazgit" },
  { value: "nekodim", label: "Nekodim" },
  { value: "obiliq-i-ri", label: "Obiliq i Ri" },
  { value: "plemetin", label: "Plemetin" },
  { value: "qikatovë", label: "Qikatovë" },
  { value: "ratkoc", label: "Ratkoc" },
  { value: "slivova", label: "Slivova" },
  { value: "talinovac", label: "Talinovac" },
  { value: "ugljare", label: "Ugljare" },
  { value: "vranjevac", label: "Vranjevac" },
  { value: "xerxe-e-eperme", label: "Xerxe e Epërme" },
  { value: "yzeiri-i-vogel", label: "Yzeiri i Vogël" },
  { value: "zllatar", label: "Zllatar" },
].sort((a, b) => a.label.localeCompare(b.label));
