import { useState, useEffect } from "react";
import LessonObservationForm from "@/components/LessonObservationForm/LessonObservationForm";
import { useTranslation } from "react-i18next";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";
import { toast } from "react-hot-toast";
import { LessonObservation } from "@/components/LessonObservationForm/LessonObservationForm";
import { Button } from "@/components/ui/button";
import { Printer } from "lucide-react";

const mockTeachers = [
  { id: "1", name: "<PERSON>", subject: "Mathematics" },
  { id: "2", name: "<PERSON>", subject: "Physics" },
  { id: "3", name: "<PERSON>", subject: "Biology" },
];

const mockObservationHistory = [
  { id: "1", teacherId: "1", date: "2023-05-15", subject: "Mathematics" },
  { id: "2", teacherId: "2", date: "2023-06-10", subject: "Physics" },
];

export default function LessonObservationPage() {
  const { t } = useTranslation();

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTeacher, setSelectedTeacher] = useState<any>(null);
  const [observationHistory, setObservationHistory] = useState<any[]>([]);
  const [draftObservation, setDraftObservation] =
    useState<LessonObservation | null>(null);

  const filteredTeachers = mockTeachers.filter((teacher) =>
    teacher.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    const savedDraft = localStorage.getItem("observationDraft");
    if (savedDraft) {
      try {
        const parsedDraft = JSON.parse(savedDraft);
        setDraftObservation(parsedDraft);
      } catch (e) {
        console.error("Failed to parse saved draft");
      }
    }
  }, []);

  useEffect(() => {
    if (selectedTeacher) {
      // In a real app, you would fetch this from your API
      const teacherHistory = mockObservationHistory.filter(
        (obs) => obs.teacherId === selectedTeacher.id
      );
      setObservationHistory(teacherHistory);
    }
  }, [selectedTeacher]);

  const handleTeacherSelect = (teacher: any) => {
    setSelectedTeacher(teacher);
  };

  const handleFormSubmit = (formData: LessonObservation) => {
    if (!selectedTeacher) {
      toast.error(t("Please select a teacher first"));
      return;
    }

    // Validate all required fields
    if (!formData.teacherName.trim()) {
      toast.error(t("validation.teacherNameRequired"));
      return;
    }

    if (!formData.subject.trim()) {
      toast.error(t("validation.subjectRequired"));
      return;
    }

    if (!formData.class.trim()) {
      toast.error(t("validation.classRequired"));
      return;
    }

    if (!formData.period.trim()) {
      toast.error(t("validation.periodRequired"));
      return;
    }

    if (!formData.hour.trim()) {
      toast.error(t("validation.hourRequired"));
      return;
    }

    if (!formData.schoolYear.trim()) {
      toast.error(t("validation.schoolYearRequired"));
      return;
    }

    if (!formData.observationPurpose.trim()) {
      toast.error(t("validation.observationPurposeRequired"));
      return;
    }

    if (!formData.lessonTopic.trim()) {
      toast.error(t("validation.lessonTopicRequired"));
      return;
    }

    if (!formData.lessonType.trim()) {
      toast.error(t("validation.lessonTypeRequired"));
      return;
    }

    if (!formData.directorName.trim()) {
      toast.error(t("validation.directorNameRequired"));
      return;
    }

    if (!formData.directorSignature.trim()) {
      toast.error(t("validation.directorSignatureRequired"));
      return;
    }

    if (!formData.teacherSignature.trim()) {
      toast.error(t("validation.teacherSignatureRequired"));
      return;
    }

    const observationData = {
      ...formData,
      teacherId: selectedTeacher.id,
    };

    console.log("Submitting observation:", observationData);

    // Here you would typically make an API call to save the observation
    // For now, we'll just simulate a successful submission

    // Add to observation history
    const newObservation = {
      id: Date.now().toString(),
      teacher: selectedTeacher.name,
      ...formData,
    };

    setObservationHistory([newObservation, ...observationHistory]);

    // Clear draft after successful submission
    localStorage.removeItem("observationDraft");
    setDraftObservation(null);

    toast.success(t("Observation saved successfully"));
  };

  const handleSaveDraft = (formData: LessonObservation) => {
    const draftData = {
      ...formData,
      teacherId: selectedTeacher?.id || "",
    };

    localStorage.setItem("observationDraft", JSON.stringify(draftData));
    setDraftObservation(draftData);
    toast.success(t("Draft saved"));
  };

  const handlePrint = () => {
    localStorage.setItem(
      "printFormData",
      JSON.stringify({
        ...draftObservation,
        teacherName: selectedTeacher.name,
        subject: selectedTeacher.subject,
        date: new Date(),
      })
    );

    window.open("/print-observation-form", "_blank");
  };

  return (
    <div className=" mx-auto h-[calc(100vh-80px)] print-container">
      <div className="flex  gap-6 overflow-hidden">
        {/* Left side: Teacher selection section */}
        <div className="w-1/3 flex flex-col overflow-y-auto scrollbar-hide pr-2">
          <div className="mb-6 ">
            <h1 className="text-3xl font-bold">
              {t("lessonObservation.title")}
            </h1>
            <p className="text-gray-500 mt-2 max-w-sm">
              {t("lessonObservation.formDescription")}
            </p>
          </div>
          <div className="bg-white rounded-md border border-gray-300 p-6 mb-6 flex-shrink-0">
            <h2 className="text-xl font-semibold mb-4">
              {t("lessonObservation.selectTeacher")}
            </h2>

            <div className="relative mb-4">
              <Input
                type="text"
                placeholder={t("lessonObservation.searchTeacher")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
              <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
            </div>

            <div className="grid grid-cols-1 gap-3 mb-4">
              {filteredTeachers.map((teacher) => (
                <div
                  key={teacher.id}
                  className={`p-3 border rounded-md cursor-pointer transition-colors ${
                    selectedTeacher?.id === teacher.id
                      ? "border-purple-500 bg-purple-50"
                      : "border-gray-200 hover:border-purple-300"
                  }`}
                  onClick={() => handleTeacherSelect(teacher)}
                >
                  <p className="font-medium">{teacher.name}</p>
                  <p className="text-sm text-gray-500">{teacher.subject}</p>
                </div>
              ))}
            </div>
          </div>

          {selectedTeacher && (
            <div className="bg-white rounded-md border border-gray-300 p-6 flex-shrink-0">
              <h3 className="text-lg font-medium mb-2">
                {t("Observation History")}
              </h3>
              {observationHistory.length > 0 ? (
                <div className="space-y-2">
                  {observationHistory.map((obs) => (
                    <div
                      key={obs.id}
                      className="p-2 bg-gray-50 rounded border border-gray-200"
                    >
                      <p>
                        {obs.date} - {obs.subject}
                      </p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">{t("No previous observations")}</p>
              )}
            </div>
          )}
        </div>

        <div className="w-2/3 bg-white rounded-md border border-gray-300 flex-1 overflow-hidden">
          <div className="bg-white p-10">
            {selectedTeacher && (
              <div className="flex justify-end pb-10 gap-2 no-print">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrint}
                  className="flex items-center gap-2 no-print"
                >
                  <Printer className="h-4 w-4" />
                  {t("lessonObservation.printForm")}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    draftObservation && handleSaveDraft(draftObservation)
                  }
                  className="border-purple-300 text-purple-700 hover:bg-purple-50"
                  disabled={!selectedTeacher}
                >
                  {t("lessonObservation.saveDraft")}
                </Button>
                <Button
                  type="button"
                  onClick={() =>
                    document.getElementById("observation-form")?.dispatchEvent(
                      new Event("submit", {
                        bubbles: true,
                        cancelable: true,
                      })
                    )
                  }
                  className="bg-purple-600 hover:bg-purple-700"
                  disabled={!selectedTeacher}
                >
                  {t("lessonObservation.saveForm")}
                </Button>
              </div>
            )}

            {selectedTeacher ? (
              <LessonObservationForm
                initialData={draftObservation}
                onSubmit={handleFormSubmit}
                onSaveDraft={handleSaveDraft}
                selectedTeacher={selectedTeacher}
                hideActions={true}
                formId="observation-form"
              />
            ) : (
              <div className="h-full  flex items-center justify-center bg-purple-50 border border-purple-200 rounded-lg p-6 text-center">
                <p className="text-purple-700">
                  {t("lessonObservation.selectTeacherStartObservation")}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
