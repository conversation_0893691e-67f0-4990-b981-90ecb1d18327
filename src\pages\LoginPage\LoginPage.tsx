import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import useSignIn from "react-auth-kit/hooks/useSignIn";
import useIsAuthenticated from "react-auth-kit/hooks/useIsAuthenticated";
import { login } from "../../api/auth.ts";
import { Eye, EyeOff } from "lucide-react";
import { FcGoogle } from "react-icons/fc";
import { SlGraduation } from "react-icons/sl";

import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import { validateEmail, validatePassword } from "../../lib/validation";
import { LoadingSpinner } from "../../components/ui/loading-spinner";

export default function LoginPage() {
  const navigate = useNavigate();
  const signIn = useSignIn();
  const isAuthenticated = useIsAuthenticated();
  const { t, i18n } = useTranslation();

  const [formData, setFormData] = useState({ email: "", password: "" });
  const [error, setError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard", { replace: true });
    }
    setCheckingAuth(false);
  }, [isAuthenticated, navigate]);

  const handleChange = (e: any) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();

    // Validate inputs
    if (!validateEmail(formData.email)) {
      toast.error(t("validation.invalidEmail"));
      return;
    }

    if (!validatePassword(formData.password)) {
      toast.error(t("invalidPassword"));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await login({
        username: formData.email,
        password: formData.password,
      });

      if (response?.status === 200) {
        const { user, access_token, refresh_token } = response.data;

        if (!user) {
          throw new Error("User information not available");
        }

        if (!user.role) {
          navigate("/unauthorized", { replace: true });
          return;
        }

        const userState = {
          id: user.id,
          email: user.email,
          name: `${user.first_name} ${user.last_name}`.trim(),
          first_name: user.first_name,
          last_name: user.last_name,
          role: user.role?.trim(),
          permissions: user.permissions || [],
          scopes: user.permissions || [],
          organization: user.organization,
          personal_id: user.personal_id,
          gender: user.gender,
          is_identity_verified: user.is_identity_verified,
          can_login: user.can_login,
        };

        // Sign in with react-auth-kit
        const signInSuccess = signIn({
          auth: {
            token: access_token,
            type: "Bearer",
          },
          refresh: refresh_token,
          userState,
        });

        if (signInSuccess) {
          toast.success(t("loginSuccess"));
          navigate("/dashboard", { replace: true });
        } else {
          throw new Error("Failed to sign in");
        }
      } else {
        setError(response?.data?.message || "Invalid credentials");
        toast.error(response?.data?.message || t("invalidCredentials"));
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("Login failed. Please try again.");
      toast.error("Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const changeLanguage = (lng: any) => {
    i18n.changeLanguage(lng);
    localStorage.setItem("language", lng);
  };

  if (checkingAuth) {
    return (
      <div
        className="min-h-screen flex flex-col items-center justify-center"
        style={{
          background:
            "linear-gradient(-45deg, #7B3AED, #9B87F5, #D8B4FE, #F3E8FF)",
          backgroundSize: "400% 400%",
          animation: "gradient 15s ease infinite",
        }}
      >
        <LoadingSpinner
          text="Loading E-ditar..."
          size="large"
          showIcons={true}
        />
      </div>
    );
  }

  return (
    <div
      className="min-h-screen flex items-center justify-center px-4 relative overflow-hidden cursor-default"
      style={{
        background:
          "linear-gradient(-45deg, #7B3AED, #9B87F5, #D8B4FE, #F3E8FF)",
        backgroundSize: "400% 400%",
        animation: "gradient 15s ease infinite",
      }}
    >
      <div className="bg-white rounded-2xl shadow-lg p-4 sm:p-6 md:p-10 max-w-md w-full transition-all relative z-10">
        <div className="flex flex-col items-center gap-1 sm:gap-2 md:gap-3 mb-3 sm:mb-4 md:mb-6">
          <div className="bg-purple-100 p-2 sm:p-3 md:p-4 rounded-full">
            <SlGraduation className="text-purple-600" size={20} />
          </div>
          <h1 className="text-xl sm:text-2xl md:text-3xl font-semibold text-purple-700">
            {t("welcomeTo")}
          </h1>
          <p className="text-xs md:text-sm text-gray-500 text-center">
            {t("welcomeDesc")}
          </p>
        </div>

        <form
          onSubmit={handleSubmit}
          className="space-y-2 sm:space-y-3 md:space-y-4"
        >
          <div>
            <label
              htmlFor="email"
              className="text-xs md:text-sm font-medium text-gray-600 block mb-0.5 md:mb-1"
            >
              {t("email")}
            </label>
            <input
              type="email"
              name="email"
              required
              value={formData.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
              className="w-full px-2 py-1 sm:px-3 sm:py-1.5 md:px-4 md:py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 outline-none transition text-xs sm:text-sm"
            />
          </div>

          <div>
            <label className="text-xs md:text-sm font-medium text-gray-600 block mb-0.5 md:mb-1">
              {t("password")}
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                required
                value={formData.password}
                onChange={handleChange}
                placeholder="••••••••"
                className="w-full px-2 py-1 sm:px-3 sm:py-1.5 md:px-4 md:py-2 pr-8 sm:pr-10 md:pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-400 outline-none transition text-xs sm:text-sm"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 flex items-center justify-center w-8 sm:w-10 md:w-12 text-gray-500 hover:text-gray-700 transition-colors focus:outline-none  rounded-r-lg"
                aria-label={showPassword ? "Hide password" : "Show password"}
                tabIndex={0}
              >
                {showPassword ? (
                  <Eye className="w-4 h-4" />
                ) : (
                  <EyeOff className="w-4 h-4" />
                )}
              </button>
            </div>
          </div>

          <div className="flex justify-end">
            <p
              onClick={() => navigate("/forgot-password")}
              className="text-xs sm:text-sm text-purple-500 cursor-pointer hover:underline"
            >
              {t("forgotPassword")}
            </p>
          </div>

          {error && (
            <p className="text-xs sm:text-sm text-red-500 text-center">
              {error}
            </p>
          )}

          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-purple-600 text-white py-1.5 sm:py-2 rounded-lg hover:bg-purple-700 transition font-medium text-xs sm:text-sm disabled:opacity-50"
          >
            {isLoading ? t("signingIn") : t("signIn")}
          </button>
        </form>

        <div className="relative my-4 sm:my-6 text-center">
          <span className="text-xs sm:text-sm text-gray-500 px-2 bg-white z-10 relative">
            {t("continueWith")}
          </span>
          <div className="absolute top-1/2 left-0 w-full h-px bg-gray-300 -z-0" />
        </div>

        <div className="flex items-center justify-center p-2 sm:p-3 border border-gray-300 w-full rounded-md mb-6 sm:mb-10 cursor-pointer hover:bg-slate-50">
          <FcGoogle size={16} className="sm:text-lg" />
          <p className="pl-2 sm:pl-3 text-xs sm:text-sm">
            {t("signinWithGoogle")}
          </p>
        </div>

        <div className="flex justify-center text-xs">
          <button
            className={`text-center w-full border-r-2 p-1.5 sm:p-2 border-gray-300 ${
              i18n.language === "sq"
                ? " text-purple-700 font-bold"
                : "text-gray-600"
            }`}
            onClick={() => changeLanguage("sq")}
          >
            SQ
          </button>
          <button
            className={`text-center w-full p-1.5 sm:p-2 ${
              i18n.language === "en"
                ? " text-purple-700 font-bold"
                : "text-gray-600"
            }`}
            onClick={() => changeLanguage("en")}
          >
            EN
          </button>
        </div>
      </div>
    </div>
  );
}
