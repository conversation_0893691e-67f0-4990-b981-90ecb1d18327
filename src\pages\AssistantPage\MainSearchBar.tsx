import React, { useRef, useEffect } from "react";
import { Send } from "lucide-react";
import { LoadingSpinner } from "../../components/ui/loading-spinner";
import { useTranslation } from "react-i18next";

interface MainSearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyDown: (e: React.KeyboardEvent) => void;
  isTyping?: boolean;
  placeholder?: string;
}

export const MainSearchBar: React.FC<MainSearchBarProps> = ({
  value,
  onChange,
  onSend,
  onKeyDown,
  isTyping = false,
  placeholder,
}) => {
  const { t } = useTranslation();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-focus the textarea when component mounts
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  // Auto-resize textarea as user types
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "56px";
      textareaRef.current.style.height = `${Math.min(
        textareaRef.current.scrollHeight,
        200
      )}px`;
    }
  }, [value]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isTyping && e.key === "Enter") {
      e.preventDefault();
      return;
    }

    onKeyDown(e);
  };

  return (
    <div className="relative flex items-end bg-white rounded-lg border border-gray-300 shadow-sm focus-within:ring-2 focus-within:ring-purple-500 focus-within:border-purple-500">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onKeyDown={handleKeyDown}
        placeholder={placeholder || t("assistant.inputPlaceholder")}
        disabled={isTyping}
        className="flex-1 py-3 pl-4 pr-12 text-gray-900 resize-none max-h-[200px] focus:outline-none"
        rows={1}
      />
      <button
        onClick={onSend}
        disabled={!value.trim() || isTyping}
        className={`absolute right-3 bottom-3 p-1.5 rounded-full flex items-center justify-center w-8 h-8 ${
          !value.trim() || isTyping
            ? "text-gray-400 bg-gray-100"
            : "text-white bg-purple-600 hover:bg-purple-700"
        } transition-colors`}
      >
        {isTyping ? (
          <div className="flex items-center justify-center">
            <LoadingSpinner
              size="tiny"
              className="text-gray-400"
              showIcons={false}
              text=""
            />
          </div>
        ) : (
          <Send size={20} />
        )}
      </button>
    </div>
  );
};
