import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Search,
  Filter,
  Plus,
  EyeIcon,
  Pencil,
  Phone,
  Mail,
  User,
  Calendar,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { useMemo, useState, useEffect } from "react";
import { useTypedAuthUser } from "../../hooks/useAuth";
import {
  Student,
  StudentsData,
  getAllStudents,
  getStudentsForTeacher,
  teacherAssignments,
  studentsData,
} from "./StudentsMockData";

// Role-based data and UI configuration
interface RoleConfig {
  canAddStudents: boolean;
  canEditStudents: boolean;
  canViewAllGrades: boolean;
  showGradeSection: boolean;
  pageTitle: string;
  pageDescription: string;
}

const getRoleConfig = (role: string): RoleConfig => {
  switch (role) {
    case "admin":
      return {
        canAddStudents: true,
        canEditStudents: true,
        canViewAllGrades: true,
        showGradeSection: true,
        pageTitle: "manageStudents.title",
        pageDescription: "manageStudents.description",
      };
    case "teacher":
      return {
        canAddStudents: false,
        canEditStudents: true,
        canViewAllGrades: false,
        showGradeSection: true,
        pageTitle: "manageStudents.title",
        pageDescription: "manageStudents.description",
      };
    default:
      return {
        canAddStudents: false,
        canEditStudents: false,
        canViewAllGrades: false,
        showGradeSection: false,
        pageTitle: "manageStudents.title",
        pageDescription: "manageStudents.description",
      };
  }
};

export default function StudentsPage() {
  const { t } = useTranslation();
  const authUser = useTypedAuthUser();
  const [query, setQuery] = useState("");
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [isTablet, setIsTablet] = useState(
    window.innerWidth >= 768 && window.innerWidth < 1024
  );

  // Normalize role to lowercase for consistent comparison
  const normalizedRole = authUser?.role?.trim().toLowerCase();
  const roleConfig = getRoleConfig(normalizedRole || "");

  // Handle responsive breakpoints
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      setIsTablet(window.innerWidth >= 768 && window.innerWidth < 1024);
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Get role-based student data
  const roleBasedStudents = useMemo(() => {
    if (!authUser) return [];

    if (normalizedRole === "admin") {
      return getAllStudents();
    } else if (normalizedRole === "teacher") {
      return getStudentsForTeacher("TEACH001");
    }

    return [];
  }, [authUser, normalizedRole]);

  // Get role-based grades data for tabs
  const roleBasedGradesData = useMemo(() => {
    if (normalizedRole === "admin") {
      return studentsData;
    } else if (normalizedRole === "teacher") {
      // For teachers, only show their assigned grades
      const assignment = teacherAssignments.find(
        (t) => t.teacherId === "TEACH001"
      );
      if (!assignment) return {};

      const teacherGradesData: StudentsData = {};
      assignment.assignedGrades.forEach((grade) => {
        const assignedSections = assignment.assignedSections[grade] || [];
        teacherGradesData[grade] = {};

        assignedSections.forEach((section) => {
          if (studentsData[grade] && studentsData[grade][section]) {
            teacherGradesData[grade][section] = studentsData[grade][section];
          }
        });
      });

      return teacherGradesData;
    }

    return {};
  }, [normalizedRole]);

  // Filter students by query
  const filteredStudents = useMemo(() => {
    if (!query) return roleBasedStudents;
    const lowerQuery = query.toLowerCase();
    return roleBasedStudents.filter(
      ({ name, id, parent }) =>
        name.toLowerCase().includes(lowerQuery) ||
        id.toLowerCase().includes(lowerQuery) ||
        parent.toLowerCase().includes(lowerQuery)
    );
  }, [query, roleBasedStudents]);

  // Calculate statistics
  const stats = useMemo(() => {
    const totalStudents = roleBasedStudents.length;
    const activeStudents = roleBasedStudents.filter(
      (s) => s.status === "active"
    ).length;

    let totalClasses = 0;
    let totalSections = 0;

    if (normalizedRole === "admin") {
      totalClasses = Object.keys(studentsData).length;
      totalSections = Object.values(studentsData).reduce(
        (acc, sections) => acc + Object.keys(sections).length,
        0
      );
    } else if (normalizedRole === "teacher") {
      const assignment = teacherAssignments.find(
        (t) => t.teacherId === "TEACH001"
      );
      if (assignment) {
        totalClasses = assignment.assignedGrades.length;
        totalSections = Object.values(assignment.assignedSections).reduce(
          (acc, sections) => acc + sections.length,
          0
        );
      }
    }

    return {
      totalStudents,
      activeStudents,
      totalClasses,
      totalSections,
    };
  }, [roleBasedStudents, normalizedRole]);

  // Check if user has valid role
  if (!normalizedRole || !["teacher", "admin"].includes(normalizedRole)) {
    return (
      <div className="p-6 text-center">
        <h2 className="text-xl font-semibold text-red-600 mb-2">
          Invalid Role
        </h2>
        <p className="text-gray-600">
          Your account role "{authUser?.role}" is not recognized. Only teachers
          and admins can access this page.
        </p>
      </div>
    );
  }

  // StudentCard component for mobile/tablet view
  const StudentCard = ({
    student,
    showGradeSection = false,
  }: {
    student: Student;
    showGradeSection?: boolean;
  }) => (
    <div className="bg-white border border-gray-300 rounded-lg p-4 mb-4 shadow-sm">
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <User className="w-4 h-4 text-gray-500" />
            <h3 className="font-semibold text-lg">{student.name}</h3>
            <Badge
              variant={student.status === "active" ? "default" : "secondary"}
              className={
                student.status === "active"
                  ? "bg-green-100 text-green-600 hover:bg-green-100 ml-auto sm:ml-0"
                  : "bg-red-100 text-red-600 hover:bg-red-100 ml-auto sm:ml-0"
              }
            >
              {student.status}
            </Badge>
          </div>

          <div className="space-y-2 text-sm">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-600">ID:</span>
              <span>{student.id}</span>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-gray-600">
                {t("manageStudents.dateOfBirth")}:
              </span>
              <span>{student.dob}</span>
            </div>

            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-gray-600">
                {t("manageStudents.parent/guardian")}:
              </span>
              <span>{student.parent}</span>
            </div>

            <div className="flex items-center gap-2">
              <Phone className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-gray-600">
                {t("manageStudents.phone")}:
              </span>
              <span>{student.phone}</span>
            </div>

            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-gray-500" />
              <span className="font-medium text-gray-600">
                {t("manageStudents.email")}:
              </span>
              <span className="truncate">{student.email}</span>
            </div>

            {showGradeSection && (
              <>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600">
                    {t("manageStudents.grade")}:
                  </span>
                  <span>{student.grade.replace("grade", "")}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="font-medium text-gray-600">
                    {t("manageStudents.section")}:
                  </span>
                  <span>{student.section}</span>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="flex gap-2 sm:flex-col sm:gap-1">
          <Button variant="ghost" size="sm" className="flex-1 sm:flex-none">
            <EyeIcon className="w-4 h-4 mr-2" />
            <span className="sm:hidden">View</span>
          </Button>
          {roleConfig.canEditStudents && (
            <Button variant="ghost" size="sm" className="flex-1 sm:flex-none">
              <Pencil className="w-4 h-4 mr-2" />
              <span className="sm:hidden">Edit</span>
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  // Render students function - responsive table/cards
  const renderStudents = (
    students: Student[],
    sectionName: string,
    showGradeSection = false
  ) => (
    <div className="border border-gray-300 rounded-md p-4 mb-6">
      <div className="text-lg font-semibold mb-4">{sectionName}</div>

      {/* Mobile/Tablet Card View */}
      {isMobile || isTablet ? (
        <div className="space-y-4">
          {students.map((student, idx) => (
            <StudentCard
              key={idx}
              student={student}
              showGradeSection={showGradeSection}
            />
          ))}
        </div>
      ) : (
        /* Desktop Table View */
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm text-left">
            <thead className="bg-gray-100">
              <tr className="text-gray-400 ">
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.studentId")}
                </th>
                <th className="px-4 py-2 font-normal">
                  {`${t("manageStudents.firstName")} ${t(
                    "manageStudents.lastName"
                  )}`}
                </th>
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.dateOfBirth")}
                </th>
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.parent/guardian")}
                </th>
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.phone")}
                </th>
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.email")}
                </th>
                {showGradeSection && (
                  <>
                    <th className="px-4 py-2 font-normal">
                      {t("manageStudents.grade")}
                    </th>
                    <th className="px-4 py-2 font-normal">
                      {t("manageStudents.section")}
                    </th>
                  </>
                )}
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.status")}
                </th>
                <th className="px-4 py-2 font-normal">
                  {t("manageStudents.actions")}
                </th>
              </tr>
            </thead>
            <tbody>
              {students.map((student, idx) => (
                <tr key={idx} className="border-t">
                  <td className="px-4 py-2 ">{student.id}</td>
                  <td className="px-4 py-2 ">{student.name}</td>
                  <td className="px-4 py-2 ">{student.dob}</td>
                  <td className="px-4 py-2 ">{student.parent}</td>
                  <td className="px-4 py-2 ">{student.phone}</td>
                  <td className="px-4 py-2 ">{student.email}</td>
                  {showGradeSection && (
                    <>
                      <td className="px-4 py-2 ">
                        {student.grade.replace("grade", "")}
                      </td>
                      <td className="px-4 py-2 ">{student.section}</td>
                    </>
                  )}
                  <td className="px-4 py-2 ">
                    <Badge
                      variant={
                        student.status === "active" ? "default" : "secondary"
                      }
                      className={
                        student.status === "active"
                          ? "bg-green-100 text-green-600 hover:bg-green-100"
                          : "bg-red-100 text-red-600 hover:bg-red-100"
                      }
                    >
                      {student.status}
                    </Badge>
                  </td>
                  <td className="px-4 py-2 ">
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <EyeIcon className="w-4 h-4" />
                      </Button>
                      {roleConfig.canEditStudents && (
                        <Button variant="ghost" size="sm">
                          <Pencil className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );

  return (
    <div className="w-full">
      {/* Header Section */}
      <div
        className={`flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 ${
          normalizedRole === "admin"
            ? "gap-3 sm:gap-4 lg:gap-6 mb-4 sm:mb-6"
            : ""
        }`}
      >
        <div className={normalizedRole === "admin" ? "flex-1" : ""}>
          <h2
            className={`font-bold ${
              normalizedRole === "admin"
                ? "text-xl sm:text-2xl lg:text-3xl xl:text-4xl leading-tight"
                : "text-2xl sm:text-3xl"
            }`}
          >
            {t(roleConfig.pageTitle)}
          </h2>
          <p
            className={`text-gray-500 ${
              normalizedRole === "admin"
                ? "text-sm sm:text-base lg:text-lg mt-1"
                : ""
            }`}
          >
            {t(roleConfig.pageDescription)}
          </p>
        </div>

        {/* Action Buttons */}
        <div
          className={`flex items-stretch gap-2 ${
            normalizedRole === "admin"
              ? "flex-col xs:flex-row sm:flex-row xs:items-center sm:gap-3 flex-shrink-0"
              : "flex-col sm:flex-row sm:items-center"
          }`}
        >
          <Button
            variant="outline"
            size="sm"
            className={`cursor-pointer ${
              normalizedRole === "admin"
                ? "text-xs sm:text-sm px-3 sm:px-4 py-2 h-8 sm:h-9"
                : ""
            }`}
          >
            <Filter
              className={`mr-2 ${
                normalizedRole === "admin"
                  ? "w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2"
                  : "w-4 h-4"
              }`}
            />
            <span
              className={normalizedRole === "admin" ? "hidden xs:inline" : ""}
            >
              {t("manageStudents.filter")}
            </span>
            {normalizedRole === "admin" && (
              <span className="xs:hidden">Filter</span>
            )}
          </Button>

          {roleConfig.canAddStudents && (
            <Button
              size="sm"
              className={`bg-purple-600 hover:bg-purple-700 cursor-pointer ${
                normalizedRole === "admin"
                  ? "text-xs sm:text-sm px-3 sm:px-4 py-2 h-8 sm:h-9"
                  : ""
              }`}
            >
              <Plus
                className={`mr-2 ${
                  normalizedRole === "admin"
                    ? "w-3 h-3 sm:w-4 sm:h-4 mr-1.5 sm:mr-2"
                    : "w-4 h-4"
                }`}
              />
              <span
                className={normalizedRole === "admin" ? "hidden xs:inline" : ""}
              >
                {t("manageStudents.addStudent")}
              </span>
              {normalizedRole === "admin" && (
                <span className="xs:hidden">
                  {t("manageStudents.addStudent")}
                </span>
              )}
            </Button>
          )}

          {!roleConfig.canAddStudents && (
            <Button
              size="sm"
              className="bg-purple-600 hover:bg-purple-700 cursor-pointer"
              disabled={true}
            >
              <Plus className="w-4 h-4 mr-2" />
              {t("manageStudents.addStudent")}
            </Button>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div
        className={`mb-4 p-2 rounded-md bg-white border border-gray-300 ${
          normalizedRole === "admin" ? "sm:mb-6 p-1.5 sm:p-2" : ""
        }`}
      >
        <div
          className={`w-full bg-white border rounded-md flex items-center gap-2 px-4 py-2 ${
            normalizedRole === "admin" ? "sm:gap-3 px-3 sm:px-4 sm:py-2.5" : ""
          }`}
        >
          <Search
            className={`text-gray-400 flex-shrink-0 ${
              normalizedRole === "admin" ? "w-4 h-4 sm:w-5 sm:h-5" : "w-5 h-5"
            }`}
          />
          <input
            type="text"
            placeholder={t("manageStudents.searchPlaceholder")}
            className={`w-full bg-transparent outline-none ${
              normalizedRole === "admin"
                ? "text-sm sm:text-base placeholder:text-xs sm:placeholder:text-sm"
                : ""
            }`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Statistics Cards */}
      <div
        className={`mb-6 grid grid-cols-2 gap-3 ${
          normalizedRole === "admin"
            ? "sm:grid-cols-2 md:grid-cols-4 gap-2 sm:gap-3 lg:gap-4"
            : "sm:grid-cols-4 sm:gap-4"
        }`}
      >
        <div
          className={`p-3 rounded-md bg-white border border-gray-300 flex flex-col justify-between ${
            normalizedRole === "admin"
              ? "sm:p-4 lg:p-5 min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]"
              : "sm:p-4"
          }`}
        >
          <div
            className={`font-bold text-blue-500 ${
              normalizedRole === "admin"
                ? "text-lg sm:text-xl lg:text-2xl xl:text-3xl"
                : "text-xl sm:text-2xl"
            }`}
          >
            {stats.totalStudents}
          </div>
          <div
            className={`text-muted-foreground ${
              normalizedRole === "admin"
                ? "text-xs sm:text-sm lg:text-base leading-tight"
                : "text-xs sm:text-sm"
            }`}
          >
            {t("manageStudents.totalStudents")}
          </div>
        </div>

        <div
          className={`p-3 rounded-md bg-white border border-gray-300 flex flex-col justify-between ${
            normalizedRole === "admin"
              ? "sm:p-4 lg:p-5 min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]"
              : "sm:p-4"
          }`}
        >
          <div
            className={`font-bold text-green-500 ${
              normalizedRole === "admin"
                ? "text-lg sm:text-xl lg:text-2xl xl:text-3xl"
                : "text-xl sm:text-2xl"
            }`}
          >
            {stats.activeStudents}
          </div>
          <div
            className={`text-muted-foreground ${
              normalizedRole === "admin"
                ? "text-xs sm:text-sm lg:text-base leading-tight"
                : "text-xs sm:text-sm"
            }`}
          >
            {t("manageStudents.activeStudents")}
          </div>
        </div>

        <div
          className={`p-3 rounded-md bg-white border border-gray-300 flex flex-col justify-between ${
            normalizedRole === "admin"
              ? "sm:p-4 lg:p-5 min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]"
              : "sm:p-4"
          }`}
        >
          <div
            className={`font-bold text-purple-500 ${
              normalizedRole === "admin"
                ? "text-lg sm:text-xl lg:text-2xl xl:text-3xl"
                : "text-xl sm:text-2xl"
            }`}
          >
            {stats.totalClasses}
          </div>
          <div
            className={`text-muted-foreground ${
              normalizedRole === "admin"
                ? "text-xs sm:text-sm lg:text-base leading-tight"
                : "text-xs sm:text-sm"
            }`}
          >
            {t("manageStudents.totalClasses")}
          </div>
        </div>

        <div
          className={`p-3 rounded-md bg-white border border-gray-300 flex flex-col justify-between ${
            normalizedRole === "admin"
              ? "sm:p-4 lg:p-5 min-h-[80px] sm:min-h-[90px] lg:min-h-[100px]"
              : "sm:p-4"
          }`}
        >
          <div
            className={`font-bold text-red-500 ${
              normalizedRole === "admin"
                ? "text-lg sm:text-xl lg:text-2xl xl:text-3xl"
                : "text-xl sm:text-2xl"
            }`}
          >
            {stats.totalSections}
          </div>
          <div
            className={`text-muted-foreground ${
              normalizedRole === "admin"
                ? "text-xs sm:text-sm lg:text-base leading-tight"
                : "text-xs sm:text-sm"
            }`}
          >
            {t("manageStudents.totalSections")}
          </div>
        </div>
      </div>

      {/* Students Display */}
      {query ? (
        filteredStudents.length > 0 ? (
          renderStudents(
            filteredStudents,
            t("manageStudents.searchResults"),
            roleConfig.showGradeSection
          )
        ) : (
          <div className="flex items-center justify-center h-64">
            <p className="text-center text-muted-foreground">
              {t("manageStudents.noStudentsFound")}
            </p>
          </div>
        )
      ) : (
        <Tabs
          defaultValue={Object.keys(roleBasedGradesData)[0] || "grade1"}
          className="w-full"
        >
          {/* Responsive Tab Layout - Admin Style */}
          {normalizedRole === "admin" && (
            <>
              {/* Mobile Layout */}
              <div className="block sm:hidden bg-amber-500">
                <div className="relative mb-4">
                  <div className="overflow-x-auto scrollbar-hide pb-1">
                    <TabsList className="bg-muted inline-flex h-9 items-center justify-start rounded-lg p-1 w-max min-w-full gap-0.5">
                      {Object.keys(roleBasedGradesData).map((gradeKey) => (
                        <TabsTrigger
                          key={gradeKey}
                          value={gradeKey}
                          className="flex-shrink-0 px-2.5 py-1.5 text-xs font-medium whitespace-nowrap min-w-[70px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
                        >
                          {`${t("setup.grade")} ${gradeKey.replace(
                            "grade",
                            ""
                          )}`}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </div>
                  <div className="absolute left-0 top-0 bottom-0 w-3 bg-gradient-to-r from-gray-50 to-transparent pointer-events-none z-10"></div>
                  <div className="absolute right-0 top-0 bottom-0 w-3 bg-gradient-to-l from-gray-50 to-transparent pointer-events-none z-10"></div>
                </div>
              </div>

              {/* Small screens (640px - 768px) */}
              <div className="hidden sm:block md:hidden ">
                <div className="relative mb-4">
                  <div className="overflow-x-auto scrollbar-hide pb-1">
                    <TabsList className="bg-muted inline-flex   gap-2 p-2 min-h-[44px]">
                      {Object.keys(roleBasedGradesData).map((gradeKey) => (
                        <TabsTrigger
                          key={gradeKey}
                          value={gradeKey}
                          className="flex-shrink-0 px-4 py-2 text-sm font-medium whitespace-nowrap min-w-[90px] justify-center data-[state=active]:bg-white data-[state=active]:shadow-sm"
                        >
                          {`${t("setup.grade")} ${gradeKey.replace(
                            "grade",
                            ""
                          )}`}
                        </TabsTrigger>
                      ))}
                    </TabsList>
                  </div>
                </div>
                <div className="absolute left-0 top-0 bottom-0 w-3 bg-gradient-to-r from-gray-50 to-transparent pointer-events-none z-10"></div>
                <div className="absolute right-0 top-0 bottom-0 w-3 bg-gradient-to-l from-gray-50 to-transparent pointer-events-none z-10"></div>
              </div>

              {/* Medium screens (768px - 1024px) */}
              <div className="mb-4 relative bg-red-500">
                <div className="overflow-x-auto scrollbar-hide pb-1 px-1 ">
                  <TabsList className="flex gap-2 p-2 bg-muted rounded-md min-h-[44px] w-max">
                    {Object.keys(roleBasedGradesData).map((gradeKey) => (
                      <TabsTrigger
                        key={gradeKey}
                        value={gradeKey}
                        className="flex-shrink-0 min-w-[100px] max-w-[130px] px-4 py-2 text-sm font-medium text-gray-700 rounded-md transition duration-200 whitespace-nowrap justify-center data-[state=active]:bg-white data-[state=active]:shadow"
                      >
                        {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                </div>

                <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-muted to-transparent pointer-events-none z-10 md:block" />
                <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-muted to-transparent pointer-events-none z-10 md:block" />
              </div>
            </>
          )}

          {/* Teacher Style - Simpler Layout */}
          {normalizedRole === "teacher" && (
            <TabsList className="bg-muted w-full justify-center flex-wrap gap-1">
              {Object.keys(roleBasedGradesData).map((gradeKey) => (
                <TabsTrigger
                  key={gradeKey}
                  value={gradeKey}
                  className="min-w-[100px] sm:w-[120px] text-xs sm:text-sm capitalize"
                >
                  {`${t("setup.grade")} ${gradeKey.replace("grade", "")}`}
                </TabsTrigger>
              ))}
            </TabsList>
          )}

          {/* Tab Content */}
          {Object.entries(roleBasedGradesData).map(([gradeKey, sections]) => (
            <TabsContent key={gradeKey} value={gradeKey}>
              {Object.entries(sections).map(([sectionName, students]) =>
                students.length > 0
                  ? renderStudents(
                      students,
                      `${t("setup.grade")} ${gradeKey.replace(
                        "grade",
                        ""
                      )} - ${sectionName}`
                    )
                  : null
              )}

              {Object.values(sections).every(
                (students) => students.length === 0
              ) && (
                <p className="text-center mt-6 text-muted-foreground">
                  {t("manageStudents.noStudentsFound")}
                </p>
              )}
            </TabsContent>
          ))}
        </Tabs>
      )}
    </div>
  );
}
