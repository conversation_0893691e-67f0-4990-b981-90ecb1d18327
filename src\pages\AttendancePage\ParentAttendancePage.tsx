import { useState } from "react";
import AttendanceCard from "../../components/AttendanceCard";
import { useTranslation } from "react-i18next";
import { Search } from "lucide-react";
import WarningManagement from "../../components/WarningManagement/WarningManagement";

export default function ParentAttendancePage() {
  const { t } = useTranslation();
  const [filterStatus, setFilterStatus] = useState("All");
  const [searchTerm, setSearchTerm] = useState("");

  const attendanceData = [
    {
      subject: "Mathematics",
      date: "Mon, Mar 10, 2025",
      reason: "Doctor appointment",
      status: "Excused",
    },
    {
      subject: "Physics",
      date: "Tue, Mar 11, 2025",
      reason: "Missed bus",
      status: "Unexcused",
    },
    {
      subject: "Biology",
      date: "Wed, Mar 12, 2025",
      reason: "Family emergency",
      status: "Pending",
    },
  ];

  const statusOptions = ["All", "Excused", "Unexcused", "Pending"];

  const filteredAttendanceData = attendanceData.filter((item) => {
    const matchesStatus =
      filterStatus === "All" || item.status === filterStatus;
    const matchesSearch = item.subject
      .toLowerCase()
      .includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  return (
    <div className="w-full">
      <div className="bg-white p-4 sm:p-5 border border-gray-300 rounded-md">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl sm:text-3xl font-bold">
            {t("attendance.childsAttendance")}
          </h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6 mb-6">
          <div className="lg:col-span-2">
            {/* Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder={t("attendance.searchBySubject")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="border rounded-md px-4 py-2 w-full pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setFilterStatus("All")}
                  className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                    filterStatus === "All"
                      ? "bg-gray-700 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {t("attendance.status.all")}
                </button>
                <button
                  onClick={() => setFilterStatus("Excused")}
                  className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                    filterStatus === "Excused"
                      ? "bg-gray-700 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {t("attendance.status.excused")}
                  {filterStatus !== "Excused" && (
                    <span className="ml-1">
                      (
                      {
                        attendanceData.filter(
                          (item) => item.status === "Excused"
                        ).length
                      }
                      )
                    </span>
                  )}
                </button>
                <button
                  onClick={() => setFilterStatus("Unexcused")}
                  className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                    filterStatus === "Unexcused"
                      ? "bg-gray-700 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {t("attendance.status.unexcused")}
                  {filterStatus !== "Unexcused" && (
                    <span className="ml-1">
                      (
                      {
                        attendanceData.filter(
                          (item) => item.status === "Unexcused"
                        ).length
                      }
                      )
                    </span>
                  )}
                </button>
                <button
                  onClick={() => setFilterStatus("Pending")}
                  className={`px-4 py-1.5 rounded-md text-sm font-medium ${
                    filterStatus === "Pending"
                      ? "bg-gray-700 text-white"
                      : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {t("attendance.status.pending")}
                  {filterStatus !== "Pending" && (
                    <span className="ml-1">
                      (
                      {
                        attendanceData.filter(
                          (item) => item.status === "Pending"
                        ).length
                      }
                      )
                    </span>
                  )}
                </button>
              </div>
            </div>

            {/* Attendance Cards */}
            <div className="space-y-2 sm:space-y-4">
              {filteredAttendanceData.map((item, index) => (
                <AttendanceCard
                  key={index}
                  subject={item.subject}
                  date={item.date}
                  reason={item.reason}
                  status={item.status}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
      {/* Child's Warnings Section - Read-only */}
      <div className="lg:col-span-1">
        <WarningManagement
          readOnly={true}
          studentId="123" // In a real app, this would be the child's ID
          title={t("warnings.childsWarnings")}
          description={t("warnings.parentDescription")}
        />
      </div>
    </div>
  );
}
