import { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { AlertTriangle, Filter, Plus, Trash2, Save } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import toast from "react-hot-toast";
import { validateRequired } from "../../lib/validation";

interface Warning {
  id: string;
  text: string;
  type: string;
  date: string;
  teacher: string;
  subject: string;
}

interface WarningManagementProps {
  readOnly?: boolean;
  studentId?: string;
  title?: string;
  description?: string;
}

const warningTypes = ["Behavior", "Academic", "Attendance", "Other"];

export default function WarningManagement({
  readOnly = false,
  studentId,
  title,
  description,
}: WarningManagementProps) {
  const { t } = useTranslation();
  const [warnings, setWarnings] = useState<Warning[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddingWarning, setIsAddingWarning] = useState(false);
  const [warningText, setWarningText] = useState("");
  const [warningType, setWarningType] = useState("");
  const [filterType, setFilterType] = useState("All");
  const [subject, setSubject] = useState("");
  const [validationErrors, setValidationErrors] = useState({
    warningText: false,
    warningType: false,
  });
  const [isMobile, setIsMobile] = useState(window.innerWidth < 640);
  const [isTablet, setIsTablet] = useState(
    window.innerWidth >= 640 && window.innerWidth < 1024
  );

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
      setIsTablet(window.innerWidth >= 640 && window.innerWidth < 1024);
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial sizing

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    fetchWarnings();
  }, [studentId]);

  const fetchWarnings = async () => {
    try {
      setLoading(true);
      // If studentId is provided, fetch warnings for that specific student
      const endpoint = studentId
        ? `${
            import.meta.env.VITE_EDITAR_BASE_API_URL
          }/warnings?studentId=${studentId}`
        : `${import.meta.env.VITE_EDITAR_BASE_API_URL}/warnings`;

      const response = await fetch(endpoint);
      if (response.ok) {
        const data = await response.json();
        setWarnings(data);
      } else {
        console.error("Failed to fetch warnings");
        // For demo purposes, add mock data if API fails
        setWarnings([
          {
            id: "1",
            text: "Disrupted class multiple times",
            type: "Behavior",
            date: new Date().toISOString(),
            teacher: "Mr. Johnson",
            subject: "Mathematics",
          },
          {
            id: "2",
            text: "Failed to submit assignment",
            type: "Academic",
            date: new Date(Date.now() - 86400000).toISOString(),
            teacher: "Ms. Garcia",
            subject: "Physics",
          },
          {
            id: "3",
            text: "Late to class three times this month",
            type: "Attendance",
            date: new Date(Date.now() - 172800000).toISOString(),
            teacher: "Mrs. Smith",
            subject: "English",
          },
        ]);
      }
    } catch (error) {
      console.error("Error fetching warnings:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddWarning = () => {
    setIsAddingWarning(true);
    setWarningText("");
    setWarningType("");
    setSubject("");
    clearValidationErrors();
  };

  const handleCancelAdd = () => {
    setIsAddingWarning(false);
    clearValidationErrors();
  };

  const clearValidationErrors = () => {
    setValidationErrors({
      warningText: false,
      warningType: false,
    });
  };

  const setValidationError = (field: string, hasError: boolean) => {
    setValidationErrors((prev) => ({
      ...prev,
      [field]: hasError,
    }));
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { value } = e.target;
    setWarningText(value);

    //
    if (value.trim() !== "") {
      setValidationError("warningText", false);
    }
  };

  const handleTypeChange = (value: string) => {
    setWarningType(value);

    // Clear validation error when user selects a type
    if (value.trim() !== "") {
      setValidationError("warningType", false);
    }
  };

  const handleSubmit = async () => {
    clearValidationErrors();
    let isValid = true;

    // Validate form data
    if (!validateRequired(warningText)) {
      setValidationError("warningText", true);
      isValid = false;
    }

    if (!validateRequired(warningType)) {
      setValidationError("warningType", true);
      isValid = false;
    }

    if (!isValid) return;

    try {
      const newWarning = {
        text: warningText,
        type: warningType,
        date: new Date().toISOString(),
        teacher: "Current Teacher", // In a real app, get from auth context
        subject: subject || "General",
        studentId: studentId, // Include the studentId if provided
      };

      const response = await fetch(
        `${import.meta.env.VITE_EDITAR_BASE_API_URL}/warnings`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(newWarning),
        }
      );

      if (response.ok) {
        toast.success(t("warnings.addSuccess"));
        setWarningText("");
        setWarningType("");
        setSubject("");
        setIsAddingWarning(false);
        fetchWarnings(); // Refresh the list
      } else {
        toast.error(t("warnings.addError"));
      }
    } catch (error) {
      console.error("Error adding warning:", error);
      toast.error(t("warnings.addError"));

      // For demo purposes, add the warning locally if API fails
      setWarnings([
        {
          id: Date.now().toString(),
          text: warningText,
          type: warningType,
          date: new Date().toISOString(),
          teacher: "Current Teacher",
          subject: subject || "General",
        },
        ...warnings,
      ]);

      setWarningText("");
      setWarningType("");
      setSubject("");
      setIsAddingWarning(false);
    }
  };

  const handleDeleteWarning = (id: string) => {
    // In a real app, you would call the API to delete the warning
    setWarnings(warnings.filter((warning) => warning.id !== id));
    toast.success(
      t("warnings.deleteSuccess") || "Warning removed successfully"
    );
  };

  const filteredWarnings =
    filterType === "All"
      ? warnings
      : warnings.filter((warning) => warning.type === filterType);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div
      className={`bg-white rounded-md border border-gray-300 p-3 sm:p-4 md:p-6 my-4 sm:my-6 md:my-10`}
    >
      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2 sm:gap-0 mb-4 sm:mb-6">
        <h2
          className={`text-lg sm:text-xl font-semibold flex items-center text-gray-800 ${
            isMobile ? "mb-2" : ""
          }`}
        >
          <AlertTriangle className="h-4 w-4 sm:h-5 sm:w-5 text-purple-600 mr-2" />
          {title || t("warnings.title")}
        </h2>
        {!readOnly && !isAddingWarning && (
          <Button
            onClick={handleAddWarning}
            className="bg-purple-600 hover:bg-purple-700 cursor-pointer text-sm sm:text-base w-full sm:w-auto"
          >
            <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />{" "}
            {t("warnings.addNew")}
          </Button>
        )}
      </div>

      {description && (
        <p className="text-sm sm:text-base mb-4">{description}</p>
      )}

      {!readOnly && isAddingWarning && (
        <Card className="mb-4 sm:mb-6 border-purple-200 shadow-sm">
          <CardContent className={`pt-4 sm:pt-6 ${isMobile ? "px-3" : ""}`}>
            <h3 className="text-base sm:text-lg font-medium mb-3 sm:mb-4">
              {t("warnings.addNew")}
            </h3>
            <div className="grid gap-3 sm:gap-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <label
                    htmlFor="warningType"
                    className="text-xs sm:text-sm font-medium mb-1 block"
                  >
                    {t("warnings.type")}*
                  </label>
                  <Select value={warningType} onValueChange={handleTypeChange}>
                    <SelectTrigger
                      id="warningType"
                      className={`w-full text-xs sm:text-sm ${
                        validationErrors.warningType
                          ? "border-red-500 focus-visible:ring-red-500"
                          : ""
                      }`}
                    >
                      <SelectValue placeholder={t("warnings.selectType")} />
                    </SelectTrigger>
                    <SelectContent>
                      {warningTypes.map((type) => (
                        <SelectItem
                          key={type}
                          value={type}
                          className="text-xs sm:text-sm"
                        >
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.warningType && (
                    <p className="text-red-500 text-xs mt-1">
                      {t("warnings.typeRequired")}
                    </p>
                  )}
                </div>
                <div>
                  <label
                    htmlFor="subject"
                    className="text-xs sm:text-sm font-medium mb-1 block"
                  >
                    {t("warnings.subject")} ({t("setup.optional") || "optional"}
                    )
                  </label>
                  <Input
                    id="subject"
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder={t("warnings.subjectPlaceholder")}
                    className="text-xs sm:text-sm"
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="warningText"
                  className="text-xs sm:text-sm font-medium mb-1 block"
                >
                  {t("warnings.text")}*
                </label>
                <Textarea
                  id="warningText"
                  value={warningText}
                  onChange={handleTextChange}
                  placeholder={t("warnings.textPlaceholder")}
                  rows={isMobile ? 2 : 3}
                  className={`text-xs sm:text-sm ${
                    validationErrors.warningText
                      ? "border-red-500 focus-visible:ring-red-500"
                      : ""
                  }`}
                />
                {validationErrors.warningText && (
                  <p className="text-red-500 text-xs mt-1">
                    {t("warnings.textRequired")}
                  </p>
                )}
              </div>
              <div className="flex justify-end gap-2 mt-2 sm:mt-4">
                <Button
                  variant="outline"
                  onClick={handleCancelAdd}
                  className="text-xs sm:text-sm py-1 h-8 sm:h-10"
                >
                  {t("setup.cancel") || "Cancel"}
                </Button>
                <Button
                  onClick={handleSubmit}
                  className="bg-purple-600 hover:bg-purple-700 text-xs sm:text-sm py-1 h-8 sm:h-10"
                >
                  <Save className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  {t("warnings.submit")}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-2 sm:gap-0 mb-3 sm:mb-4">
        <h3 className="text-sm sm:text-md font-semibold">
          {t("warnings.list")}
        </h3>

        <div className="flex items-center gap-1 sm:gap-2">
          <Filter className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500" />
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-[120px] sm:w-[150px] cursor-pointer text-xs sm:text-sm h-8 sm:h-10">
              <SelectValue placeholder={t("warnings.filter")} />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All" className="text-xs sm:text-sm">
                {t("warnings.all")}
              </SelectItem>
              {warningTypes.map((type) => (
                <SelectItem
                  key={type}
                  value={type}
                  className="text-xs sm:text-sm"
                >
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {loading ? (
        <div className="border rounded-md p-4 sm:p-8 text-center text-gray-500 text-sm sm:text-base">
          Loading warnings...
        </div>
      ) : filteredWarnings.length === 0 ? (
        <div className="border rounded-md p-4 sm:p-8 text-center text-gray-500 text-sm sm:text-base">
          {t("warnings.noWarnings")}
        </div>
      ) : (
        <div className="overflow-x-auto -mx-3 sm:mx-0">
          <table className="min-w-full divide-y divide-gray-200 text-xs sm:text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("warnings.subject")}
                </th>
                <th className="px-2 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("warnings.text")}
                </th>
                <th className="px-2 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("warnings.type")}
                </th>
                <th className="px-2 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t("warnings.date")}
                </th>
                {!readOnly && (
                  <th className="px-2 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t("warnings.action")}
                  </th>
                )}
                {readOnly && (
                  <th className="px-2 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {t("warnings.teacher")}
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredWarnings.map((warning) => (
                <tr key={warning.id}>
                  <td className="px-2 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                    {warning.subject}
                  </td>
                  <td className="px-2 sm:px-6 py-2 sm:py-4 text-xs sm:text-sm text-gray-500 max-w-[100px] sm:max-w-xs truncate">
                    {warning.text}
                  </td>
                  <td className="px-2 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm">
                    <span
                      className={`px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs ${
                        warning.type === "Behavior"
                          ? "bg-red-100 text-red-800"
                          : warning.type === "Academic"
                          ? "bg-yellow-100 text-yellow-800"
                          : warning.type === "Attendance"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {warning.type}
                    </span>
                  </td>
                  <td className="px-2 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                    {formatDate(warning.date)}
                  </td>
                  {!readOnly && (
                    <td className="px-2 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteWarning(warning.id)}
                        className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-6 w-6 sm:h-8 sm:w-8"
                      >
                        <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      </Button>
                    </td>
                  )}
                  {readOnly && (
                    <td className="px-2 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                      {warning.teacher}
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
